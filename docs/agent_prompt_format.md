# Prompt Authoring Guide

A standard for writing high-quality prompts across this codebase.
This guide enforces a consistent structure with explicit sections such as Input, Tools, Process, Artifact Schema, Artifact, and Output Schema.
It ensures prompts are reusable, consistent, and compatible in multi-agent workflows.

---

## Why This Guide
- Consistent authoring across prompts and teams.
- Clear expectations for AI output and structure.
- Reusable, composable tasks with explicit constraints.
- Prevents ambiguity and enforces measurable outcomes.

---

## Core Principles
- Output MUST strictly follow the declared schema.
- Steps MUST be written in ALL CAPS TITLES with strong keywords (MUST, REQUIRED, CRITICAL).
- Keep outputs concise, skimmable, and actionable.
- Favor specificity over generalities; avoid buzzwords and platitudes.
- Use square brackets [Element Name] to reference schema elements, sections, or concepts defined elsewhere.
- Use single quotes 'literal value' only for literal values, examples, or direct text that should appear as-is.

---

## Standard Prompt Structure

### 1) How to Respond to This Prompt
- The model MUST execute the Prompt exactly as described.
- The model MUST generate an artifact following the Artifact Schema.
- The artifact MUST be saved using available tools, NOT returned directly.
- The model MUST return only the Output Schema as the final response.
- **CRITICAL:** No extra explanations outside of the schema or required output.

### 2) Artifact Schema
- Defines the structure of the main artifact the model MUST generate and save.
- This is separate from the execution report (Output Schema).
- Can be JSON, Markdown, text, or another structured format.
- MUST include constraints (required fields, limits, formatting rules).

**Example (JSON):**
```
{
  "purpose": "[Purpose]",
  "features": ["[Feature List]"]
}
```

**Example (Markdown):**
```
# [Report Title]
## [Section Name]
- [Item Description]
```

---

### 3) Inputs
- Define what the model requires as input.
- Input format can be JSON, plain text, or structured fields.
- MUST specify required fields, optional fields, and constraints.


### 4) Output Schema
- Standard structured response every model MUST return.
- This is the execution report, not the main artifact.

**Example:**
```
{
  "agent": "[AgentName]",
  "status": "[success | error]",
  "message": "[Summary of what happened]",
  "steps": {
    "step_name": "[success | failed]"
  },
  "artifacts": [
    {
      "type": "[file | json | text | other]",
      "path": "[optional]",
      "description": "[what was produced]"
    }
  ],
  "error": "[Error message if any]",
  "timestamp": "[2025-08-19T12:50:00Z]"
}
```
### 5) Available Tools
- List all tools accessible to the model.
- MUST describe each tool’s purpose and constraints.

---

### 6) Prompt

#### Role
- Define the role of the model. MUST be precise and tied to the scope.

#### Task
- High-level description of the objective. MUST specify what artifact(s) the model produces and what structured response it returns.

### Process Overview
	•	Give a short, numbered list of the main steps in the process.
	•	Each step = one sentence max, describing the high-level action.
	•	Example style (do not fill with real data):
	1.	[STEP NAME] – [one-line summary of the action].
	2.	[STEP NAME] – [one-line summary of the action].
	3.	[STEP NAME] – [one-line summary of the action].

### Step Details
 	- Break down each step from the overview in detail.
  - Use ALL CAPS TITLES for step headers.
  - Each step must include purpose, required tools, and constraints.
  - Example style (still template, no real data):

    #### STEP 1 – [STEP NAME]
    - Purpose: [describe purpose of this step]
    - Actions: [list the concrete actions the model must take]
    - Constraints: [list restrictions for this step]

    #### STEP 2 – [STEP NAME]
    - Purpose: [describe purpose of this step]
    - Actions: [list the concrete actions the model must take]
    - Constraints: [list restrictions for this step]

    #### STEP 3 – [STEP NAME]
    - Purpose: [describe purpose of this step]
    - Actions: [list the concrete actions the model must take]
    - Constraints: [list restrictions for this step]

- Each step MUST contain linear, action-oriented instructions.
- The Step for generating the artifact MUST explicitly reference the Artifact section before generation. e.g Analyze the [Artifact Guide] before generating the artifact.

---

### Artifact Guide

**Artifact Guidelines**:
- Defines the guidelines for the main artifact the model MUST generate and save.

**Artifact Instructions**
- Key instructions for generating the artifact.

**Artifact Key Elements**
- Essential items that MUST be present in the generated artifact.

**Artifact Requirements**
- Global rules the Artifact Schema MUST follow.
- Example: Artifact Schema MUST be valid JSON before saving.
- **REQUIRED:** All mandatory fields must be present and properly formatted.
- **CRITICAL:** Do not return ‘success’ before confirming file save.

**Artifact Restrictions**
- Explicit prohibitions for the artifact.
- Example:
  - ❌ Do not skip required fields.
  - ❌ Do not generate malformed JSON.
  - ❌ Do not assume success without save confirmation.

---

### Key Elements
- Essential commitments the model MUST ensure (not related to the artifact itself).

### Responsibilities
- Broader behavioral duties of the model during execution.
- Example: Always analyze input before generation. Always validate before saving.

### Restrictions
- Explicit prohibitions on the model’s behavior.
- Example:
  - ❌ Do not add commentary outside schemas.
  - ❌ Do not invent tools.
  - ❌ Do not skip mandatory steps.

---

## Template (Copy-Paste)

### How to Respond to This Prompt
- The model MUST execute the Prompt exactly as described.
- The model MUST generate an artifact following the Artifact Schema.
- The artifact MUST be saved using available tools, NOT returned directly.
- The model MUST return only the Output Schema as the final response.

### Artifact Schema
{ define the structure of the main deliverable here }

**Example (JSON):**
```
{
  "example_field": "[value]"
}
```

### Inputs
{ define required inputs here }

### Available Tools
{ list tools and constraints here }

### Output Schema
{ define execution report schema here }

**Example:**
```
{
  "agent": "[AgentName]",
  "status": "[success | error]"
}
```

### Prompt

#### Role
{ define the model’s role here }

#### Task
{ describe objectives and outputs here }

#### Process
- STEP 1 – ANALYZE INPUT (MUST): analyze and validate the provided input
- STEP 2 – GENERATE ARTIFACT (REQUIRED): reference the Artifact section, then create the artifact following the defined schema
- STEP 3 – SAVE AND VALIDATE (CRITICAL): save the artifact and confirm success

---

### Artifact
- **Artifact Key Elements:** { list essential fields }
- **Artifact Requirements:** { list formatting and validation rules }
- **Artifact Restrictions:** { list prohibitions for the artifact }

---

### Key Elements
{ list essential commitments }

### Responsibilities
{ list execution duties }

### Restrictions
{ list prohibitions for behavior }
```

***