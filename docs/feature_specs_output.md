Feature Specifications Documentation (Updated)

Overview

This document is a validation checklist for feature specifications generated by the src/scripts/featureSpecs/featureSpecs.js script. It defines the required structure, conditions, and rules for generating valid feature specifications.

Purpose
	•	Validation Guide: Ensure AI-generated specifications follow the exact schema
	•	Quality Assurance: Guarantee consistency across feature files
	•	Documentation: Provide developers with a clear schema for reference

⸻

Feature Specification Structure

Root Structure ✅

{
    "name": "[string]",
    "description": "[string]",
    "userExperience": "[array]",
    "screens": "[object]",
    "conditions": "[array]",
    "dataSchemas": "[object]",
    "requests": "[array]"
}


⸻

Required Fields Checklist

1. Feature Object ✅
	•	name: Feature name (string)
	•	description: Feature description (string)
	•	userExperience: Array of user experience objects
	•	screens: Object mapping paths to descriptions
	•	conditions: Array of feature conditions (business rules / restrictions)
	•	dataSchemas: Object defining data structures
	•	requests: Array of API request configurations

⸻

2. User Experience ✅

Each user experience object must contain:
	•	who: "User" or "App"
	•	if: Conditional access logic (or null)
	•	action: Must contain exactly one of:
	•	path (page navigation)
	•	modal (open/close modal)
	•	request (API request trigger)
	•	element (UI interaction)
	•	where: Path of the screen
	•	when: Trigger that caused the action (must mirror the paired action)

⚠ Rules:
	•	Always set "if": null for list views (/posts, /articles, etc.)
	•	Access checks (hasAccess) only allowed on item pages (/posts/:id)
	•	If action.type is "navigate", there must be a corresponding "when" object

⸻

3. Action Configuration ✅
	•	type: Valid type (click, load, send, navigate, redirect, open, close, fill, select, upload, validate, type)
	•	request: API request config (required when type is load or send)
	•	path / modal: Valid route string or modal name (if navigation)
	•	element: UI element (type, eventId)

⚠ Notify must be included in request if the action should trigger a notification.

⸻

Screen Configuration

Screens ✅
	•	Every path in userExperience must appear in screens
	•	Path names are short, pluralized list form (/posts, /audios)
	•	For single entities → /entity/:id format
	•	/workflow/:id used for resource generation tracking
	•	/paywall is always a modal, never a full screen
	•	/chatbot handles chat conversations

⸻

Data Schemas ✅

Schema Structure

{
  "entityName": {
    "canBeCommented": [true/false],
    "usePayment": [true/false],
    "description": "[string]",
    "fields": {
      "fieldName": {
        "type": "[string]",
        "required": [true/false],
        "isUser": [true/false],
        "description": "[string]"
      }
    }
  }
}

Rules
	•	Entity names: lowercase singular nouns (post, comment, audio)
	•	Mandatory top-level props: canBeCommented, usePayment, description, fields
	•	Field rules:
	•	id, name, price required if entity is a payment resource
	•	Use workflowId for generated resources
	•	Never mix title and name (choose one)
	•	For relationships → always use objectId and objectType
	•	No access control fields in schemas

⸻

Request Configuration ✅

Request Structure

{
  "requestId": "[string]",
  "useWorkflow": "[object]", // optional
  "useAi": "[object]", // optional
  "dataSchema": "[string]",
  "type": "[Find/Read/Create/Update/Delete]",
  "params": "[object]",
  "body": "[object]",
  "notifyLink": "[string]" // optional
}

Rules
	•	requestId must start with feature name (listPosts_request_1)
	•	dataSchema must reference an existing schema
	•	type: only Find, Read, Create, Update, Delete
	•	params: only for Read, Update, Delete (identifier field must exist in schema)
	•	body: only for Create, Update, Find (fields must exist in schema, respect required flags)
	•	useWorkflow: single workflow per resource generation (no duplicates)
	•	useAi: if generation/transcription/upscaling is AI-driven
	•	notifyLink: must match an existing path

⸻

Special Processing ✅
	•	Workflow Rules:
	•	Always navigate to /workflow/:id before showing resource details
	•	Only one workflow request per entity
	•	Workflow tasks must follow generate → upscale/edit → store
	•	/workflow/:id requests must be type "Read" only
	•	Payment Rules:
	•	Subscription checks must use useAuthStore.hasSubscription()
	•	Paywall handled only via modal (/paywall)
	•	Credit/one-time/cart checks use hasAccess on single entity pages only
	•	Transcription Rules:
	•	Entity name: "audio"
	•	Paths: /audios, /audios/:id
	•	Workflow: transcribe → store
	•	generationType: [“audio”, “text”]

⸻

Quality Checks ✅
	•	All userExperience flows start from entry screen and end with confirmation/navigation back
	•	Every screen in flows is present in screens
	•	Every request in requests maps to an explicit action in userExperience
	•	No duplicate or unused requests
	•	Data schemas have only required fields, no redundancy
	•	Workflow always returns entity instead of reloading it

⸻
