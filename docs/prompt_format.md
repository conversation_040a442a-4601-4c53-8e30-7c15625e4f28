## Prompt Authoring Guide

A standard for writing high‑quality prompts across this codebase. This guide enforces a consistent structure with explicit sections for Process, Key Elements, Requirements, and Restrictions under each task. It mirrors the format used in specs/prompt/brand/brand.ts.

### Why this guide
- Consistent authoring across prompts and teams
- Clear expectations for AI output and structure
- Reusable, composable tasks with explicit constraints

---

## Core Principles
- Output must strictly follow the declared schema and delimiters
- Keep outputs concise, skimmable, and actionable
- Each task includes the same four subsections: Process, Key Elements, Requirements, Restrictions
- Favor specificity over generalities; avoid buzzwords and platitudes

---

## Standard Prompt Structure

### 1) How to Respond to This Prompt
- Instruct the model to execute the Prompt section to produce the output
- Remind to use the custom delimiter format and provided schema
- Require output to start with ```markdown and end with ```
- Require a single markdown block with correct headings, emphasis, and no extra wrappers

### 2) Output Schema
- Define the exact structure with custom delimiters and headings
- Use the format:
  - +++Section Name---
  - [Content] (constraints)
  - ===DELIMITER===

### 3) Prompt
- Provide the role and task context
- Define numbered tasks (1..N)
- For each task, include:
  - Process: step‑by‑step method the model should follow
  - Key Elements: what must appear in the output
  - Requirements: quality and formatting criteria to enforce
  - Restrictions: what to avoid to reduce ambiguity and risk

---

## Section Definitions

### Process
- A concrete sequence of steps (Step 1, Step 2, …)
- Each step is action‑oriented and unambiguous
- Designed to be followed linearly by the model

### Key Elements
- The essential components that must be present in the output
- Bulleted, each element unambiguously defined

### Requirements
- Must‑dos that enforce quality (clarity, concision, alignment, measurability)
- Can include word limits, structural rules, style constraints

### Restrictions
- Must‑not rules to prevent common failure modes (jargon, vagueness, contradictions)
- Can prohibit brand/product mentions, clichés, or off‑brand tone

---

## Template (Copy‑Paste)

Use this as a starting point for any new prompt. Adjust sections/labels as needed while keeping the structure intact.

```markdown
# How to Respond to This Prompt

To get the output structure, execute the **Prompt** provided below.

The output must:

- Use the custom delimiter format and respect the provided schema.
- Start with ```markdown and end with ```.
- Be a single valid markdown text with no extraneous text or wrappers.
- Highlight key concepts or important information with **bold** or _italic_.
- Avoid long blocks of text.
- Use **headers** to break down the content.
- Apply correct markdown syntax for visual clarity and consistency.

## Output Schema

```markdown
+++Section 1---
[Content for section 1] (constraints)
===DELIMITER===
+++Section 2---
[Content for section 2] (constraints)
===DELIMITER===
... (add more sections as needed)
```

# Prompt

## Role:

[Your role here]

## Task:

[High‑level description of what to produce]

1. **Section 1 Title**:
   [One‑line description of the task]

   - **Process**:
     - **Step 1**: [Action]
     - **Step 2**: [Action]
     - **Step 3**: [Action]

   - **Key Elements**:
     - [Element 1]
     - [Element 2]

   - **Requirements**:
     - [Requirement 1]
     - [Requirement 2]

   - **Restrictions**:
     - [Restriction 1]
     - [Restriction 2]

2. **Section 2 Title**:
   [One‑line description of the task]

   - **Process**:
     - **Step 1**: [Action]
     - **Step 2**: [Action]

   - **Key Elements**:
     - [Element 1]

   - **Requirements**:
     - [Requirement]

   - **Restrictions**:
     - [Restriction]

## Additional Information:

[Inject dynamic variables and context here]
```

---

## Example Alignment (Excerpts)

The following excerpts mirror the exact structure used in the brand prompt so authors can match tone and detail level.

### Territory (example)

- Process includes competitor audit, keyword ideation, scoring, rationale, and validation.
- Key Elements list: three keywords, definition, proof points, positioning, and example usage.
- Requirements enforce specificity, benefit mapping, proof points, and word limit.
- Restrictions ban brand repetition, vague superlatives, and undefined buzzwords.

### Mission (example)

- Process covers mission sentence, time‑bound vision, strategic pillars, metrics, and alignment.
- Key Elements include mission, vision, pillars, OKRs, and impact statement.
- Requirements: clarity, measurability, audience focus, and consistency with other sections.
- Restrictions: no jargon, platitudes, or implementation details.

---

## Authoring Checklist

Before finalizing a prompt, confirm:

- [ ] “How to Respond” instructs execution of the Prompt and formatting rules
- [ ] Output Schema uses the +++/===DELIMITER=== pattern and starts/ends with ```markdown fences
- [ ] Each numbered task includes Process, Key Elements, Requirements, Restrictions
- [ ] Word limits and measurability are specified where relevant
- [ ] Restrictions cover common pitfalls (jargon, clichés, contradictions)
- [ ] Additional Information block lists all dynamic variables needed by the prompt
- [ ] Tone is concise, specific, and aligned with the brand/project context

---

## Tips
- Prefer explicit, testable instructions over descriptive prose
- Keep bullet points short; avoid nested complexity unless needed for clarity
- Use example snippets sparingly to demonstrate format without overwhelming the model
- Align Requirements/Restrictions across related sections for consistency

---

## Maintenance
- Store new prompts under specs/prompt/[area]/ following existing patterns
- When updating the format, update this guide and point authors to relevant examples (e.g., specs/prompt/brand/brand.ts)
- Keep sections DRY: if a rule applies globally, put it in “How to Respond” instead of repeating per task

