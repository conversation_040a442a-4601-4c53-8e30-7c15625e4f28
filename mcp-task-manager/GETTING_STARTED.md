# Getting Started with MCP Task Manager

This guide will help you quickly get started with the MCP Task Manager and understand how to use its comprehensive task management features.

## What is MCP Task Manager?

MCP Task Manager is a comprehensive task management MCP server that provides hierarchical project organization. It allows AI assistants to manage projects, roadmaps, releases, tasks, and subtasks with local SQLite database storage.

## Quick Start (5 minutes)

### 1. Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/mcp-task-manager.git
cd mcp-task-manager

# Install in development mode
pip install -e ".[dev]"
```

### 2. Start the Server

```bash
# Start the task manager server
mcp-task-manager --dev
```

### 3. Test with Client

In another terminal:

```bash
# Run the example client
python examples/example_client.py
```

That's it! You now have a working task management MCP server with comprehensive project management tools.

## Basic Usage (10 minutes)

### 1. Create Your First Project

```bash
# Using the MCP client or through Claude <PERSON>/Cursor
create_project(name="My First Project", description="Learning task management")
```

### 2. Build a Project Structure

```bash
# Create a roadmap
create_roadmap(project_id=1, title="Q1 2024 Goals", description="First quarter objectives")

# Create a release
create_release(roadmap_id=1, title="MVP Release", version="1.0.0")

# Create tasks
create_task(release_id=1, title="Setup Development Environment", priority="high")
create_task(release_id=1, title="Implement Core Features", priority="medium")

# Create subtasks
create_subtask(task_id=1, title="Install dependencies", estimated_hours=2)
create_subtask(task_id=1, title="Configure database", estimated_hours=3)
```

### 3. Track Progress

```bash
# Get project overview
get_project(project_id=1)

# Search across all entities
search_entities(entity_type="task", search_term="setup")

# Get progress summary
get_progress_summary(project_id=1)
```

## What's Included

### Core Components

- **Hierarchical Task Management**: Projects → Roadmaps → Releases → Tasks → Subtasks
- **SQLite Database**: Local database storage with automatic schema creation
- **Progress Tracking**: Automatic progress calculation across all levels
- **Status Management**: Comprehensive status tracking for all entities
- **Priority System**: Priority levels for tasks and subtasks
- **Time Tracking**: Estimated vs actual hours tracking

### Management Tools

1. **Project Management**: Create, list, update, delete projects
2. **Roadmap Management**: Strategic planning within projects
3. **Release Management**: Milestone and version management
4. **Task Management**: Individual work item tracking
5. **Subtask Management**: Granular work breakdown
6. **Search & Progress**: Cross-entity search and progress reporting

### Development Features

- **Development Mode**: Run with MCP Inspector for debugging
- **Auto-installation**: Install in Claude Desktop with one command
- **Environment Configuration**: Support for environment variables
- **Modern Python Packaging**: Uses pyproject.toml with setuptools
- **Comprehensive Testing**: Unit and integration tests

### Documentation

- **README.md**: Main project documentation
- **CUSTOMIZATION.md**: Detailed customization guide
- **DEVELOPMENT.md**: Development workflows and best practices
- **GETTING_STARTED.md**: This quick start guide

## Common Use Cases

### 1. Software Development Projects

```bash
# Create a software project
create_project(name="Mobile App Development", description="iOS and Android app")

# Plan releases
create_roadmap(project_id=1, title="2024 Development Roadmap")
create_release(roadmap_id=1, title="Beta Release", version="0.9.0")
create_release(roadmap_id=1, title="Production Release", version="1.0.0")

# Break down features into tasks
create_task(release_id=1, title="User Authentication", priority="high")
create_task(release_id=1, title="Data Synchronization", priority="medium")
```

### 2. Marketing Campaigns

```bash
# Create a marketing project
create_project(name="Q1 Marketing Campaign", description="Product launch campaign")

# Plan campaign phases
create_roadmap(project_id=2, title="Launch Strategy")
create_release(roadmap_id=2, title="Pre-launch Phase")
create_release(roadmap_id=2, title="Launch Phase")
create_release(roadmap_id=2, title="Post-launch Phase")
```

### 3. Research Projects

```bash
# Create a research project
create_project(name="Market Research Study", description="Customer behavior analysis")

# Organize research phases
create_roadmap(project_id=3, title="Research Methodology")
create_release(roadmap_id=3, title="Data Collection")
create_task(release_id=3, title="Survey Design", estimated_hours=20)
create_task(release_id=3, title="Interview Preparation", estimated_hours=15)
```

### 4. Event Planning

```bash
# Create an event project
create_project(name="Annual Conference", description="Company annual conference 2024")

# Plan event phases
create_roadmap(project_id=4, title="Conference Planning")
create_release(roadmap_id=4, title="Venue & Logistics")
create_release(roadmap_id=4, title="Content & Speakers")
create_release(roadmap_id=4, title="Marketing & Registration")
```

## MCP Client Configuration

### Claude Desktop

Add to `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "task-manager": {
      "command": "mcp-task-manager"
    }
  }
}
```

### Cursor

Add to `~/.cursor/mcp.json`:

```json
{
  "mcpServers": {
    "task-manager": {
      "command": "mcp-task-manager"
    }
  }
}
```

## Development Commands

```bash
# Install in development mode
make install-dev

# Run tests
make test

# Format code
make format

# Lint code
make lint

# Run server
make run

# Run in development mode
make dev

# Build package
make build
```

## Next Steps

1. **Start with a simple project** to understand the hierarchy
2. **Explore all available tools** through the MCP Inspector
3. **Set up your preferred MCP client** (Claude Desktop or Cursor)
4. **Import existing projects** from other tools if needed
5. **Customize the database schema** for additional fields if required
6. **Integrate with external tools** through custom extensions

## Getting Help

- **Documentation**: Check the `docs/` directory for detailed guides
- **Examples**: Look at `examples/` for usage patterns
- **Tests**: Review `tests/` for testing examples
- **Original Project**: Study `mcp-local-context` for more complex examples

## Best Practices

1. **Project Structure**: Use clear, descriptive names for projects and roadmaps
2. **Task Breakdown**: Break large tasks into manageable subtasks
3. **Status Updates**: Keep status information current for accurate progress tracking
4. **Time Estimation**: Provide realistic time estimates for better planning
5. **Priority Management**: Use priorities to focus on important work
6. **Regular Reviews**: Periodically review and update project progress

## Troubleshooting

### Server Won't Start

- Check Python version (3.10+ required)
- Verify dependencies are installed
- Check for port conflicts

### Client Can't Connect

- Ensure server is running
- Check host and port configuration
- Verify MCP client configuration

### Database Issues

- Check SQLite database file permissions
- Verify database schema is properly initialized
- Check for database lock issues

### Tools Not Working

- Verify entity relationships (project → roadmap → release → task → subtask)
- Check required parameters for each tool
- Ensure proper status and priority values

## Resources

- [MCP Documentation](https://modelcontextprotocol.io/)
- [FastMCP Documentation](https://github.com/jlowin/fastmcp)
- [SQLAlchemy Documentation](https://docs.sqlalchemy.org/)
- [pytest Documentation](https://docs.pytest.org/)

Happy building! 🚀
