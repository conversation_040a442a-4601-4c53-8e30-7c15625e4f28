# Makefile for MCP Task Manager
# Provides convenient commands for development and deployment

.PHONY: help install install-dev test test-verbose lint format clean build publish run dev

# Default target
help:
	@echo "Available commands:"
	@echo "  install      - Install the package"
	@echo "  install-dev  - Install in development mode with dev dependencies"
	@echo "  test         - Run tests"
	@echo "  test-verbose - Run tests with verbose output"
	@echo "  lint         - Run linting (flake8, mypy)"
	@echo "  format       - Format code (black, isort)"
	@echo "  clean        - Clean build artifacts"
	@echo "  build        - Build the package"
	@echo "  publish      - Publish to PyPI"
	@echo "  run          - Run the server"
	@echo "  dev          - Run in development mode"

# Installation
install:
	pip install .

install-dev:
	pip install -e ".[dev]"

# Testing
test:
	python -m pytest tests/

test-verbose:
	python -m pytest tests/ -v

test-client:
	python tests/test_client.py

# Code quality
lint:
	flake8 server/ tests/
	mypy server/

format:
	black server/ tests/ examples/
	isort server/ tests/ examples/

# Build and publish
clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

build: clean
	python -m build

publish: build
	python -m twine upload dist/*

# Development
run:
	python -m server.server

dev:
	mcp-task-manager --dev

# Server management
start:
	mcp-task-manager &

stop:
	pkill -f "mcp-task-manager"

# Example usage
example:
	python examples/example_client.py
