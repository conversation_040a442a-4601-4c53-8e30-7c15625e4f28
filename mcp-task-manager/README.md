# MCP Task Manager

A comprehensive task management MCP server with hierarchical project organization. Manage projects, roadmaps, releases, tasks, and subtasks with local SQLite database storage.

## Features

- **Hierarchical Task Management**: Projects → Roadmaps → Releases → Tasks → Subtasks
- **Multi-Project Support**: Manage multiple projects with independent roadmaps and tasks
- **Local SQLite Database**: All data stored locally with no external dependencies
- **Progress Tracking**: Automatic progress calculation across all hierarchy levels
- **Status Management**: Track status (not_started, in_progress, completed, on_hold, cancelled)
- **Priority System**: Set priorities (low, medium, high, critical) for tasks and subtasks
- **Time Tracking**: Estimated vs actual hours tracking for tasks and subtasks
- **FastMCP Integration**: Built on the modern FastMCP framework
- **CLI Interface**: Command-line interface for easy server management
- **Development Mode**: Built-in development server with MCP Inspector support

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/mcp-task-manager.git
cd mcp-task-manager

# Install in development mode
pip install -e .

# Or install from PyPI (after publishing)
pip install mcp-task-manager
```

### 2. Run the Server

```bash
# Run with default settings
mcp-task-manager

# Run with custom settings
mcp-task-manager --host 0.0.0.0 --port 9000

# Run in development mode with MCP Inspector
mcp-task-manager --dev

# Install in Claude Desktop
mcp-task-manager --install
```

### 3. Basic Usage

Once the server is running, you can use it through any MCP-compatible client (like Claude Desktop or Cursor) to:

- Create and manage projects
- Define roadmaps within projects
- Plan releases within roadmaps
- Break down work into tasks and subtasks
- Track progress and status across all levels

## Project Structure

```
mcp-task-manager/
├── README.md                 # This file
├── pyproject.toml            # Modern Python packaging configuration
├── setup.py                  # Backward compatibility setup
├── requirements.txt          # Dependencies
├── server/                   # Main package directory
│   ├── __init__.py          # Package initialization
│   ├── server.py            # Main server implementation with all tools
│   ├── cli.py               # Command-line interface
│   ├── models.py            # SQLAlchemy database models
│   ├── database.py          # Database connection management
│   └── db_utils.py          # Database utility functions
├── tests/                   # Test files
│   ├── test_server.py       # Server tests
│   └── test_client.py       # Client tests
├── examples/                # Usage examples
│   └── example_client.py    # Example client code
├── docs/                    # Documentation
│   ├── CUSTOMIZATION.md     # Customization guide
│   └── DEVELOPMENT.md       # Development guide
└── scripts/                 # Utility scripts
    └── install.sh           # Installation script
```

## Database Schema

The task manager uses a hierarchical SQLite database structure:

### Core Models

- **Project**: Top-level organizational unit
- **Roadmap**: Strategic plans within a project
- **Release**: Specific milestones within a roadmap
- **Task**: Individual work items within a release
- **Subtask**: Granular work items within a task

### Key Features

- **Status Tracking**: All entities support status (not_started, in_progress, completed, on_hold, cancelled)
- **Priority System**: Tasks and subtasks have priority levels (low, medium, high, critical)
- **Time Tracking**: Estimated vs actual hours for tasks and subtasks
- **Progress Calculation**: Automatic progress rollup from subtasks to projects
- **Relationships**: Proper foreign key relationships with cascade delete

## Available Tools

The server provides comprehensive tools for each entity type:

### Project Management
- `create_project` - Create new projects
- `list_projects` - List all projects with filtering
- `get_project` - Get detailed project information with progress
- `update_project` - Update project details and status
- `delete_project` - Delete projects and all associated data

### Roadmap Management
- `create_roadmap` - Create roadmaps within projects
- `list_roadmaps` - List roadmaps with filtering
- `get_roadmap` - Get detailed roadmap information
- `update_roadmap` - Update roadmap details
- `delete_roadmap` - Delete roadmaps and associated data

### Release Management
- `create_release` - Create releases within roadmaps
- `list_releases` - List releases with filtering
- `get_release` - Get detailed release information
- `update_release` - Update release details
- `delete_release` - Delete releases and associated data

### Task Management
- `create_task` - Create tasks within releases
- `list_tasks` - List tasks with filtering
- `get_task` - Get detailed task information
- `update_task` - Update task details, priority, and assignment
- `delete_task` - Delete tasks and associated data

### Subtask Management
- `create_subtask` - Create subtasks within tasks
- `list_subtasks` - List subtasks with filtering
- `get_subtask` - Get detailed subtask information
- `update_subtask` - Update subtask details and completion status
- `delete_subtask` - Delete individual subtasks

### Search and Progress
- `search_entities` - Search across all entity types
- `get_progress_summary` - Get comprehensive progress reports

## Configuration

### Environment Variables

- `MCP_HOST`: Server host (default: 127.0.0.1)
- `MCP_PORT`: Server port (default: 8000)
- `MCP_PATH`: MCP endpoint path (default: /mcp)

### Command Line Arguments

```bash
mcp-task-manager --help
```

### Database Location

The SQLite database is created as `task_manager.db` in the current working directory. You can customize the database location by modifying the `DATABASE_URL` in `server/models.py`.

## MCP Client Configuration

### Claude Desktop

Add to your `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "task-manager": {
      "command": "mcp-task-manager"
    }
  }
}
```

### Cursor

Add to your `~/.cursor/mcp.json`:

```json
{
  "mcpServers": {
    "task-manager": {
      "command": "mcp-task-manager"
    }
  }
}
```

## Example Usage

Here's a typical workflow using the task manager:

```bash
# 1. Create a project
create_project(name="Website Redesign", description="Complete redesign of company website")

# 2. Create a roadmap
create_roadmap(project_id=1, title="Q1 2024 Roadmap", description="First quarter deliverables")

# 3. Create releases
create_release(roadmap_id=1, title="MVP Release", version="1.0.0", description="Minimum viable product")

# 4. Create tasks
create_task(release_id=1, title="Design Homepage", priority="high", estimated_hours=20)

# 5. Create subtasks
create_subtask(task_id=1, title="Create wireframes", estimated_hours=8)
create_subtask(task_id=1, title="Design mockups", estimated_hours=12)

# 6. Track progress
get_project(project_id=1)  # Shows overall progress
```

## Development

### Running Tests

```bash
# Run all tests
python -m pytest tests/

# Run specific test
python -m pytest tests/test_server.py

# Run with coverage
python -m pytest tests/ --cov=server
```

### Development Mode

```bash
# Run with MCP Inspector for debugging
mcp-task-manager --dev
```

### Database Management

```bash
# The database is automatically created on first run
# To reset the database, simply delete task_manager.db
rm task_manager.db
```

### Building and Publishing

```bash
# Install build dependencies
pip install build twine

# Build package
python -m build

# Publish to PyPI
python -m twine upload dist/*
```

## License

MIT License - see LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## Support

For questions and support:
- Check the documentation in the `docs/` directory
- Open an issue on GitHub
- Join the MCP community discussions

## Roadmap

Future enhancements planned:
- Web dashboard for visual project management
- Export/import functionality (JSON, CSV)
- Team collaboration features
- Integration with external tools (GitHub, Jira)
- Advanced reporting and analytics
