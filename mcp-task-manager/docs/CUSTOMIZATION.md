# Customization Guide

This guide explains how to customize the MCP Task Manager for your specific task management needs.

## Quick Customization Checklist

### 1. Database Customization
- [ ] Modify database models in `models.py` for additional fields
- [ ] Update database schema with custom attributes
- [ ] Add custom validation rules
- [ ] Configure database location and settings

### 2. Tool Customization
- [ ] Add custom fields to existing tools
- [ ] Create new tools for specific workflows
- [ ] Modify progress calculation logic
- [ ] Add custom search and filtering options

### 3. Integration Customization
- [ ] Add external tool integrations (GitHub, Jira, etc.)
- [ ] Create import/export functionality
- [ ] Add notification systems
- [ ] Integrate with time tracking tools

### 4. UI and Reporting
- [ ] Add custom progress reports
- [ ] Create dashboard views
- [ ] Add export formats (CSV, JSON, PDF)
- [ ] Customize status and priority options

## Detailed Customization Steps

### Adding Custom Fields to Models

1. **Extend Project model:**
   ```python
   # In server/models.py
   class Project(Base):
       # ... existing fields ...
       budget = Column(Float, nullable=True)
       client_name = Column(String(255), nullable=True)
       project_manager = Column(String(100), nullable=True)
       tags = Column(Text, nullable=True)  # JSON string for tags
   ```

2. **Extend Task model:**
   ```python
   class Task(Base):
       # ... existing fields ...
       complexity = Column(SQLEnum(ComplexityEnum), nullable=True)
       dependencies = Column(Text, nullable=True)  # JSON string for task IDs
       external_id = Column(String(100), nullable=True)  # For integrations
   ```

3. **Add custom enums:**
   ```python
   class ComplexityEnum(str, Enum):
       SIMPLE = "simple"
       MODERATE = "moderate"
       COMPLEX = "complex"
       VERY_COMPLEX = "very_complex"
   ```

### Adding Custom Tools

Add new tools to `server.py` for specific workflows:

```python
@self.mcp.tool()
def assign_task_to_team(task_id: int, team_members: List[str]) -> Dict[str, Any]:
    """
    Assign a task to multiple team members.

    Args:
        task_id: ID of the task to assign
        team_members: List of team member names/emails

    Returns:
        Assignment result with notifications sent
    """
    try:
        with self.db_manager.get_session() as session:
            task = session.query(Task).filter(Task.id == task_id).first()
            if not task:
                return {"error": f"Task {task_id} not found"}

            # Custom logic for team assignment
            task.assignee = ", ".join(team_members)
            session.commit()

            # Send notifications (custom implementation)
            self._send_assignment_notifications(task, team_members)

            return {"success": True, "assigned_to": team_members}
    except Exception as e:
        return {"error": f"Assignment failed: {str(e)}"}
```

### Adding Configuration

1. **Environment Variables:**
   Add to `cli.py`:
   ```python
   parser.add_argument(
       "--notification-webhook",
       default=os.environ.get("TASK_NOTIFICATION_WEBHOOK"),
       help="Webhook URL for task notifications"
   )
   parser.add_argument(
       "--default-priority",
       default=os.environ.get("DEFAULT_TASK_PRIORITY", "medium"),
       help="Default priority for new tasks"
   )
   ```

2. **Configuration Files:**
   Create a config module:
   ```python
   # server/config.py
   import os
   import json
   from pathlib import Path

   class TaskManagerConfig:
       def __init__(self):
           self.notification_webhook = os.environ.get("TASK_NOTIFICATION_WEBHOOK")
           self.default_priority = os.environ.get("DEFAULT_TASK_PRIORITY", "medium")
           self.config_file = Path.home() / ".task-manager" / "config.json"
           self.load_config()

       def load_config(self):
           if self.config_file.exists():
               with open(self.config_file) as f:
                   config = json.load(f)
                   self.__dict__.update(config)
   ```

### Adding External Integrations

1. **GitHub Integration:**
   ```python
   # Add to requirements.txt or pyproject.toml
   dependencies = [
       "mcp[cli]>=1.9.1",
       "sqlalchemy>=2.0.0",
       "pydantic>=2.0.0",
       "python-dateutil>=2.8.0",
       "requests>=2.28.0",   # For GitHub API
       "PyGithub>=1.58.0",   # GitHub SDK
   ]
   ```

2. **GitHub Issue Sync:**
   ```python
   @self.mcp.tool()
   def sync_github_issues(project_id: int, repo_url: str, github_token: str) -> Dict[str, Any]:
       """Sync GitHub issues as tasks."""
       from github import Github

       try:
           g = Github(github_token)
           repo = g.get_repo(repo_url.split('/')[-2:])
           issues = repo.get_issues(state='open')

           with self.db_manager.get_session() as session:
               project = session.query(Project).filter(Project.id == project_id).first()
               if not project:
                   return {"error": "Project not found"}

               synced_count = 0
               for issue in issues:
                   # Create task from GitHub issue
                   task = Task(
                       release_id=1,  # You'd need to determine this
                       title=issue.title,
                       description=issue.body,
                       external_id=f"github-{issue.number}",
                       assignee=issue.assignee.login if issue.assignee else None
                   )
                   session.add(task)
                   synced_count += 1

               session.commit()
               return {"success": True, "synced_issues": synced_count}
       except Exception as e:
           return {"error": f"GitHub sync failed: {str(e)}"}
   ```

### Customizing Tests

1. **Update test files:**
   - Rename test classes and methods
   - Update import statements
   - Add tests for your custom tools

2. **Add integration tests:**
   ```python
   # tests/test_integration.py
   import pytest
   from your_package_name.server import YourServerClass
   
   class TestIntegration:
       def test_your_workflow(self):
           # Test your specific workflow
           pass
   ```

### Documentation

1. **Update README.md:**
   - Replace project description
   - Update installation instructions
   - Document your tools and their usage
   - Add examples specific to your use case

2. **Add API documentation:**
   ```python
   # docs/api.md
   # Your API Documentation
   
   ## Tools
   
   ### your_custom_tool
   Description and examples...
   ```

### Deployment Customization

1. **Docker Support:**
   ```dockerfile
   # Dockerfile
   FROM python:3.11-slim
   WORKDIR /app
   COPY . .
   RUN pip install .
   CMD ["your-command"]
   ```

2. **Systemd Service:**
   ```ini
   # your-service.service
   [Unit]
   Description=Your MCP Server
   
   [Service]
   ExecStart=/usr/local/bin/your-command
   Restart=always
   
   [Install]
   WantedBy=multi-user.target
   ```

## Advanced Customization

### Custom Transport

If you need a different transport mechanism:

```python
# In server.py
def run(self):
    # Custom transport configuration
    self.mcp.run(
        transport="your-custom-transport",
        transport_options={"your": "options"}
    )
```

### Middleware and Hooks

Add middleware for logging, authentication, etc.:

```python
class YourMCPServer:
    def __init__(self):
        # ... existing code ...
        self._setup_middleware()
    
    def _setup_middleware(self):
        # Add logging, auth, etc.
        pass
```

### Plugin System

Create a plugin system for extensibility:

```python
# your_package_name/plugins.py
class PluginManager:
    def __init__(self):
        self.plugins = []
    
    def register_plugin(self, plugin):
        self.plugins.append(plugin)
    
    def load_tools(self, mcp_server):
        for plugin in self.plugins:
            plugin.register_tools(mcp_server)
```

## Testing Your Customizations

1. **Run the test suite:**
   ```bash
   make test
   ```

2. **Test the CLI:**
   ```bash
   your-command --help
   your-command --dev
   ```

3. **Test client integration:**
   ```bash
   python examples/example_client.py
   ```

## Publishing Your Package

1. **Update version:**
   ```bash
   # Update version in pyproject.toml and setup.py
   ```

2. **Build and publish:**
   ```bash
   make build
   make publish
   ```

## Getting Help

- Check the task manager documentation in the `docs/` directory
- Review the MCP documentation at https://modelcontextprotocol.io/
- Look at the database models in `server/models.py` for schema reference
- Examine existing tools in `server/server.py` for implementation patterns
- Join the MCP community discussions
