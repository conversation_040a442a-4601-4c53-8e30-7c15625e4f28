# Development Guide

This guide covers development workflows, best practices, and advanced topics for working with the MCP Task Manager.

## Development Setup

### Prerequisites

- Python 3.10 or later
- pip (Python package installer)
- Git (for version control)

### Quick Setup

```bash
# Clone the task manager repository
git clone https://github.com/yourusername/mcp-task-manager.git
cd mcp-task-manager

# Install in development mode
pip install -e ".[dev]"

# Or use the installation script
./scripts/install.sh --dev
```

### Development Dependencies

The development dependencies include:

- **pytest**: Testing framework
- **pytest-asyncio**: Async testing support
- **pytest-mock**: Mocking support for tests
- **factory-boy**: Test data generation
- **black**: Code formatting
- **isort**: Import sorting
- **flake8**: Linting
- **mypy**: Type checking

## Development Workflow

### 1. Code Style and Formatting

```bash
# Format code
make format

# Check linting
make lint

# Or run individually
black server/ tests/ examples/
isort server/ tests/ examples/
flake8 server/ tests/
mypy server/
```

### 2. Testing

```bash
# Run all tests
make test

# Run tests with verbose output
make test-verbose

# Run specific test file
python -m pytest tests/test_server.py -v

# Run tests with coverage
python -m pytest tests/ --cov=server --cov-report=html
```

### 3. Running the Server

```bash
# Run directly
python -m server.server

# Run with CLI
mcp-task-manager

# Run in development mode (with MCP Inspector)
mcp-task-manager --dev

# Run with custom settings
mcp-task-manager --host 0.0.0.0 --port 9000
```

### 4. Testing Client Integration

```bash
# Run the example client
python examples/example_client.py

# Run the test client
python tests/test_client.py
```

## Project Structure Deep Dive

```
mcp-task-manager/
├── server/                 # Main package
│   ├── __init__.py         # Package initialization
│   ├── server.py           # Core server implementation with all tools
│   ├── cli.py              # Command-line interface
│   ├── models.py           # SQLAlchemy database models
│   ├── database.py         # Database connection management
│   └── db_utils.py         # Database utility functions
├── tests/                  # Test suite
│   ├── __init__.py
│   ├── test_server.py      # Server unit tests
│   └── test_client.py      # Client integration tests
├── examples/               # Usage examples
│   └── example_client.py   # Example client implementation
├── docs/                   # Documentation
│   ├── CUSTOMIZATION.md    # Customization guide
│   └── DEVELOPMENT.md      # This file
├── scripts/                # Utility scripts
│   └── install.sh          # Installation script
├── pyproject.toml          # Modern Python packaging
├── setup.py                # Backward compatibility
├── requirements.txt        # Dependencies
├── Makefile               # Development commands
├── MANIFEST.in            # Package manifest
├── GETTING_STARTED.md     # Quick start guide
├── .gitignore             # Git ignore rules
├── LICENSE                # License file
└── README.md              # Main documentation
```

## Adding New Tools

### Basic Tool Structure

```python
@self.mcp.tool()
def your_tool_name(param1: str, param2: int = 0) -> Dict[str, Any]:
    """
    Tool description that will appear in the MCP client.
    
    Provide a clear description of what the tool does and how to use it.
    This docstring is important as it helps AI assistants understand
    when and how to use your tool.
    
    Args:
        param1: Description of the first parameter
        param2: Description of the second parameter with default value
        
    Returns:
        Description of what the tool returns
    """
    try:
        # Your tool implementation here
        result = your_business_logic(param1, param2)
        
        return {
            "success": True,
            "result": result,
            "metadata": {
                "timestamp": datetime.datetime.now().isoformat(),
                "parameters": {"param1": param1, "param2": param2}
            }
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__
        }
```

### Tool Best Practices

1. **Clear Documentation**: Write comprehensive docstrings
2. **Type Hints**: Use proper type hints for parameters and return values
3. **Error Handling**: Always handle exceptions gracefully
4. **Validation**: Validate input parameters
5. **Consistent Returns**: Use consistent return formats
6. **Logging**: Add logging for debugging

### Example: Custom Task Management Tool

```python
@self.mcp.tool()
def bulk_update_task_status(task_ids: List[int], new_status: str, update_reason: str = "") -> Dict[str, Any]:
    """
    Update the status of multiple tasks at once.

    Args:
        task_ids: List of task IDs to update
        new_status: New status to set (not_started, in_progress, completed, on_hold, cancelled)
        update_reason: Optional reason for the status change

    Returns:
        Result of the bulk update operation
    """
    try:
        # Validate status
        try:
            status_enum = StatusEnum(new_status)
        except ValueError:
            return {"error": f"Invalid status: {new_status}. Valid values: {[s.value for s in StatusEnum]}"}

        updated_tasks = []
        failed_tasks = []

        with self.db_manager.get_session() as session:
            for task_id in task_ids:
                try:
                    task = session.query(Task).filter(Task.id == task_id).first()
                    if not task:
                        failed_tasks.append({"id": task_id, "error": "Task not found"})
                        continue

                    old_status = task.status.value
                    task.status = status_enum

                    # Add update reason to description if provided
                    if update_reason:
                        task.description = f"{task.description}\n\nStatus updated: {old_status} → {new_status}\nReason: {update_reason}"

                    updated_tasks.append({
                        "id": task.id,
                        "title": task.title,
                        "old_status": old_status,
                        "new_status": new_status
                    })

                except Exception as e:
                    failed_tasks.append({"id": task_id, "error": str(e)})

            session.commit()

            return {
                "success": True,
                "updated_count": len(updated_tasks),
                "failed_count": len(failed_tasks),
                "updated_tasks": updated_tasks,
                "failed_tasks": failed_tasks
            }

    except Exception as e:
        return {"error": f"Bulk update failed: {str(e)}"}
```

## Testing Guidelines

### Unit Tests

```python
# tests/test_your_tool.py
import pytest
from unittest.mock import patch
from server.server import TaskManagerMCPServer

class TestYourTool:
    @patch('server.server.get_database_manager')
    def setup_method(self, mock_db_manager):
        mock_db_manager.return_value.initialize_database.return_value = None
        self.server = TaskManagerMCPServer()

    def test_tool_exists(self):
        # Test that your tool is registered
        assert self.server.mcp is not None

    @pytest.mark.asyncio
    async def test_tool_functionality(self):
        # Test your tool's functionality
        # Note: You'll need to extract the tool function for direct testing
        pass
```

### Integration Tests

```python
# tests/test_integration.py
import pytest
import asyncio
from mcp.client import Client

@pytest.mark.asyncio
async def test_full_workflow():
    """Test a complete workflow using the MCP client."""
    async with Client("http://127.0.0.1:8000/mcp") as client:
        # Test your complete workflow
        result = await client.call_tool("your_tool_name", {"param": "value"})
        assert result.content["success"] is True
```

## Debugging

### Server Debugging

1. **Add logging:**
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   logger = logging.getLogger(__name__)
   
   @self.mcp.tool()
   def debug_tool():
       logger.debug("Debug message")
       logger.info("Info message")
   ```

2. **Use development mode:**
   ```bash
   mcp-task-manager --dev
   ```

3. **Print debugging:**
   ```python
   @self.mcp.tool()
   def debug_tool():
       print(f"Debug: received parameters")  # Will appear in server console
   ```

### Client Debugging

1. **Use the test client:**
   ```bash
   python tests/test_client.py
   ```

2. **Add debug output:**
   ```python
   # In your client code
   import json
   result = await client.call_tool("tool_name", params)
   print(json.dumps(result.content, indent=2))
   ```

## Performance Considerations

### Tool Performance

1. **Async Operations**: Use async/await for I/O operations
2. **Caching**: Implement caching for expensive operations
3. **Lazy Loading**: Load resources only when needed
4. **Resource Management**: Properly close files, connections, etc.

### Example: Cached Tool

```python
from functools import lru_cache

class TaskManagerMCPServer:
    def __init__(self):
        # ... existing initialization ...
        self._progress_cache = {}

    @self.mcp.tool()
    def get_cached_project_progress(self, project_id: int, force_refresh: bool = False) -> Dict[str, Any]:
        """Get project progress with caching."""
        cache_key = f"progress_{project_id}"

        if not force_refresh and cache_key in self._progress_cache:
            return {"result": self._progress_cache[cache_key], "cached": True}

        # Expensive progress calculation
        with self.db_manager.get_session() as session:
            progress = get_project_progress(session, project_id)
            self._progress_cache[cache_key] = progress

        return {"result": progress, "cached": False}
```

## Deployment

### Local Development

```bash
# Install and run locally
pip install -e .
mcp-task-manager
```

### Production Deployment

1. **Install from PyPI:**
   ```bash
   pip install mcp-task-manager
   ```

2. **Docker deployment:**
   ```dockerfile
   FROM python:3.11-slim
   RUN pip install mcp-task-manager
   CMD ["mcp-task-manager"]
   ```

3. **Systemd service:**
   ```bash
   sudo systemctl enable mcp-task-manager
   sudo systemctl start mcp-task-manager
   ```

## Contributing

### Code Review Checklist

- [ ] Code follows style guidelines (black, isort)
- [ ] All tests pass
- [ ] New features have tests
- [ ] Documentation is updated
- [ ] Type hints are used
- [ ] Error handling is implemented

### Pull Request Process

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Update documentation
6. Submit a pull request

## Troubleshooting

### Common Issues

1. **Import errors**: Check package installation and Python path
2. **Port conflicts**: Use different port with `--port` option
3. **Permission errors**: Check file permissions and user access
4. **MCP client connection**: Ensure server is running and accessible

### Getting Help

- Check the logs for error messages
- Use the development mode for debugging
- Review the MCP documentation
- Check existing issues in the repository
