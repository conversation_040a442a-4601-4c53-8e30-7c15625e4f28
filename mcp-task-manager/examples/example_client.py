#!/usr/bin/env python3
"""
Example Task Manager MCP Client

This script demonstrates how to create a client that connects to the task manager
MCP server and uses its tools programmatically.
"""

import asyncio
import json
import sys
from pathlib import Path

# Add the parent directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from mcp.client import Client
    HAS_MCP_CLIENT = True
except ImportError:
    print("MCP client not available. Install with: pip install 'mcp[cli]'")
    HAS_MCP_CLIENT = False


class TaskManagerClientExample:
    """Example task manager MCP client that demonstrates various usage patterns."""

    def __init__(self, server_url: str = "http://127.0.0.1:8000/mcp"):
        """Initialize the client with server URL."""
        self.server_url = server_url
        self.client = None

    async def connect(self):
        """Connect to the MCP server."""
        if not HAS_MCP_CLIENT:
            raise ImportError("MCP client not available")
        
        self.client = Client(self.server_url)
        await self.client.__aenter__()
        print(f"Connected to MCP server at {self.server_url}")

    async def disconnect(self):
        """Disconnect from the MCP server."""
        if self.client:
            await self.client.__aexit__(None, None, None)
            print("Disconnected from MCP server")

    async def get_server_info(self):
        """Get information about the server."""
        if not self.client:
            raise RuntimeError("Not connected to server")
        
        info = await self.client.get_server_info()
        print(f"Server: {info.get('name', 'Unknown')}")
        print(f"Version: {info.get('version', 'Unknown')}")
        return info

    async def list_available_tools(self):
        """List all available tools on the server."""
        if not self.client:
            raise RuntimeError("Not connected to server")
        
        tools = await self.client.list_tools()
        print(f"\nAvailable tools ({len(tools)}):")
        for tool in tools:
            print(f"  • {tool.name}")
            print(f"    {tool.description}")
            if hasattr(tool, 'parameters') and tool.parameters:
                print(f"    Parameters: {list(tool.parameters.get('properties', {}).keys())}")
        
        return tools

    async def demonstrate_project_management(self):
        """Demonstrate project management tools."""
        print("\n" + "="*50)
        print("Demonstrating Project Management")
        print("="*50)

        # Create a project
        result = await self.client.call_tool("create_project", {
            "name": "Example Project",
            "description": "A sample project for demonstration",
            "status": "not_started"
        })
        print("Created project:")
        print(json.dumps(result.content, indent=2))

        if result.content.get("success"):
            project_id = result.content["project"]["id"]

            # List projects
            result = await self.client.call_tool("list_projects")
            print("\nProject list:")
            print(json.dumps(result.content, indent=2))

            # Get project details
            result = await self.client.call_tool("get_project", {"project_id": project_id})
            print(f"\nProject {project_id} details:")
            print(json.dumps(result.content, indent=2))

    async def demonstrate_roadmap_management(self):
        """Demonstrate roadmap management tools."""
        print("\n" + "="*50)
        print("Demonstrating Roadmap Management")
        print("="*50)

        # First, create a project to contain the roadmap
        project_result = await self.client.call_tool("create_project", {
            "name": "Roadmap Demo Project",
            "description": "Project for roadmap demonstration"
        })

        if project_result.content.get("success"):
            project_id = project_result.content["project"]["id"]

            # Create a roadmap
            result = await self.client.call_tool("create_roadmap", {
                "project_id": project_id,
                "title": "Q1 2024 Roadmap",
                "description": "First quarter objectives and milestones"
            })
            print("Created roadmap:")
            print(json.dumps(result.content, indent=2))

    async def demonstrate_task_workflow(self):
        """Demonstrate a complete task workflow."""
        print("\n" + "="*50)
        print("Demonstrating Complete Task Workflow")
        print("="*50)

        # Create project → roadmap → release → task → subtask

        # 1. Create project
        project_result = await self.client.call_tool("create_project", {
            "name": "Workflow Demo",
            "description": "Complete workflow demonstration"
        })

        if not project_result.content.get("success"):
            print("Failed to create project")
            return

        project_id = project_result.content["project"]["id"]
        print(f"✓ Created project {project_id}")

        # 2. Create roadmap
        roadmap_result = await self.client.call_tool("create_roadmap", {
            "project_id": project_id,
            "title": "Demo Roadmap",
            "description": "Demonstration roadmap"
        })

        if roadmap_result.content.get("success"):
            roadmap_id = roadmap_result.content["roadmap"]["id"]
            print(f"✓ Created roadmap {roadmap_id}")

            # 3. Create release
            release_result = await self.client.call_tool("create_release", {
                "roadmap_id": roadmap_id,
                "title": "Demo Release v1.0",
                "description": "First demo release",
                "version": "1.0.0"
            })

            if release_result.content.get("success"):
                release_id = release_result.content["release"]["id"]
                print(f"✓ Created release {release_id}")

                # 4. Create task
                task_result = await self.client.call_tool("create_task", {
                    "release_id": release_id,
                    "title": "Demo Task",
                    "description": "A demonstration task",
                    "priority": "high",
                    "estimated_hours": 8.0
                })

                if task_result.content.get("success"):
                    task_id = task_result.content["task"]["id"]
                    print(f"✓ Created task {task_id}")

                    # 5. Create subtasks
                    subtask1_result = await self.client.call_tool("create_subtask", {
                        "task_id": task_id,
                        "title": "Subtask 1: Planning",
                        "description": "Plan the implementation",
                        "estimated_hours": 2.0
                    })

                    subtask2_result = await self.client.call_tool("create_subtask", {
                        "task_id": task_id,
                        "title": "Subtask 2: Implementation",
                        "description": "Implement the feature",
                        "estimated_hours": 6.0
                    })

                    if subtask1_result.content.get("success") and subtask2_result.content.get("success"):
                        print("✓ Created subtasks")

                        # 6. Show progress
                        progress_result = await self.client.call_tool("get_project", {"project_id": project_id})
                        if progress_result.content.get("success"):
                            print("\nProject progress:")
                            print(json.dumps(progress_result.content["progress"], indent=2))

    async def run_all_examples(self):
        """Run all example demonstrations."""
        try:
            await self.connect()
            await self.get_server_info()
            await self.list_available_tools()
            await self.demonstrate_project_management()
            await self.demonstrate_roadmap_management()
            await self.demonstrate_task_workflow()
        finally:
            await self.disconnect()


async def main():
    """Main function to run the example client."""
    print("Task Manager MCP Client Example")
    print("This example demonstrates how to connect to and use the task manager MCP server.")
    print()

    if not HAS_MCP_CLIENT:
        print("Error: MCP client not available.")
        print("Install with: pip install 'mcp[cli]'")
        return

    # Create and run the example client
    client_example = TaskManagerClientExample()

    try:
        await client_example.run_all_examples()
        print("\n✓ All examples completed successfully!")
    except Exception as e:
        print(f"\n✗ Error running examples: {e}")
        print("\nMake sure your task manager MCP server is running:")
        print("  mcp-task-manager")
        print("  # or")
        print("  python -m server.server")


if __name__ == "__main__":
    if HAS_MCP_CLIENT:
        asyncio.run(main())
    else:
        print("MCP client not available. Install with: pip install 'mcp[cli]'")
        sys.exit(1)
