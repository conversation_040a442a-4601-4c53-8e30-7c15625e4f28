[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "mcp-task-manager"
version = "0.1.0"
description = "A comprehensive task manager MCP server with roadmaps, releases, tasks, and subtasks"
readme = "README.md"
requires-python = ">=3.10"
license = {text = "MIT"}
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
keywords = ["mcp", "model-context-protocol", "ai", "assistant", "task-manager", "roadmap", "project-management", "sqlite"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
dependencies = [
    "mcp[cli]>=1.9.1",
    "sqlalchemy>=2.0.0",
    "pydantic>=2.0.0",
    "python-dateutil>=2.8.0",
    "typing-extensions>=4.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.10.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "factory-boy>=3.2.0",
]
test = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.10.0",
    "factory-boy>=3.2.0",
]

[project.urls]
Homepage = "https://github.com/yourusername/mcp-task-manager"
Repository = "https://github.com/yourusername/mcp-task-manager"
Issues = "https://github.com/yourusername/mcp-task-manager/issues"
Documentation = "https://github.com/yourusername/mcp-task-manager#readme"

[project.scripts]
mcp-task-manager = "server.cli:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["server*"]

[tool.setuptools.package-data]
server = ["*.md", "*.txt", "*.json"]

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["server"]

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "auto"
