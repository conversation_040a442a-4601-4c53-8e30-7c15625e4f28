#!/bin/bash
# Installation script for MCP Task Manager

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check Python version
check_python() {
    if ! command_exists python3; then
        print_error "Python 3 is not installed. Please install Python 3.10 or later."
        exit 1
    fi
    
    python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    required_version="3.10"
    
    if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 10) else 1)"; then
        print_error "Python $required_version or later is required. Found: $python_version"
        exit 1
    fi
    
    print_success "Python $python_version detected"
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Check if pip is available
    if ! command_exists pip3; then
        print_error "pip3 is not installed. Please install pip."
        exit 1
    fi
    
    # Install the package in development mode
    if [ "$1" = "dev" ]; then
        print_status "Installing in development mode with dev dependencies..."
        pip3 install -e ".[dev]"
    else
        print_status "Installing in normal mode..."
        pip3 install -e .
    fi
    
    print_success "Dependencies installed successfully"
}

# Run tests
run_tests() {
    print_status "Running tests..."
    
    if command_exists pytest; then
        python3 -m pytest tests/ -v
        print_success "All tests passed"
    else
        print_warning "pytest not available, running basic tests..."
        python3 -m unittest discover tests/
    fi
}

# Start the server
start_server() {
    print_status "Starting Task Manager MCP server..."

    if [ "$1" = "dev" ]; then
        print_status "Starting in development mode..."
        mcp-task-manager --dev
    else
        print_status "Starting server..."
        mcp-task-manager
    fi
}

# Main installation function
main() {
    echo "MCP Task Manager Installation Script"
    echo "===================================="
    
    # Parse command line arguments
    INSTALL_MODE="normal"
    RUN_TESTS=false
    START_SERVER=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --dev)
                INSTALL_MODE="dev"
                shift
                ;;
            --test)
                RUN_TESTS=true
                shift
                ;;
            --run)
                START_SERVER=true
                shift
                ;;
            --help|-h)
                echo "Usage: $0 [OPTIONS]"
                echo ""
                echo "Options:"
                echo "  --dev     Install in development mode with dev dependencies"
                echo "  --test    Run tests after installation"
                echo "  --run     Start the server after installation"
                echo "  --help    Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                echo "Use --help for usage information"
                exit 1
                ;;
        esac
    done
    
    # Check prerequisites
    check_python
    
    # Install dependencies
    install_dependencies "$INSTALL_MODE"
    
    # Run tests if requested
    if [ "$RUN_TESTS" = true ]; then
        run_tests
    fi
    
    # Start server if requested
    if [ "$START_SERVER" = true ]; then
        start_server "$INSTALL_MODE"
    else
        print_success "Installation completed successfully!"
        echo ""
        echo "Next steps:"
        echo "  1. Run the server: mcp-task-manager"
        echo "  2. Run in dev mode: mcp-task-manager --dev"
        echo "  3. Install in Claude Desktop: mcp-task-manager --install"
        echo "  4. Run tests: make test"
        echo "  5. See examples: python examples/example_client.py"
    fi
}

# Run main function
main "$@"
