#!/usr/bin/env python3
"""
MCP Task Manager CLI

Command-line interface for the task manager MCP server with installation and development support.
"""

import os
import sys
import argparse
import subprocess


def find_mcp_command():
    """Find the mcp command in the system."""
    # Try to find the mcp command in the PATH
    try:
        mcp_path = subprocess.check_output(["which", "mcp"], stderr=subprocess.DEVNULL).decode().strip()
        return mcp_path
    except subprocess.CalledProcessError:
        # Try to find the mcp command in common locations
        common_locations = [
            os.path.expanduser("~/.local/bin/mcp"),
            "/usr/local/bin/mcp",
            "/usr/bin/mcp",
        ]
        for location in common_locations:
            if os.path.isfile(location):
                return location
        
        # If we can't find the mcp command, return None
        return None


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="MCP Task Manager - A comprehensive task management MCP server",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s                    # Run the server with default settings
  %(prog)s --host 0.0.0.0     # Run on all interfaces
  %(prog)s --port 9000        # Run on port 9000
  %(prog)s --dev              # Run in development mode with MCP Inspector
  %(prog)s --install          # Install in Claude Desktop
        """
    )
    
    # Server configuration
    parser.add_argument(
        "--host",
        default=os.environ.get("MCP_HOST", "127.0.0.1"),
        help="Host to run the server on (default: 127.0.0.1)"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=int(os.environ.get("MCP_PORT", "8000")),
        help="Port to run the server on (default: 8000)"
    )
    parser.add_argument(
        "--path",
        default=os.environ.get("MCP_PATH", "/mcp"),
        help="Path for the MCP endpoint (default: /mcp)"
    )
    
    # Mode selection
    mode_group = parser.add_mutually_exclusive_group()
    mode_group.add_argument(
        "--install",
        action="store_true",
        help="Install the server in Claude Desktop"
    )
    mode_group.add_argument(
        "--dev",
        action="store_true",
        help="Run the server in development mode with the MCP Inspector"
    )
    
    # Additional options
    parser.add_argument(
        "--name",
        default="Task Manager",
        help="Name for the server when installing (default: Task Manager)"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose output"
    )
    
    return parser.parse_args()


def main():
    """Main entry point for the CLI."""
    args = parse_args()

    # Set environment variables
    os.environ["MCP_HOST"] = args.host
    os.environ["MCP_PORT"] = str(args.port)
    os.environ["MCP_PATH"] = args.path

    if args.verbose:
        print(f"Configuration:")
        print(f"  Host: {args.host}")
        print(f"  Port: {args.port}")
        print(f"  Path: {args.path}")
        print()

    if args.install or args.dev:
        # Find the mcp command
        mcp_cmd = find_mcp_command()
        if not mcp_cmd:
            print("Error: Could not find the mcp command. Please install the MCP Python SDK.")
            print("You can install it with: pip install 'mcp[cli]'")
            sys.exit(1)
        
        # Get the path to the server module
        server_module = "server.server"
        
        if args.install:
            # Install the server in Claude Desktop
            print(f"Installing '{args.name}' in Claude Desktop...")
            cmd = [mcp_cmd, "install", "-m", server_module, "--name", args.name]
            
            # Add environment variables
            cmd.extend(["-v", f"MCP_HOST={args.host}"])
            cmd.extend(["-v", f"MCP_PORT={args.port}"])
            cmd.extend(["-v", f"MCP_PATH={args.path}"])
            
        else:  # args.dev
            # Run the server in development mode with the MCP Inspector
            print("Starting server in development mode with MCP Inspector...")
            cmd = [mcp_cmd, "dev", "-m", server_module]
        
        if args.verbose:
            print(f"Running: {' '.join(cmd)}")
        
        # Run the command
        try:
            subprocess.run(cmd, check=True)
        except subprocess.CalledProcessError as e:
            print(f"Error running command: {e}")
            sys.exit(1)
        except KeyboardInterrupt:
            print("\nOperation cancelled by user")
            sys.exit(0)
    
    else:
        # Import the server module and run it directly
        try:
            from .server import main as server_main

            if args.verbose:
                print("Starting server directly...")

            # Run the server
            return server_main()

        except ImportError as e:
            print(f"Error importing server module: {e}")
            print("Make sure the package is properly installed.")
            sys.exit(1)
        except KeyboardInterrupt:
            print("\nServer stopped by user")
            sys.exit(0)


if __name__ == "__main__":
    main()
