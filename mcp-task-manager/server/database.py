#!/usr/bin/env python3
"""
Database connection and initialization utilities for the Task Manager MCP Server.

This module provides database connection management, initialization, and utility
functions for working with the SQLite local database.
"""

import os
import logging
from contextlib import contextmanager
from typing import Generator, Optional
from pathlib import Path

from sqlalchemy import create_engine, Engine
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy.exc import SQLAlchemyError

from .models import (
    Base,
    Project,
    Roadmap,
    Release,
    Task,
    Subtask,
    StatusEnum,
    PriorityEnum,
    create_database_engine,
    create_session_factory,
    init_database,
    DATABASE_URL
)

# Configure logging
logger = logging.getLogger(__name__)


class DatabaseManager:
    """
    Database manager for handling SQLite database operations.
    
    This class provides a centralized way to manage database connections,
    sessions, and initialization for the task manager system.
    """
    
    def __init__(self, database_url: Optional[str] = None, echo: bool = False):
        """
        Initialize the database manager.
        
        Args:
            database_url: Optional custom database URL (defaults to local SQLite)
            echo: Whether to echo SQL queries for debugging
        """
        self.database_url = database_url or DATABASE_URL
        self.echo = echo
        self._engine: Optional[Engine] = None
        self._session_factory: Optional[sessionmaker] = None
        
        # Ensure the database directory exists
        self._ensure_database_directory()
    
    def _ensure_database_directory(self) -> None:
        """Ensure the database directory exists for SQLite file."""
        if self.database_url.startswith("sqlite:///"):
            db_path = self.database_url.replace("sqlite:///", "")
            db_dir = Path(db_path).parent
            db_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"Database directory ensured: {db_dir}")
    
    @property
    def engine(self) -> Engine:
        """Get or create the database engine."""
        if self._engine is None:
            self._engine = create_engine(
                self.database_url,
                echo=self.echo,
                connect_args={"check_same_thread": False} if "sqlite" in self.database_url else {}
            )
            logger.info(f"Database engine created for: {self.database_url}")
        return self._engine
    
    @property
    def session_factory(self) -> sessionmaker:
        """Get or create the session factory."""
        if self._session_factory is None:
            self._session_factory = create_session_factory(self.engine)
            logger.info("Database session factory created")
        return self._session_factory
    
    def initialize_database(self) -> None:
        """
        Initialize the database by creating all tables.
        
        This method is safe to call multiple times - it will only create
        tables that don't already exist.
        """
        try:
            init_database(self.engine)
            logger.info("Database initialized successfully")
        except SQLAlchemyError as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    def check_database_exists(self) -> bool:
        """
        Check if the database file exists (for SQLite).
        
        Returns:
            True if database exists, False otherwise
        """
        if self.database_url.startswith("sqlite:///"):
            db_path = self.database_url.replace("sqlite:///", "")
            return Path(db_path).exists()
        return True  # For non-file databases, assume they exist
    
    def get_database_info(self) -> dict:
        """
        Get information about the database.
        
        Returns:
            Dictionary containing database information
        """
        info = {
            "database_url": self.database_url,
            "database_type": "SQLite" if "sqlite" in self.database_url else "Other",
            "echo_enabled": self.echo,
        }
        
        if self.database_url.startswith("sqlite:///"):
            db_path = self.database_url.replace("sqlite:///", "")
            path_obj = Path(db_path)
            info.update({
                "database_file": str(path_obj.absolute()),
                "database_exists": path_obj.exists(),
                "database_size_bytes": path_obj.stat().st_size if path_obj.exists() else 0,
            })
        
        return info
    
    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """
        Context manager for database sessions.
        
        Provides automatic session management with proper cleanup and error handling.
        
        Yields:
            SQLAlchemy session instance
            
        Example:
            with db_manager.get_session() as session:
                roadmap = session.query(Roadmap).first()
        """
        session = self.session_factory()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            session.close()
    
    def create_session(self) -> Session:
        """
        Create a new database session.
        
        Note: When using this method, you must manually manage the session
        lifecycle (commit/rollback/close). Consider using get_session() 
        context manager instead.
        
        Returns:
            SQLAlchemy session instance
        """
        return self.session_factory()
    
    def reset_database(self) -> None:
        """
        Reset the database by dropping and recreating all tables.
        
        WARNING: This will delete all data in the database!
        """
        try:
            Base.metadata.drop_all(bind=self.engine)
            Base.metadata.create_all(bind=self.engine)
            logger.warning("Database reset completed - all data deleted!")
        except SQLAlchemyError as e:
            logger.error(f"Failed to reset database: {e}")
            raise
    
    def get_table_counts(self) -> dict:
        """
        Get the count of records in each table.
        
        Returns:
            Dictionary with table names as keys and record counts as values
        """
        counts = {}
        try:
            with self.get_session() as session:
                counts["roadmaps"] = session.query(Roadmap).count()
                counts["releases"] = session.query(Release).count()
                counts["tasks"] = session.query(Task).count()
                counts["subtasks"] = session.query(Subtask).count()
        except SQLAlchemyError as e:
            logger.error(f"Failed to get table counts: {e}")
            raise
        
        return counts
    
    def close(self) -> None:
        """Close the database engine and clean up resources."""
        if self._engine:
            self._engine.dispose()
            self._engine = None
            self._session_factory = None
            logger.info("Database connection closed")


# Global database manager instance
_db_manager: Optional[DatabaseManager] = None


def get_database_manager(database_url: Optional[str] = None, echo: bool = False) -> DatabaseManager:
    """
    Get the global database manager instance.
    
    Args:
        database_url: Optional custom database URL
        echo: Whether to echo SQL queries for debugging
        
    Returns:
        DatabaseManager instance
    """
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager(database_url=database_url, echo=echo)
    return _db_manager


def initialize_task_manager_database(database_url: Optional[str] = None, echo: bool = False) -> DatabaseManager:
    """
    Initialize the task manager database.
    
    This is the main entry point for setting up the database for the task manager.
    
    Args:
        database_url: Optional custom database URL (defaults to local SQLite)
        echo: Whether to echo SQL queries for debugging
        
    Returns:
        Initialized DatabaseManager instance
    """
    db_manager = get_database_manager(database_url=database_url, echo=echo)
    db_manager.initialize_database()
    
    logger.info("Task manager database initialized successfully")
    logger.info(f"Database info: {db_manager.get_database_info()}")
    
    return db_manager


# Export commonly used items
__all__ = [
    "DatabaseManager",
    "get_database_manager", 
    "initialize_task_manager_database",
]
