#!/usr/bin/env python3
"""
Database utility functions for the Task Manager MCP Server.

This module provides common database operations, helper functions,
and utilities for working with the task management entities.
"""

import logging
from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import and_, or_, desc, asc

from .models import (
    Project,
    Roadmap,
    Release,
    Task,
    Subtask,
    StatusEnum,
    PriorityEnum
)

logger = logging.getLogger(__name__)


class DatabaseOperationError(Exception):
    """Custom exception for database operation errors."""
    pass


def calculate_progress_percentage(total_items: int, completed_items: int) -> float:
    """
    Calculate progress percentage.

    Args:
        total_items: Total number of items
        completed_items: Number of completed items

    Returns:
        Progress percentage (0.0 to 100.0)
    """
    if total_items == 0:
        return 0.0
    return round((completed_items / total_items) * 100.0, 2)


def get_project_progress(session: Session, project_id: int) -> Dict[str, Any]:
    """
    Calculate comprehensive progress for a project.

    Args:
        session: Database session
        project_id: ID of the project

    Returns:
        Dictionary containing progress information
    """
    try:
        project = session.query(Project).filter(Project.id == project_id).first()
        if not project:
            raise DatabaseOperationError(f"Project with ID {project_id} not found")

        # Count roadmaps
        total_roadmaps = len(project.roadmaps)
        completed_roadmaps = sum(1 for r in project.roadmaps if r.status == StatusEnum.COMPLETED)

        # Count releases across all roadmaps
        total_releases = sum(len(r.releases) for r in project.roadmaps)
        completed_releases = sum(
            sum(1 for rel in r.releases if rel.status == StatusEnum.COMPLETED)
            for r in project.roadmaps
        )

        # Count tasks across all releases
        total_tasks = sum(
            sum(len(rel.tasks) for rel in r.releases)
            for r in project.roadmaps
        )
        completed_tasks = sum(
            sum(
                sum(1 for t in rel.tasks if t.status == StatusEnum.COMPLETED)
                for rel in r.releases
            )
            for r in project.roadmaps
        )

        # Count subtasks across all tasks
        total_subtasks = sum(
            sum(
                sum(len(t.subtasks) for t in rel.tasks)
                for rel in r.releases
            )
            for r in project.roadmaps
        )
        completed_subtasks = sum(
            sum(
                sum(
                    sum(1 for st in t.subtasks if st.is_completed)
                    for t in rel.tasks
                )
                for rel in r.releases
            )
            for r in project.roadmaps
        )

        return {
            "project_id": project_id,
            "project_name": project.name,
            "project_status": project.status,
            "roadmaps": {
                "total": total_roadmaps,
                "completed": completed_roadmaps,
                "progress_percentage": calculate_progress_percentage(total_roadmaps, completed_roadmaps)
            },
            "releases": {
                "total": total_releases,
                "completed": completed_releases,
                "progress_percentage": calculate_progress_percentage(total_releases, completed_releases)
            },
            "tasks": {
                "total": total_tasks,
                "completed": completed_tasks,
                "progress_percentage": calculate_progress_percentage(total_tasks, completed_tasks)
            },
            "subtasks": {
                "total": total_subtasks,
                "completed": completed_subtasks,
                "progress_percentage": calculate_progress_percentage(total_subtasks, completed_subtasks)
            },
            "overall_progress_percentage": calculate_progress_percentage(
                total_subtasks if total_subtasks > 0 else total_tasks,
                completed_subtasks if total_subtasks > 0 else completed_tasks
            )
        }
    except SQLAlchemyError as e:
        logger.error(f"Database error calculating project progress: {e}")
        raise DatabaseOperationError(f"Failed to calculate project progress: {e}")


def get_roadmap_progress(session: Session, roadmap_id: int) -> Dict[str, Any]:
    """
    Calculate comprehensive progress for a roadmap.
    
    Args:
        session: Database session
        roadmap_id: ID of the roadmap
        
    Returns:
        Dictionary containing progress information
    """
    try:
        roadmap = session.query(Roadmap).filter(Roadmap.id == roadmap_id).first()
        if not roadmap:
            raise DatabaseOperationError(f"Roadmap with ID {roadmap_id} not found")
        
        # Count releases
        total_releases = len(roadmap.releases)
        completed_releases = sum(1 for r in roadmap.releases if r.status == StatusEnum.COMPLETED)
        
        # Count tasks across all releases
        total_tasks = sum(len(r.tasks) for r in roadmap.releases)
        completed_tasks = sum(
            sum(1 for t in r.tasks if t.status == StatusEnum.COMPLETED) 
            for r in roadmap.releases
        )
        
        # Count subtasks across all tasks
        total_subtasks = sum(
            sum(len(t.subtasks) for t in r.tasks) 
            for r in roadmap.releases
        )
        completed_subtasks = sum(
            sum(sum(1 for st in t.subtasks if st.is_completed) for t in r.tasks)
            for r in roadmap.releases
        )
        
        return {
            "roadmap_id": roadmap_id,
            "roadmap_title": roadmap.title,
            "roadmap_status": roadmap.status,
            "releases": {
                "total": total_releases,
                "completed": completed_releases,
                "progress_percentage": calculate_progress_percentage(total_releases, completed_releases)
            },
            "tasks": {
                "total": total_tasks,
                "completed": completed_tasks,
                "progress_percentage": calculate_progress_percentage(total_tasks, completed_tasks)
            },
            "subtasks": {
                "total": total_subtasks,
                "completed": completed_subtasks,
                "progress_percentage": calculate_progress_percentage(total_subtasks, completed_subtasks)
            },
            "overall_progress_percentage": calculate_progress_percentage(
                total_subtasks if total_subtasks > 0 else total_tasks,
                completed_subtasks if total_subtasks > 0 else completed_tasks
            )
        }
    except SQLAlchemyError as e:
        logger.error(f"Database error calculating roadmap progress: {e}")
        raise DatabaseOperationError(f"Failed to calculate roadmap progress: {e}")


def get_release_progress(session: Session, release_id: int) -> Dict[str, Any]:
    """
    Calculate comprehensive progress for a release.
    
    Args:
        session: Database session
        release_id: ID of the release
        
    Returns:
        Dictionary containing progress information
    """
    try:
        release = session.query(Release).filter(Release.id == release_id).first()
        if not release:
            raise DatabaseOperationError(f"Release with ID {release_id} not found")
        
        # Count tasks
        total_tasks = len(release.tasks)
        completed_tasks = sum(1 for t in release.tasks if t.status == StatusEnum.COMPLETED)
        
        # Count subtasks
        total_subtasks = sum(len(t.subtasks) for t in release.tasks)
        completed_subtasks = sum(
            sum(1 for st in t.subtasks if st.is_completed) 
            for t in release.tasks
        )
        
        return {
            "release_id": release_id,
            "release_title": release.title,
            "release_status": release.status,
            "roadmap_id": release.roadmap_id,
            "tasks": {
                "total": total_tasks,
                "completed": completed_tasks,
                "progress_percentage": calculate_progress_percentage(total_tasks, completed_tasks)
            },
            "subtasks": {
                "total": total_subtasks,
                "completed": completed_subtasks,
                "progress_percentage": calculate_progress_percentage(total_subtasks, completed_subtasks)
            },
            "overall_progress_percentage": calculate_progress_percentage(
                total_subtasks if total_subtasks > 0 else total_tasks,
                completed_subtasks if total_subtasks > 0 else completed_tasks
            )
        }
    except SQLAlchemyError as e:
        logger.error(f"Database error calculating release progress: {e}")
        raise DatabaseOperationError(f"Failed to calculate release progress: {e}")


def get_task_progress(session: Session, task_id: int) -> Dict[str, Any]:
    """
    Calculate comprehensive progress for a task.
    
    Args:
        session: Database session
        task_id: ID of the task
        
    Returns:
        Dictionary containing progress information
    """
    try:
        task = session.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise DatabaseOperationError(f"Task with ID {task_id} not found")
        
        # Count subtasks
        total_subtasks = len(task.subtasks)
        completed_subtasks = sum(1 for st in task.subtasks if st.is_completed)
        
        return {
            "task_id": task_id,
            "task_title": task.title,
            "task_status": task.status,
            "release_id": task.release_id,
            "subtasks": {
                "total": total_subtasks,
                "completed": completed_subtasks,
                "progress_percentage": calculate_progress_percentage(total_subtasks, completed_subtasks)
            },
            "overall_progress_percentage": calculate_progress_percentage(total_subtasks, completed_subtasks)
        }
    except SQLAlchemyError as e:
        logger.error(f"Database error calculating task progress: {e}")
        raise DatabaseOperationError(f"Failed to calculate task progress: {e}")


def update_entity_status(session: Session, entity_type: str, entity_id: int, new_status: StatusEnum) -> bool:
    """
    Update the status of any entity and handle cascading updates.

    Args:
        session: Database session
        entity_type: Type of entity ('project', 'roadmap', 'release', 'task', 'subtask')
        entity_id: ID of the entity
        new_status: New status to set

    Returns:
        True if update was successful, False otherwise
    """
    try:
        entity_map = {
            'project': Project,
            'roadmap': Roadmap,
            'release': Release,
            'task': Task,
            'subtask': Subtask
        }
        
        if entity_type not in entity_map:
            raise DatabaseOperationError(f"Invalid entity type: {entity_type}")
        
        entity_class = entity_map[entity_type]
        entity = session.query(entity_class).filter(entity_class.id == entity_id).first()
        
        if not entity:
            raise DatabaseOperationError(f"{entity_type.title()} with ID {entity_id} not found")
        
        # Update the entity status
        entity.status = new_status
        entity.updated_at = datetime.utcnow()
        
        # Handle completion date for subtasks
        if entity_type == 'subtask' and new_status == StatusEnum.COMPLETED:
            entity.is_completed = True
            entity.completed_at = datetime.utcnow()
        elif entity_type == 'subtask' and new_status != StatusEnum.COMPLETED:
            entity.is_completed = False
            entity.completed_at = None
        
        # Handle actual end date for other entities
        if entity_type in ['project', 'roadmap', 'release', 'task'] and new_status == StatusEnum.COMPLETED:
            entity.actual_end_date = datetime.utcnow()
        elif entity_type in ['project', 'roadmap', 'release', 'task'] and new_status != StatusEnum.COMPLETED:
            entity.actual_end_date = None
        
        session.commit()
        logger.info(f"Updated {entity_type} {entity_id} status to {new_status}")
        return True
        
    except SQLAlchemyError as e:
        session.rollback()
        logger.error(f"Database error updating {entity_type} status: {e}")
        raise DatabaseOperationError(f"Failed to update {entity_type} status: {e}")


def search_entities(
    session: Session,
    entity_type: str,
    search_term: Optional[str] = None,
    status_filter: Optional[StatusEnum] = None,
    priority_filter: Optional[PriorityEnum] = None,
    assignee_filter: Optional[str] = None,
    project_id: Optional[int] = None,
    limit: int = 100
) -> List[Any]:
    """
    Search and filter entities based on various criteria.

    Args:
        session: Database session
        entity_type: Type of entity to search ('project', 'roadmap', 'release', 'task', 'subtask')
        search_term: Optional search term for title/description/name
        status_filter: Optional status filter
        priority_filter: Optional priority filter (tasks/subtasks only)
        assignee_filter: Optional assignee filter (tasks/subtasks only)
        project_id: Optional project filter (for roadmaps, releases, tasks, subtasks)
        limit: Maximum number of results to return

    Returns:
        List of matching entities
    """
    try:
        entity_map = {
            'project': Project,
            'roadmap': Roadmap,
            'release': Release,
            'task': Task,
            'subtask': Subtask
        }
        
        if entity_type not in entity_map:
            raise DatabaseOperationError(f"Invalid entity type: {entity_type}")
        
        entity_class = entity_map[entity_type]
        query = session.query(entity_class)
        
        # Apply search term filter
        if search_term:
            if entity_type == 'project':
                # Projects have 'name' instead of 'title'
                search_filter = or_(
                    entity_class.name.ilike(f"%{search_term}%"),
                    entity_class.description.ilike(f"%{search_term}%")
                )
            else:
                search_filter = or_(
                    entity_class.title.ilike(f"%{search_term}%"),
                    entity_class.description.ilike(f"%{search_term}%")
                )
            query = query.filter(search_filter)

        # Apply project filter (for non-project entities)
        if project_id and entity_type != 'project':
            if entity_type == 'roadmap':
                query = query.filter(entity_class.project_id == project_id)
            elif entity_type == 'release':
                query = query.join(Roadmap).filter(Roadmap.project_id == project_id)
            elif entity_type in ['task', 'subtask']:
                if entity_type == 'task':
                    query = query.join(Release).join(Roadmap).filter(Roadmap.project_id == project_id)
                else:  # subtask
                    query = query.join(Task).join(Release).join(Roadmap).filter(Roadmap.project_id == project_id)
        
        # Apply status filter
        if status_filter:
            query = query.filter(entity_class.status == status_filter)
        
        # Apply priority filter (only for tasks and subtasks)
        if priority_filter and entity_type in ['task', 'subtask']:
            query = query.filter(entity_class.priority == priority_filter)
        
        # Apply assignee filter (only for tasks and subtasks)
        if assignee_filter and entity_type in ['task', 'subtask']:
            query = query.filter(entity_class.assignee.ilike(f"%{assignee_filter}%"))
        
        # Order by updated_at descending
        query = query.order_by(desc(entity_class.updated_at))
        
        # Apply limit
        query = query.limit(limit)
        
        results = query.all()
        logger.info(f"Found {len(results)} {entity_type}s matching search criteria")
        return results
        
    except SQLAlchemyError as e:
        logger.error(f"Database error searching {entity_type}s: {e}")
        raise DatabaseOperationError(f"Failed to search {entity_type}s: {e}")


def get_entity_hierarchy(session: Session, entity_type: str, entity_id: int) -> Dict[str, Any]:
    """
    Get the full hierarchy for an entity (parent and children).

    Args:
        session: Database session
        entity_type: Type of entity ('project', 'roadmap', 'release', 'task', 'subtask')
        entity_id: ID of the entity

    Returns:
        Dictionary containing the entity and its hierarchy
    """
    try:
        if entity_type == 'project':
            project = session.query(Project).filter(Project.id == entity_id).first()
            if not project:
                raise DatabaseOperationError(f"Project with ID {entity_id} not found")

            return {
                "project": project,
                "roadmaps": project.roadmaps,
                "total_releases": sum(len(r.releases) for r in project.roadmaps),
                "total_tasks": sum(
                    sum(len(rel.tasks) for rel in r.releases)
                    for r in project.roadmaps
                ),
                "total_subtasks": sum(
                    sum(
                        sum(len(t.subtasks) for t in rel.tasks)
                        for rel in r.releases
                    )
                    for r in project.roadmaps
                )
            }

        elif entity_type == 'roadmap':
            roadmap = session.query(Roadmap).filter(Roadmap.id == entity_id).first()
            if not roadmap:
                raise DatabaseOperationError(f"Roadmap with ID {entity_id} not found")
            
            return {
                "roadmap": roadmap,
                "releases": roadmap.releases,
                "total_tasks": sum(len(r.tasks) for r in roadmap.releases),
                "total_subtasks": sum(sum(len(t.subtasks) for t in r.tasks) for r in roadmap.releases)
            }
            
        elif entity_type == 'release':
            release = session.query(Release).filter(Release.id == entity_id).first()
            if not release:
                raise DatabaseOperationError(f"Release with ID {entity_id} not found")
            
            return {
                "project": release.roadmap.project,
                "roadmap": release.roadmap,
                "release": release,
                "tasks": release.tasks,
                "total_subtasks": sum(len(t.subtasks) for t in release.tasks)
            }

        elif entity_type == 'task':
            task = session.query(Task).filter(Task.id == entity_id).first()
            if not task:
                raise DatabaseOperationError(f"Task with ID {entity_id} not found")

            return {
                "project": task.release.roadmap.project,
                "roadmap": task.release.roadmap,
                "release": task.release,
                "task": task,
                "subtasks": task.subtasks
            }

        elif entity_type == 'subtask':
            subtask = session.query(Subtask).filter(Subtask.id == entity_id).first()
            if not subtask:
                raise DatabaseOperationError(f"Subtask with ID {entity_id} not found")

            return {
                "project": subtask.task.release.roadmap.project,
                "roadmap": subtask.task.release.roadmap,
                "release": subtask.task.release,
                "task": subtask.task,
                "subtask": subtask
            }
            
        else:
            raise DatabaseOperationError(f"Invalid entity type: {entity_type}")
            
    except SQLAlchemyError as e:
        logger.error(f"Database error getting entity hierarchy: {e}")
        raise DatabaseOperationError(f"Failed to get entity hierarchy: {e}")


# Export commonly used items
__all__ = [
    "DatabaseOperationError",
    "calculate_progress_percentage",
    "get_project_progress",
    "get_roadmap_progress",
    "get_release_progress",
    "get_task_progress",
    "update_entity_status",
    "search_entities",
    "get_entity_hierarchy"
]
