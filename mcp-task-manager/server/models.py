#!/usr/bin/env python3
"""
Database models for the Task Manager MCP Server.

This module defines SQLAlchemy models for the hierarchical task management system:
- Roadmaps: High-level strategic plans
- Releases: Specific milestones within roadmaps  
- Tasks: Individual work items within releases
- Subtasks: Granular work items within tasks

All models use SQLite as the local database backend.
"""

from datetime import datetime
from enum import Enum
from typing import List, Optional

from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    Enum as SQLEnum,
    ForeignKey,
    Integer,
    String,
    Text,
    Float,
    create_engine,
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, sessionmaker
from sqlalchemy.sql import func

Base = declarative_base()


class StatusEnum(str, Enum):
    """Status enumeration for all entities."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    ON_HOLD = "on_hold"
    CANCELLED = "cancelled"


class PriorityEnum(str, Enum):
    """Priority enumeration for tasks and subtasks."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class Project(Base):
    """
    Project model representing the top-level organizational unit.

    A project contains multiple roadmaps and provides the highest-level
    organizational structure for the task management system.
    """
    __tablename__ = "projects"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255), nullable=False, index=True, unique=True)
    description = Column(Text, nullable=True)
    status = Column(SQLEnum(StatusEnum), default=StatusEnum.NOT_STARTED, nullable=False)
    start_date = Column(DateTime, nullable=True)
    target_end_date = Column(DateTime, nullable=True)
    actual_end_date = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    roadmaps = relationship("Roadmap", back_populates="project", cascade="all, delete-orphan")

    def __repr__(self) -> str:
        return f"<Project(id={self.id}, name='{self.name}', status='{self.status}')>"


class Roadmap(Base):
    """
    Roadmap model representing high-level strategic plans.

    A roadmap belongs to a project and contains multiple releases.
    """
    __tablename__ = "roadmaps"

    id = Column(Integer, primary_key=True, autoincrement=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    title = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    status = Column(SQLEnum(StatusEnum), default=StatusEnum.NOT_STARTED, nullable=False)
    start_date = Column(DateTime, nullable=True)
    target_end_date = Column(DateTime, nullable=True)
    actual_end_date = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    project = relationship("Project", back_populates="roadmaps")
    releases = relationship("Release", back_populates="roadmap", cascade="all, delete-orphan")

    def __repr__(self) -> str:
        return f"<Roadmap(id={self.id}, title='{self.title}', status='{self.status}')>"


class Release(Base):
    """
    Release model representing specific milestones within roadmaps.
    
    A release belongs to a roadmap and contains multiple tasks.
    Releases represent deliverable milestones or versions.
    """
    __tablename__ = "releases"

    id = Column(Integer, primary_key=True, autoincrement=True)
    roadmap_id = Column(Integer, ForeignKey("roadmaps.id"), nullable=False)
    title = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    version = Column(String(50), nullable=True)
    status = Column(SQLEnum(StatusEnum), default=StatusEnum.NOT_STARTED, nullable=False)
    start_date = Column(DateTime, nullable=True)
    target_end_date = Column(DateTime, nullable=True)
    actual_end_date = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    roadmap = relationship("Roadmap", back_populates="releases")
    tasks = relationship("Task", back_populates="release", cascade="all, delete-orphan")

    def __repr__(self) -> str:
        return f"<Release(id={self.id}, title='{self.title}', version='{self.version}', status='{self.status}')>"


class Task(Base):
    """
    Task model representing individual work items within releases.
    
    A task belongs to a release and can contain multiple subtasks.
    Tasks represent specific work items that need to be completed.
    """
    __tablename__ = "tasks"

    id = Column(Integer, primary_key=True, autoincrement=True)
    release_id = Column(Integer, ForeignKey("releases.id"), nullable=False)
    title = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    status = Column(SQLEnum(StatusEnum), default=StatusEnum.NOT_STARTED, nullable=False)
    priority = Column(SQLEnum(PriorityEnum), default=PriorityEnum.MEDIUM, nullable=False)
    assignee = Column(String(100), nullable=True)
    estimated_hours = Column(Float, nullable=True)
    actual_hours = Column(Float, nullable=True)
    start_date = Column(DateTime, nullable=True)
    target_end_date = Column(DateTime, nullable=True)
    actual_end_date = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    release = relationship("Release", back_populates="tasks")
    subtasks = relationship("Subtask", back_populates="task", cascade="all, delete-orphan")

    def __repr__(self) -> str:
        return f"<Task(id={self.id}, title='{self.title}', status='{self.status}', priority='{self.priority}')>"


class Subtask(Base):
    """
    Subtask model representing granular work items within tasks.
    
    A subtask belongs to a task and represents the most granular level
    of work breakdown in the task management system.
    """
    __tablename__ = "subtasks"

    id = Column(Integer, primary_key=True, autoincrement=True)
    task_id = Column(Integer, ForeignKey("tasks.id"), nullable=False)
    title = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    status = Column(SQLEnum(StatusEnum), default=StatusEnum.NOT_STARTED, nullable=False)
    priority = Column(SQLEnum(PriorityEnum), default=PriorityEnum.MEDIUM, nullable=False)
    assignee = Column(String(100), nullable=True)
    estimated_hours = Column(Float, nullable=True)
    actual_hours = Column(Float, nullable=True)
    is_completed = Column(Boolean, default=False, nullable=False)
    completed_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    task = relationship("Task", back_populates="subtasks")

    def __repr__(self) -> str:
        return f"<Subtask(id={self.id}, title='{self.title}', status='{self.status}', completed={self.is_completed})>"


# Database configuration for SQLite (local database)
DATABASE_URL = "sqlite:///./task_manager.db"

def create_database_engine():
    """
    Create and configure the SQLite database engine.
    
    Returns:
        SQLAlchemy engine configured for SQLite with local file storage.
    """
    engine = create_engine(
        DATABASE_URL,
        echo=False,  # Set to True for SQL query logging during development
        connect_args={"check_same_thread": False}  # Required for SQLite with threading
    )
    return engine


def create_session_factory(engine):
    """
    Create a session factory for database operations.
    
    Args:
        engine: SQLAlchemy engine instance
        
    Returns:
        SQLAlchemy sessionmaker factory
    """
    return sessionmaker(autocommit=False, autoflush=False, bind=engine)


def init_database(engine):
    """
    Initialize the database by creating all tables.
    
    Args:
        engine: SQLAlchemy engine instance
    """
    Base.metadata.create_all(bind=engine)


# Export commonly used items
__all__ = [
    "Base",
    "StatusEnum",
    "PriorityEnum",
    "Project",
    "Roadmap",
    "Release",
    "Task",
    "Subtask",
    "create_database_engine",
    "create_session_factory",
    "init_database",
    "DATABASE_URL"
]
