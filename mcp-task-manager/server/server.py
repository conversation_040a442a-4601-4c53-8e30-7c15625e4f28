#!/usr/bin/env python3
"""
Task Manager MCP Server

A comprehensive task management MCP server with multi-project support.
Provides tools for managing projects, roadmaps, releases, tasks, and subtasks.
"""

import os
import sys
import argparse
import platform
import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path

from mcp.server.fastmcp import FastMC<PERSON>
from .database import get_database_manager
from .models import StatusEnum, PriorityEnum, Project, Roadmap, Release, Task, Subtask
from .db_utils import (
    get_project_progress,
    get_roadmap_progress,
    get_release_progress,
    get_task_progress,
    update_entity_status,
    search_entities,
    get_entity_hierarchy,
    DatabaseOperationError
)


class TaskManagerMCPServer:
    """
    A comprehensive task management MCP server with multi-project support.

    This server provides tools for managing projects, roadmaps, releases, tasks, and subtasks
    in a hierarchical structure with local SQLite database storage.
    """

    def __init__(self, host: str = "127.0.0.1", port: int = 8000, path: str = "/mcp"):
        """
        Initialize the Task Manager MCP server.

        Args:
            host: Host to run the server on
            port: Port to run the server on
            path: Path for the MCP endpoint
        """
        self.host = host
        self.port = port
        self.path = path

        # Initialize the database
        self.db_manager = get_database_manager()
        self.db_manager.initialize_database()

        # Initialize the MCP server
        self.mcp = FastMCP(
            name="Task Manager MCP Server",
            instructions="A comprehensive task management server supporting projects, roadmaps, releases, tasks, and subtasks with hierarchical organization and progress tracking.",
        )

        # Register tools
        self._register_tools()

    def _register_tools(self):
        """Register task management tools with the MCP server."""

        # Project Management Tools
        @self.mcp.tool()
        def create_project(name: str, description: Optional[str] = None,
                          status: str = "not_started") -> Dict[str, Any]:
            """
            Create a new project.

            Args:
                name: Project name (must be unique)
                description: Optional project description
                status: Project status (not_started, in_progress, completed, on_hold, cancelled)

            Returns:
                Dictionary containing the created project information.
            """
            try:
                # Validate status
                try:
                    status_enum = StatusEnum(status)
                except ValueError:
                    return {"error": f"Invalid status: {status}. Valid values: {[s.value for s in StatusEnum]}"}

                with self.db_manager.get_session() as session:
                    # Check if project name already exists
                    existing = session.query(Project).filter(Project.name == name).first()
                    if existing:
                        return {"error": f"Project with name '{name}' already exists"}

                    # Create new project
                    project = Project(
                        name=name,
                        description=description,
                        status=status_enum
                    )
                    session.add(project)
                    session.commit()
                    session.refresh(project)

                    return {
                        "success": True,
                        "project": {
                            "id": project.id,
                            "name": project.name,
                            "description": project.description,
                            "status": project.status.value,
                            "created_at": project.created_at.isoformat(),
                            "updated_at": project.updated_at.isoformat()
                        }
                    }
            except Exception as e:
                return {"error": f"Failed to create project: {str(e)}"}

        @self.mcp.tool()
        def list_projects(status_filter: Optional[str] = None) -> Dict[str, Any]:
            """
            List all projects with optional status filtering.

            Args:
                status_filter: Optional status filter (not_started, in_progress, completed, on_hold, cancelled)

            Returns:
                Dictionary containing list of projects.
            """
            try:
                status_enum = None
                if status_filter:
                    try:
                        status_enum = StatusEnum(status_filter)
                    except ValueError:
                        return {"error": f"Invalid status filter: {status_filter}. Valid values: {[s.value for s in StatusEnum]}"}

                with self.db_manager.get_session() as session:
                    projects = search_entities(
                        session=session,
                        entity_type='project',
                        status_filter=status_enum,
                        limit=100
                    )

                    project_list = []
                    for project in projects:
                        # Get basic progress info
                        progress = get_project_progress(session, project.id)
                        project_list.append({
                            "id": project.id,
                            "name": project.name,
                            "description": project.description,
                            "status": project.status.value,
                            "roadmaps_count": progress["roadmaps"]["total"],
                            "overall_progress": progress["overall_progress_percentage"],
                            "created_at": project.created_at.isoformat(),
                            "updated_at": project.updated_at.isoformat()
                        })

                    return {
                        "success": True,
                        "projects": project_list,
                        "total_count": len(project_list)
                    }
            except Exception as e:
                return {"error": f"Failed to list projects: {str(e)}"}

        @self.mcp.tool()
        def get_project(project_id: int) -> Dict[str, Any]:
            """
            Get detailed project information with progress.

            Args:
                project_id: ID of the project to retrieve

            Returns:
                Dictionary containing detailed project information and progress.
            """
            try:
                with self.db_manager.get_session() as session:
                    project = session.query(Project).filter(Project.id == project_id).first()
                    if not project:
                        return {"error": f"Project with ID {project_id} not found"}

                    # Get comprehensive progress
                    progress = get_project_progress(session, project_id)

                    # Get hierarchy info
                    hierarchy = get_entity_hierarchy(session, 'project', project_id)

                    return {
                        "success": True,
                        "project": {
                            "id": project.id,
                            "name": project.name,
                            "description": project.description,
                            "status": project.status.value,
                            "start_date": project.start_date.isoformat() if project.start_date else None,
                            "target_end_date": project.target_end_date.isoformat() if project.target_end_date else None,
                            "actual_end_date": project.actual_end_date.isoformat() if project.actual_end_date else None,
                            "created_at": project.created_at.isoformat(),
                            "updated_at": project.updated_at.isoformat()
                        },
                        "progress": progress,
                        "hierarchy": {
                            "roadmaps_count": hierarchy["total_releases"],
                            "releases_count": hierarchy["total_releases"],
                            "tasks_count": hierarchy["total_tasks"],
                            "subtasks_count": hierarchy["total_subtasks"]
                        }
                    }
            except Exception as e:
                return {"error": f"Failed to get project: {str(e)}"}

        @self.mcp.tool()
        def update_project(project_id: int, name: Optional[str] = None,
                          description: Optional[str] = None, status: Optional[str] = None,
                          start_date: Optional[str] = None, target_end_date: Optional[str] = None) -> Dict[str, Any]:
            """
            Update project information.

            Args:
                project_id: ID of the project to update
                name: New project name (optional)
                description: New project description (optional)
                status: New project status (optional)
                start_date: New start date in ISO format (optional)
                target_end_date: New target end date in ISO format (optional)

            Returns:
                Dictionary containing the updated project information.
            """
            try:
                with self.db_manager.get_session() as session:
                    project = session.query(Project).filter(Project.id == project_id).first()
                    if not project:
                        return {"error": f"Project with ID {project_id} not found"}

                    # Update fields if provided
                    if name is not None:
                        # Check for name uniqueness
                        existing = session.query(Project).filter(
                            Project.name == name,
                            Project.id != project_id
                        ).first()
                        if existing:
                            return {"error": f"Project with name '{name}' already exists"}
                        project.name = name

                    if description is not None:
                        project.description = description

                    if status is not None:
                        try:
                            status_enum = StatusEnum(status)
                            project.status = status_enum
                        except ValueError:
                            return {"error": f"Invalid status: {status}. Valid values: {[s.value for s in StatusEnum]}"}

                    if start_date is not None:
                        try:
                            project.start_date = datetime.datetime.fromisoformat(start_date.replace('Z', '+00:00'))
                        except ValueError:
                            return {"error": "Invalid start_date format. Use ISO format (YYYY-MM-DDTHH:MM:SS)"}

                    if target_end_date is not None:
                        try:
                            project.target_end_date = datetime.datetime.fromisoformat(target_end_date.replace('Z', '+00:00'))
                        except ValueError:
                            return {"error": "Invalid target_end_date format. Use ISO format (YYYY-MM-DDTHH:MM:SS)"}

                    session.commit()
                    session.refresh(project)

                    return {
                        "success": True,
                        "project": {
                            "id": project.id,
                            "name": project.name,
                            "description": project.description,
                            "status": project.status.value,
                            "start_date": project.start_date.isoformat() if project.start_date else None,
                            "target_end_date": project.target_end_date.isoformat() if project.target_end_date else None,
                            "actual_end_date": project.actual_end_date.isoformat() if project.actual_end_date else None,
                            "created_at": project.created_at.isoformat(),
                            "updated_at": project.updated_at.isoformat()
                        }
                    }
            except Exception as e:
                return {"error": f"Failed to update project: {str(e)}"}

        @self.mcp.tool()
        def delete_project(project_id: int) -> Dict[str, Any]:
            """
            Delete a project and all its associated data.

            Args:
                project_id: ID of the project to delete

            Returns:
                Dictionary confirming deletion.
            """
            try:
                with self.db_manager.get_session() as session:
                    project = session.query(Project).filter(Project.id == project_id).first()
                    if not project:
                        return {"error": f"Project with ID {project_id} not found"}

                    project_name = project.name
                    session.delete(project)
                    session.commit()

                    return {
                        "success": True,
                        "message": f"Project '{project_name}' and all associated data deleted successfully"
                    }
            except Exception as e:
                return {"error": f"Failed to delete project: {str(e)}"}

        # Roadmap Management Tools
        @self.mcp.tool()
        def create_roadmap(project_id: int, title: str, description: Optional[str] = None,
                          status: str = "not_started") -> Dict[str, Any]:
            """
            Create a new roadmap within a project.

            Args:
                project_id: ID of the parent project
                title: Roadmap title
                description: Optional roadmap description
                status: Roadmap status (not_started, in_progress, completed, on_hold, cancelled)

            Returns:
                Dictionary containing the created roadmap information.
            """
            try:
                # Validate status
                try:
                    status_enum = StatusEnum(status)
                except ValueError:
                    return {"error": f"Invalid status: {status}. Valid values: {[s.value for s in StatusEnum]}"}

                with self.db_manager.get_session() as session:
                    # Check if project exists
                    project = session.query(Project).filter(Project.id == project_id).first()
                    if not project:
                        return {"error": f"Project with ID {project_id} not found"}

                    # Create new roadmap
                    roadmap = Roadmap(
                        project_id=project_id,
                        title=title,
                        description=description,
                        status=status_enum
                    )
                    session.add(roadmap)
                    session.commit()
                    session.refresh(roadmap)

                    return {
                        "success": True,
                        "roadmap": {
                            "id": roadmap.id,
                            "project_id": roadmap.project_id,
                            "title": roadmap.title,
                            "description": roadmap.description,
                            "status": roadmap.status.value,
                            "created_at": roadmap.created_at.isoformat(),
                            "updated_at": roadmap.updated_at.isoformat()
                        }
                    }
            except Exception as e:
                return {"error": f"Failed to create roadmap: {str(e)}"}

        @self.mcp.tool()
        def create_release(roadmap_id: int, title: str, description: Optional[str] = None,
                          version: Optional[str] = None, status: str = "not_started") -> Dict[str, Any]:
            """
            Create a new release within a roadmap.

            Args:
                roadmap_id: ID of the parent roadmap
                title: Release title
                description: Optional release description
                version: Optional version string
                status: Release status (not_started, in_progress, completed, on_hold, cancelled)

            Returns:
                Dictionary containing the created release information.
            """
            try:
                # Validate status
                try:
                    status_enum = StatusEnum(status)
                except ValueError:
                    return {"error": f"Invalid status: {status}. Valid values: {[s.value for s in StatusEnum]}"}

                with self.db_manager.get_session() as session:
                    # Check if roadmap exists
                    roadmap = session.query(Roadmap).filter(Roadmap.id == roadmap_id).first()
                    if not roadmap:
                        return {"error": f"Roadmap with ID {roadmap_id} not found"}

                    # Create new release
                    release = Release(
                        roadmap_id=roadmap_id,
                        title=title,
                        description=description,
                        version=version,
                        status=status_enum
                    )
                    session.add(release)
                    session.commit()
                    session.refresh(release)

                    return {
                        "success": True,
                        "release": {
                            "id": release.id,
                            "roadmap_id": release.roadmap_id,
                            "title": release.title,
                            "description": release.description,
                            "version": release.version,
                            "status": release.status.value,
                            "created_at": release.created_at.isoformat(),
                            "updated_at": release.updated_at.isoformat()
                        }
                    }
            except Exception as e:
                return {"error": f"Failed to create release: {str(e)}"}

        @self.mcp.tool()
        def create_task(release_id: int, title: str, description: Optional[str] = None,
                       priority: str = "medium", estimated_hours: Optional[float] = None,
                       status: str = "not_started") -> Dict[str, Any]:
            """
            Create a new task within a release.

            Args:
                release_id: ID of the parent release
                title: Task title
                description: Optional task description
                priority: Task priority (low, medium, high, critical)
                estimated_hours: Optional estimated hours
                status: Task status (not_started, in_progress, completed, on_hold, cancelled)

            Returns:
                Dictionary containing the created task information.
            """
            try:
                # Validate status and priority
                try:
                    status_enum = StatusEnum(status)
                    priority_enum = PriorityEnum(priority)
                except ValueError as e:
                    return {"error": f"Invalid value: {str(e)}"}

                with self.db_manager.get_session() as session:
                    # Check if release exists
                    release = session.query(Release).filter(Release.id == release_id).first()
                    if not release:
                        return {"error": f"Release with ID {release_id} not found"}

                    # Create new task
                    task = Task(
                        release_id=release_id,
                        title=title,
                        description=description,
                        priority=priority_enum,
                        estimated_hours=estimated_hours,
                        status=status_enum
                    )
                    session.add(task)
                    session.commit()
                    session.refresh(task)

                    return {
                        "success": True,
                        "task": {
                            "id": task.id,
                            "release_id": task.release_id,
                            "title": task.title,
                            "description": task.description,
                            "priority": task.priority.value,
                            "estimated_hours": task.estimated_hours,
                            "status": task.status.value,
                            "created_at": task.created_at.isoformat(),
                            "updated_at": task.updated_at.isoformat()
                        }
                    }
            except Exception as e:
                return {"error": f"Failed to create task: {str(e)}"}

        @self.mcp.tool()
        def update_task(task_id: int, status: Optional[str] = None, description: Optional[str] = None,
                       actual_hours: Optional[float] = None) -> Dict[str, Any]:
            """
            Update task information.

            Args:
                task_id: ID of the task to update
                status: New task status (optional)
                description: New task description (optional)
                actual_hours: Actual hours spent (optional)

            Returns:
                Dictionary containing the updated task information.
            """
            try:
                with self.db_manager.get_session() as session:
                    task = session.query(Task).filter(Task.id == task_id).first()
                    if not task:
                        return {"error": f"Task with ID {task_id} not found"}

                    # Update fields if provided
                    if status is not None:
                        try:
                            status_enum = StatusEnum(status)
                            task.status = status_enum
                        except ValueError:
                            return {"error": f"Invalid status: {status}. Valid values: {[s.value for s in StatusEnum]}"}

                    if description is not None:
                        task.description = description

                    if actual_hours is not None:
                        task.actual_hours = actual_hours

                    session.commit()
                    session.refresh(task)

                    return {
                        "success": True,
                        "task": {
                            "id": task.id,
                            "release_id": task.release_id,
                            "title": task.title,
                            "description": task.description,
                            "priority": task.priority.value,
                            "estimated_hours": task.estimated_hours,
                            "actual_hours": task.actual_hours,
                            "status": task.status.value,
                            "updated_at": task.updated_at.isoformat()
                        }
                    }
            except Exception as e:
                return {"error": f"Failed to update task: {str(e)}"}

        @self.mcp.tool()
        def get_progress_summary(project_id: int) -> Dict[str, Any]:
            """
            Get comprehensive progress summary for a project.

            Args:
                project_id: ID of the project to get progress for

            Returns:
                Dictionary containing detailed progress information.
            """
            try:
                with self.db_manager.get_session() as session:
                    project = session.query(Project).filter(Project.id == project_id).first()
                    if not project:
                        return {"error": f"Project with ID {project_id} not found"}

                    # Get all tasks in the project
                    tasks = session.query(Task).join(Release).join(Roadmap).filter(
                        Roadmap.project_id == project_id
                    ).all()

                    if not tasks:
                        return {
                            "success": True,
                            "project_id": project_id,
                            "project_name": project.name,
                            "overall_progress": 0.0,
                            "total_tasks": 0,
                            "completed_tasks": 0,
                            "in_progress_tasks": 0,
                            "on_hold_tasks": 0,
                            "not_started_tasks": 0,
                            "task_details": []
                        }

                    # Count tasks by status
                    completed_tasks = sum(1 for task in tasks if task.status == StatusEnum.COMPLETED)
                    in_progress_tasks = sum(1 for task in tasks if task.status == StatusEnum.IN_PROGRESS)
                    on_hold_tasks = sum(1 for task in tasks if task.status == StatusEnum.ON_HOLD)
                    not_started_tasks = sum(1 for task in tasks if task.status == StatusEnum.NOT_STARTED)
                    cancelled_tasks = sum(1 for task in tasks if task.status == StatusEnum.CANCELLED)

                    total_tasks = len(tasks)
                    overall_progress = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0

                    # Get task details
                    task_details = []
                    for task in tasks:
                        task_details.append({
                            "id": task.id,
                            "title": task.title,
                            "status": task.status.value,
                            "priority": task.priority.value,
                            "estimated_hours": task.estimated_hours,
                            "actual_hours": task.actual_hours
                        })

                    return {
                        "success": True,
                        "project_id": project_id,
                        "project_name": project.name,
                        "overall_progress": round(overall_progress, 1),
                        "total_tasks": total_tasks,
                        "completed_tasks": completed_tasks,
                        "in_progress_tasks": in_progress_tasks,
                        "on_hold_tasks": on_hold_tasks,
                        "not_started_tasks": not_started_tasks,
                        "cancelled_tasks": cancelled_tasks,
                        "task_details": task_details
                    }
            except Exception as e:
                return {"error": f"Failed to get progress summary: {str(e)}"}


    def run(self):
        """Run the Task Manager MCP server."""
        print(f"Starting Task Manager MCP Server...")
        print(f"Server will be available at: http://{self.host}:{self.port}{self.path}")
        print(f"Working directory: {Path.cwd()}")
        print(f"Database: {self.db_manager.database_url}")

        # Run the server with streamable-http transport
        self.mcp.run(transport="streamable-http", mount_path=self.path)


def main():
    """Main entry point for the Task Manager MCP server."""
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Task Manager MCP Server")
    parser.add_argument(
        "--host",
        default=os.environ.get("MCP_HOST", "127.0.0.1"),
        help="Host to run the server on (default: 127.0.0.1)"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=int(os.environ.get("MCP_PORT", "8000")),
        help="Port to run the server on (default: 8000)"
    )
    parser.add_argument(
        "--path",
        default=os.environ.get("MCP_PATH", "/mcp"),
        help="Path for the MCP endpoint (default: /mcp)"
    )
    args = parser.parse_args()

    # Create and run the server
    server = TaskManagerMCPServer(
        host=args.host,
        port=args.port,
        path=args.path
    )
    server.run()


if __name__ == "__main__":
    main()
