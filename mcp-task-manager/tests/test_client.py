#!/usr/bin/env python3
"""
Test client for the Task Manager MCP server, version HTTP streamable.
"""

import asyncio
import json

try:
    from mcp.client.streamable_http import streamablehttp_client
    from mcp import ClientSession
    HAS_MCP_CLIENT = True
except ImportError:
    print("Warning: MCP client not available. Install with: pip install 'mcp'")
    HAS_MCP_CLIENT = False


def parse_content(content):
    """
    Parse content returned by MCP server.
    Handles TextContent lists and raw dicts/lists/strings.
    """
    import mcp.types as mcp_types

    # Case: list of contents (common in MCP responses)
    if isinstance(content, list):
        parsed_list = []
        for item in content:
            if isinstance(item, mcp_types.TextContent):
                try:
                    parsed_list.append(json.loads(item.text))
                except Exception:
                    parsed_list.append(item.text)
            else:
                parsed_list.append(item)
        # If it's a single JSON dict wrapped in a list, just return that
        if len(parsed_list) == 1:
            return parsed_list[0]
        return parsed_list

    # Case: single TextContent
    if isinstance(content, mcp_types.TextContent):
        try:
            return json.loads(content.text)
        except Exception:
            return content.text

    # Case: primitive or already dict/list
    if isinstance(content, (dict, list, str, int, float, bool, type(None))):
        return content

    # Fallback: try to unwrap `.text`
    if hasattr(content, "text"):
        try:
            return json.loads(content.text)
        except Exception:
            return content.text

    return str(content)
async def test_server_connection():
    """Test basic connection to the MCP server."""
    if not HAS_MCP_CLIENT:
        print("Skipping client tests - MCP client not available")
        return False

    try:
        print("Connecting to MCP server at http://127.0.0.1:8000/mcp...")

        async with streamablehttp_client("http://127.0.0.1:8000/mcp") as (read_stream, write_stream, _):
            async with ClientSession(read_stream, write_stream) as session:
                await session.initialize()

                print("✓ Connected to MCP server")
                return True

    except Exception as e:
        print(f"✗ Failed to connect to server: {e}")
        return False


async def test_server_tools():
    """Test the available tools on the MCP server."""
    if not HAS_MCP_CLIENT:
        print("Skipping tool tests - MCP client not available")
        return False

    all_ok = True

    try:
        async with streamablehttp_client("http://127.0.0.1:8000/mcp") as (read_stream, write_stream, _):
            async with ClientSession(read_stream, write_stream) as session:
                await session.initialize()

                # Test create_project
                print("\n--- Testing create_project tool ---")
                try:
                    payload = {
                        "name": "Test Project",
                        "description": "A test project for client testing",
                        "status": "not_started"
                    }
                    print("→ Sending payload:", payload)

                    project_result = await session.call_tool("create_project", payload)
                    print("← Raw result:", project_result.content)

                    content_json = parse_content(project_result.content)
                    print("✓ create_project result (parsed):", json.dumps(content_json, indent=2))

                    project = content_json.get("project") if isinstance(content_json, dict) else None
                    project_id = project.get("id") if project else None

                    if project_id:
                        # Test list_projects
                        print("\n--- Testing list_projects tool ---")
                        try:
                            list_projects_result = await session.call_tool("list_projects", {})
                            print("← Raw list_projects result:", list_projects_result.content)

                            list_projects_json = parse_content(list_projects_result.content)
                            projects = list_projects_json.get("projects", [])
                            print(f"✓ list_projects result: Found {len(projects)} projects")
                        except Exception as e:
                            print(f"✗ list_projects failed: {e}")
                            all_ok = False

                        # Test get_project
                        print("\n--- Testing get_project tool ---")
                        try:
                            get_project_result = await session.call_tool("get_project", {"project_id": project_id})
                            print("← Raw get_project result:", get_project_result.content)

                            get_project_json = parse_content(get_project_result.content)
                            print(f"✓ get_project result: {json.dumps(get_project_json, indent=2)}")
                        except Exception as e:
                            print(f"✗ get_project failed: {e}")
                            all_ok = False
                    else:
                        print("⚠️ project_id not found in create_project response")
                        all_ok = False

                except Exception as e:
                    print(f"✗ create_project failed: {e}")
                    all_ok = False

                return all_ok

    except Exception as e:
        print(f"✗ Failed to test tools: {e}")
        return False


async def main():
    print("Task Manager MCP Server Test Client")
    print("=" * 50)

    connection_ok = await test_server_connection()
    if connection_ok:
        tools_ok = await test_server_tools()
        if tools_ok:
            print("\n✓ All tests completed successfully")
        else:
            print("\n✗ Some tool tests failed")
    else:
        print("\n✗ Tests failed - could not connect to server")
        print("\nMake sure the server is running:")
        print("  python -m server.server")
        print("  # or")
        print("  mcp-task-manager")


if __name__ == "__main__":
    if HAS_MCP_CLIENT:
        asyncio.run(main())
    else:
        print("MCP client not available. Install with: pip install 'mcp'")
        import sys
        sys.exit(1)