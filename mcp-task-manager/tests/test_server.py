#!/usr/bin/env python3
"""
Tests for the Task Manager MCP server.

This module contains unit tests for the task manager MCP server functionality.
"""

import pytest
import asyncio
from unittest.mock import patch, MagicMock

# Import the server class
from server.server import TaskManagerMCPServer


class TestTaskManagerMCPServer:
    """Test cases for TaskManagerMCPServer class."""

    @patch('server.server.get_database_manager')
    def test_server_initialization(self, mock_db_manager):
        """Test that the server initializes correctly."""
        mock_db_manager.return_value.initialize_database.return_value = None

        server = TaskManagerMCPServer(host="localhost", port=9000, path="/test")

        assert server.host == "localhost"
        assert server.port == 9000
        assert server.path == "/test"
        assert server.mcp is not None

    @patch('server.server.get_database_manager')
    def test_server_default_values(self, mock_db_manager):
        """Test that the server uses correct default values."""
        mock_db_manager.return_value.initialize_database.return_value = None

        server = TaskManagerMCPServer()

        assert server.host == "127.0.0.1"
        assert server.port == 8000
        assert server.path == "/mcp"

    @patch('server.server.get_database_manager')
    @patch('server.server.FastMCP')
    def test_tools_registration(self, mock_fastmcp, mock_db_manager):
        """Test that tools are registered correctly."""
        mock_db_manager.return_value.initialize_database.return_value = None
        mock_mcp_instance = MagicMock()
        mock_fastmcp.return_value = mock_mcp_instance

        server = TaskManagerMCPServer()

        # Verify FastMCP was called with correct parameters
        mock_fastmcp.assert_called_once_with(
            name="Task Manager MCP Server",
            instructions="A comprehensive task management server supporting projects, roadmaps, releases, tasks, and subtasks with hierarchical organization and progress tracking."
        )

        # Verify that tool decorator was called (tools were registered)
        assert mock_mcp_instance.tool.called

    @patch('server.server.get_database_manager')
    def test_server_run_method_exists(self, mock_db_manager):
        """Test that the server has a run method."""
        mock_db_manager.return_value.initialize_database.return_value = None

        server = TaskManagerMCPServer()
        assert hasattr(server, 'run')
        assert callable(getattr(server, 'run'))


class TestServerTools:
    """Test cases for server tools."""

    @patch('server.server.get_database_manager')
    def setup_method(self, mock_db_manager):
        """Set up test fixtures."""
        mock_db_manager.return_value.initialize_database.return_value = None
        self.server = TaskManagerMCPServer()

    def test_project_tools_exist(self):
        """Test that project management tools are registered."""
        # This is a basic test to ensure the tool registration doesn't fail
        # In a real implementation, you might want to test the actual tool functionality
        assert self.server.mcp is not None

    def test_roadmap_tools_exist(self):
        """Test that roadmap management tools are registered."""
        # This is a basic test to ensure the tool registration doesn't fail
        assert self.server.mcp is not None

    def test_task_tools_exist(self):
        """Test that task management tools are registered."""
        # This is a basic test to ensure the tool registration doesn't fail
        assert self.server.mcp is not None


@pytest.mark.asyncio
class TestServerIntegration:
    """Integration tests for the server."""

    @patch('server.server.get_database_manager')
    async def test_server_creation_and_setup(self, mock_db_manager):
        """Test that server can be created and set up without errors."""
        mock_db_manager.return_value.initialize_database.return_value = None

        server = TaskManagerMCPServer()

        # Test that the server object is created correctly
        assert server is not None
        assert server.mcp is not None

        # Test that we can access server properties
        assert isinstance(server.host, str)
        assert isinstance(server.port, int)
        assert isinstance(server.path, str)


if __name__ == "__main__":
    # Run tests if this file is executed directly
    pytest.main([__file__, "-v"])
