# Team App Studio

AI Agent System for Full-Stack Application Generation

## Overview

Team App Studio is a multi-agent system that generates complete full-stack applications from a single user prompt. It follows a sequential workflow with specialized agents for different aspects of application development.

## Architecture

The system consists of:

1. **App Studio Manager** - Main orchestrator agent
2. **Product Manager** - Generates project specifications
3. **UX/UI Designer** - Creates design specifications
4. **Backend Developer** - Generates server-side code
5. **Frontend Developer** - Generates client-side code
6. **Document Manager** - Handles file operations

## Workflow

The agents work in this exact sequence:

1. **Product Manager** → Generates `specifications.json`
2. **UX/UI Designer** → Creates feature, screen, navigation, and form specs
3. **Backend Developer** → Generates Prisma schema, controllers, services
4. **Frontend Developer** → Generates React components, pages, forms
5. **Final Report** → Summary of generated application

## Directory Structure

```
team_app_studio/
├── __init__.py
├── agent.py              # Main orchestrator
├── prompt.py             # Orchestration logic
├── tools.py              # Agent tools
├── sub_agents/
│   ├── __init__.py
│   ├── product_manager/
│   │   ├── __init__.py
│   │   ├── agent.py
│   │   ├── prompt.py
│   │   └── tools.py
│   ├── ux_ui_manager/
│   │   └── ... (similar structure)
│   ├── frontend_developer/
│   │   └── ... (similar structure)
│   ├── backend_developer/
│   │   └── ... (similar structure)
│   └── document_manager/
│       └── ... (similar structure)
├── tests/
│   ├── __init__.py
│   ├── test_product_manager.py
│   ├── test_ux_ui_manager.py
│   ├── test_backend_developer.py
│   ├── test_frontend_developer.py
│   ├── test_app_studio_manager.py
│   └── run_tests.py
└── README.md
```

## Usage

### Running the Main Agent

```python
import asyncio
from team_app_studio.agent import main

# Run the app studio manager
asyncio.run(main())
```

### Using Individual Agents

```python
from team_app_studio.sub_agents import product_manager

# Use a specific sub-agent
result = product_manager.run("Build me a blog application")
```

## Testing

Run all tests:
```bash
cd team_app_studio/tests
python run_tests.py
```

Run specific test:
```bash
python run_tests.py test_product_manager
```

## Integration with JavaScript Scripts

The Python agents call existing JavaScript scripts via subprocess:

- `product_manager` → `src/scripts/projectSpecs/projectSpecs.js`
- `ux_ui_manager` → `src/scripts/featureSpecs/`, `screenSpecs/`, etc.
- `backend_developer` → `src/scripts/serverCode/generateServer.js`
- `frontend_developer` → `src/scripts/clientCode/generateScreens.js`

## Dependencies

- Google ADK (Agent Development Kit)
- Python 3.8+
- Node.js (for JavaScript script execution)

## Configuration

Ensure you have:
1. `GOOGLE_API_KEY` environment variable set
2. Node.js installed for script execution
3. MCP local context server running (for document manager)

## Output

Generated applications are placed in the `specs/` directory with:
- Project specifications
- Design specifications
- Generated server code
- Generated client code


python3 -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt