import { SeedSpec } from "specs/specs.interface";

export const accountHaveCoins: SeedSpec=[
     {
          provider: 'AuthUserController',
          action: 'signUp',
          params: [
            {
              email: '<EMAIL>',
              password: 'studiolabs',
              name: 'mock.name',
            },
            'fr',
          ],
        },
        {
          provider: 'IapController',
          action: 'buyGold',
          params: [
             {
               appType: 'ios',
               purchase: "mock.purchaseCoins"
             },
            'result',
          ],
        },
];