import { SeedSpec } from "specs/specs.interface";

export const accountHaveCoinsAndSubs: SeedSpec=[
     {
          provider: 'AuthUserController',
          action: 'signUp',
          saveAs: 'accountWithCoinsAndSubs',
          params: [
            {
              email: '<EMAIL>',
              password: 'studiolabs',
              name: 'mock.name',
            },
            'fr',
          ],
 }
];

 export const coinsAndSubsDemo: SeedSpec=[
  {
    provider: 'IapController',
    action: 'buyGold',
    params: [
       {
         appType: 'ios',
         purchase: "mock.purchaseCoins"
       },
      'seed.accountWithCoinsAndSubs.result',
    ],
  },
  {
    provider: 'IapController',
    action: 'save',
    params: [
       {
         appType: 'ios',
         purchase: "mock.purchaseSubs"
       },
       'seed.accountWithCoinsAndSubs.result',
    ],
  },
];

