import { SeedSpec } from "specs/specs.interface";

export const accountHaveSubs: SeedSpec=[
     {
          provider: 'AuthUserController',
          action: 'signUp',
          params: [
            {
              email: '<EMAIL>',
              password: 'studiolabs',
              name: 'mock.name',
            },
            'fr',
          ],
        },
        {
          provider: 'IapController',
          action: 'save',
          params: [
             {
               appType: 'ios',
               purchase: "mock.purchaseSubs"
             },
            'result',
          ],
        },
];