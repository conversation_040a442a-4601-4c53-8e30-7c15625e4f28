import {SeedSpec} from 'specs/specs.interface';

export const adminDemosCreateUseCaseWithNoPicture: SeedSpec = [
  {
    provider: 'AdminController',
    action: 'create',
    params: [
      'auth.admin',
      {
        email: 'mock.email',
        name: 'mock.name',
        contents: 'mock.contents',
        role: 'mock.role',
      },
    ],
  },
];

export const adminDemosCreateUseCaseWithPicture: SeedSpec = [
  {
    provider: 'AdminController',
    action: 'create',

    params: [
      'auth.admin',
      {
        email: 'mock.email',
        name: 'mock.name',
        contents: 'mock.contents',
        role: 'mock.role',
        imageUrl: 'mock.avatar',
      },
    ],
  },
];

export const adminDemoCreateUseCaseExistEmail: SeedSpec = [
  {
    provider: 'AdminController',
    action: 'create',

    params: [
      'auth.admin',
      {
        email: 'mock.email',
        name: 'mock.name',
        contents: 'mock.contents',
        role: 'mock.role',
      },
    ],
  },
];
