import {AnalyticEvent} from '@prisma/client';
import {SeedSpec} from 'specs/specs.interface';

export const analyticToViewEventDemo: SeedSpec = [
  {
    provider: 'AnalyticController',
    action: 'create',
    params: [
      'auth.user',
      {
        name: 'Analytic - view Event',
        objectType: 'news',
        objectId: 'seed.newsToRead.result.id',
        event: AnalyticEvent.view,
      },
    ],
  },
];
