import {SeedSpec} from 'specs/specs.interface';

const objects = [
  {
    objectId: 'seed.newsToRead.result.id',
    objectType: 'news',
  },
];

export const commentDemo: SeedSpec = Array.from({length: 4}, (x, index) => [
  {
    provider: 'CommentController',
    action: 'create',
    params: [
      'auth.user',
      {
        text: 'mock.paragraph',
        ...objects[index],
      },
    ],
  },
  {
    provider: 'CommentController',
    action: 'createAnswer',
    params: [
      'auth.test',
      {
        commentId: 'result.id',
        text: 'mock.paragraph',
        ...objects[index],
      },
    ],
  },
  {
    provider: 'CommentController',
    action: 'createAnswer',
    params: [
      'auth.user',
      {
        commentId: 'result.id',
        text: 'mock.paragraph',
        ...objects[index],
      },
    ],
  },
]).flatMap(x => x);

const objectProps = number => {
  if (number < 10) {
    return objects[0];
  }
  if (number >= 10 && number < 20) {
    return objects[1];
  }
  if (number >= 20 && number < 30) {
    return objects[2];
  }
  if (number >= 30 && number < 40) {
    return objects[3];
  }
  if (number >= 40 && number < 50) {
    return objects[4];
  }
};

export const commentsWithReportsDemo: SeedSpec = Array.from(
  {length: 50},
  (x, index) => [
    {
      provider: 'CommentController',
      action: 'create',
      params: [
        'seed.userDemo' + index.toString() + '.result',
        {
          text: 'mock.paragraph',
          ...objectProps(index),
        },
      ],
    },
    {
      provider: 'CommentController',
      action: 'reportComment',
      params: [
        'result.id',
        'seed.userDemo' + (49 - index) + '.result',
        {
          text: 'mock.paragraph',
        },
      ],
    },
  ],
).flatMap(x => x);
