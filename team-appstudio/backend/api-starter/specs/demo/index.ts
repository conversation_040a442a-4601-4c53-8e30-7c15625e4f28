import {
  adminDemosCreateUseCaseWithNoPicture,
  adminDemosCreateUseCaseWithPicture,
  adminDemoCreateUseCaseExistEmail,
} from './admins.demo';
import {Seed} from 'specs/specs.interface';

import {userDemos} from 'specs/demo/users.demo';

import {commentDemo, commentsWithReportsDemo} from 'specs/demo/comment.demo';
import {fieldDynamic} from 'specs/seed/field';
import {user} from 'specs/seed/auth/user/user';

import {test} from 'specs/seed/auth/user/test';
import {admin} from 'specs/seed/auth/admin/admin';
import {demoContents} from './contents.demo';

import {
  newsToShowOnHomeDemo,
  newsToCoinsDemo,
  newsToSubscriptionAndOrCoinsDemo,
  newsToSubscriptionDemo,
} from './news';
import {analyticToViewEventDemo} from 'specs/demo/analytic/analyticToViewEvent.demo';
import {
  accountHaveCoins,
  accountHaveSubs,
  accountHaveCoinsAndSubs,
  coinsAndSubsDemo,
  accountWithoutCoinsAndSubs,
} from './account';
import {creatorEvent} from 'specs/seed/auth/user/creatorEvent';
import {likeOnProfileDemo} from './like';
import {ratingDemo} from './rating.demo';
import {usersForDataTable} from 'specs/seed/user/usersForDataTable';

export const demoSeedConfig: Seed = {
  fieldDynamic,
  test,
  admin,
  user,
  creatorEvent,
  userDemos,
  adminDemosCreateUseCaseWithNoPicture,
  adminDemosCreateUseCaseWithPicture,
  adminDemoCreateUseCaseExistEmail,
  demoContents,

  newsToShowOnHomeDemo,
  newsToCoinsDemo,
  newsToSubscriptionAndOrCoinsDemo,
  newsToSubscriptionDemo,

  analyticToViewEventDemo,
  accountHaveCoins,
  accountHaveCoinsAndSubs,
  coinsAndSubsDemo,
  accountHaveSubs,
  accountWithoutCoinsAndSubs,
  commentDemo,
  commentsWithReportsDemo,
  likeOnProfileDemo,
  ratingDemo,
  usersForDataTable,
};

export default demoSeedConfig;
