import {SeedSpec} from 'specs/specs.interface';

export const likeOnProfileDemo: SeedSpec = Array.from(
  {length: 25},
  (x, index) => [
    {
      provider: 'LikeController',
      action: 'create',
      params: [
        'seed.userDemo' + index.toString() + '.result',
        {
          objectId: 'seed.test.result.id',
          objectType: 'profile',
          reaction: 1,
        },
      ],
    },
  ],
).flatMap(x => x);
