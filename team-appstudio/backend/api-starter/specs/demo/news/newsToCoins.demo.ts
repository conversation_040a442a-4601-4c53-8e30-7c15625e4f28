import {NewsCategory} from '@prisma/client';
import {SeedSpec} from 'specs/specs.interface';

export const newsToCoinsDemo: SeedSpec = [
  {
    provider: 'NewsController',
    action: 'create',
    params: [
      {
        title: 'News that needs coins',
      },
    ],
  },
  {
    provider: 'NewsController',
    action: 'update',
    params: [
      'result.id',
      {
        imageUrl: 'mock.banner',
        description: 'mock.paragraph',
        publicationDate: 'mock.today',
        category: NewsCategory.innewss,
        payWithCoins: true,
        payWithSubscription: false,
        priceWithCoins: 20,
        priceWithSubscription: 0,
      },
    ],
  },
];
