import {NewsCategory} from '@prisma/client';
import {SeedSpec} from 'specs/specs.interface';

export const newsToShowOnHomeDemo: SeedSpec = Array.from(
  {length: 2},
  (x, index) => [
    {
      provider: 'NewsController',
      action: 'create',
      params: [
        //'auth.admin',
        {
          title: 'News on Home ' + (index + 1),
        },
      ],
    },
    {
      provider: 'NewsController',
      action: 'update',
      params: [
        'result.id',
        {
          imageUrl: 'mock.banner',
          description: 'mock.paragraph',
          publicationDate: 'mock.today',
          category: NewsCategory.innewss,
        },
      ],
    },
  ],
).flatMap(x => x);
