import {NewsCategory} from '@prisma/client';
import {SeedSpec} from 'specs/specs.interface';

export const newsToSubscriptionDemo: SeedSpec = [
  {
    provider: 'NewsController',
    action: 'create',
    params: [
      {
        title: 'News that needs subscription',
      },
    ],
  },
  {
    provider: 'NewsController',
    action: 'update',
    params: [
      'result.id',
      {
        imageUrl: 'mock.banner',
        description: 'mock.paragraph',
        publicationDate: 'mock.today',
        category: NewsCategory.innewss,
        payWithCoins: false,
        payWithSubscription: true,
        priceWithCoins: 0,
        priceWithSubscription: 15,
      },
    ],
  },
];
