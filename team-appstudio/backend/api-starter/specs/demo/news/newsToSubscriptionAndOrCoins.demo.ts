import {NewsCategory} from '@prisma/client';
import {SeedSpec} from 'specs/specs.interface';

export const newsToSubscriptionAndOrCoinsDemo: SeedSpec = [
  {
    provider: 'NewsController',
    action: 'create',
    params: [
      {
        title: 'News that needs coins AND/OR Subscription',
      },
    ],
  },
  {
    provider: 'NewsController',
    action: 'update',
    params: [
      'result.id',
      {
        imageUrl: 'mock.banner',
        description: 'mock.paragraph',
        publicationDate: 'mock.today',
        category: NewsCategory.innewss,
        payWithCoins: true,
        payWithSubscription: true,
        priceWithCoins: 10,
        priceWithSubscription: 15,
      },
    ],
  },
];
