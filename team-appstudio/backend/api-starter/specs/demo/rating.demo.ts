import {SeedSpec} from 'specs/specs.interface';

const objects = [
  {
    objectId: 'seed.newsToRead.result.id',
    objectType: 'news',
    count: 5,
  },
  {
    objectId: 'seed.newsToRead.result.id',
    objectType: 'news',
    count: 2,
  },
];

export const ratingDemo: SeedSpec = Array.from({length: 4}, (x, index) => [
  {
    provider: 'RatingController',
    action: 'create',
    params: [
      'auth.user',
      {
        ...objects[index],
      },
    ],
  },
  {
    provider: 'RatingController',
    action: 'create',
    params: [
      'auth.test',
      {
        ...objects[index],
      },
    ],
  },
]).flatMap(x => x);
