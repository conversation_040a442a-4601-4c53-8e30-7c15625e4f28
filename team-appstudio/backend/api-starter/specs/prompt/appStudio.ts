export const AppStudioPrompt = () => `
# Definition of App-Studio

App-Studio simplifies frontend development by offering a rich set of components and utilities tailored for efficient design management, event handling, theming, and responsive layout creation.
By leveraging its components and features like the \`media\` prop and \`useResponsive\` hook, developers can create robust and adaptable web applications with ease.

## Examples of App-Studio usage:

It adds additional properties to help better manage the design:

- \`size\`: Makes width and height equal to size.
- \`on\`: Object that defines style for different CSS events.
- \`media\`: Object that defines styles for different media queries.
- \`shadow\`: Shadows an element; it can be a boolean, number, or \`Shadow\` object.

### View

The \`View\` component is a more generic one; it can be used to create any kind of layout. It extends the basic \`div\` HTML tag and provides additional properties for managing styles. It also provides several other components for convenience, including \`Div\`, \`SafeArea\`, \`Scroll\`, and \`Span\`.

#### Usage

\`\`\`jsx
<View backgroundColor="color.red" color="color.white" padding={20}>This is a view</View>
\`\`\`

### Text

The \`Text\` component extends the basic \`div\` HTML tag with additional properties for managing text styles.

#### Usage

\`\`\`jsx
<Text color="color.blue" textAlign="justify">This is a text</Text>
\`\`\`

### Image

The \`Image\` component extends the basic \`img\` HTML tag with additional properties like shadow, media, and on.

#### Usage

\`\`\`jsx
<Image src="url_to_image" alt="description" />
\`\`\`

### Form

The \`Form\` component extends the basic \`form\` HTML tag. It also provides \`Button\` and \`Input\` components.

#### Usage

\`\`\`jsx
<Form>
  <Input placeholder="Enter your name" />
  <Button isAuto>Submit</Button>
</Form>
\`\`\`

App-Studio provides CSS design props for layout, spacing, sizing, shadows with the \`shadow\` prop, event management through the \`on\` prop, and theming. Components include \`Element\` for fundamental design, \`View\` based on the \`div\`, \`Text\` for text styles, \`Form\` for form-related designs, and \`Image\` based on the \`img\` tag.

## Event Management

App-Studio provides an intuitive way to manage events in your CSS through the \`on\` prop. This feature is designed to offer a convenient way to style elements based on various interactive states, represented by CSS pseudo-classes like \`hover\`, \`active\`, and \`focus\`.

### 1. Introduction to \`on\` Prop

The \`on\` prop takes an object as its value. The keys in this object correspond to the names of the CSS pseudo-classes, and the values are objects that define the styles to apply when the event occurs.

\`\`\`jsx
on={{ [eventName]: { [styleProps]: [styleValue] } }}
\`\`\`

Here \`eventName\` is the name of the CSS pseudo-class, \`styleProps\` are the CSS properties you wish to change, and \`styleValue\` are the values you want to apply.

### 2. Usage Examples

#### Example 1: Changing Background Color on Hover for \`View\`

\`\`\`jsx
<View
  backgroundColor="grey"
  padding={20}
  on={{ hover: { backgroundColor: 'red' } }}
>
  Hover over me
</View>
\`\`\`

### 3. Supported Events

The \`on\` prop currently supports a variety of CSS pseudo-classes, allowing you to fine-tune your UI based on user interaction. Here are some commonly used pseudo-classes:

- \`hover\`: Triggered when the mouse is placed over the component.
- \`active\`: Triggered when the component is actively being interacted with (e.g., a mouse click).
- \`focus\`: Triggered when the component gains focus (e.g., through tab navigation).
- \`disabled\`: Triggered when the component is disabled.

The \`on\` prop is a powerful and efficient way to manage events in your CSS. It offers a straightforward method for enhancing your components' interactivity, making for a more dynamic and engaging user experience.

### Example of Style Usage

\`\`\`jsx
<View backgroundColor="red" padding={10}>This is an example of how App-Studio component accepts styles</View>
\`\`\`

### Media Prop for Responsive Design

The media prop is particularly useful for managing responsive design without causing your components to re-render. You can specify different styles for various devices or screen sizes.
Mobile, desktop or tablet should be used exclusively with media only.

### Examples of Media (Responsiveness) Usage
1. \`\`\`jsx
<View size={100}
     media={{
        mobile: {
          backgroundColor: 'color.green',
        },
        tablet: {
          backgroundColor: 'color.yellow',
        },
        desktop: {
          backgroundColor: 'color.blue',
        },
      }}
/>
\`\`\`

2. \`\`\`jsx
<Horizontal media={{
        mobile: {
          width: 100,
          height:100,
        },
        tablet: {
          width: 200,
          height:200,
        },
        desktop: {
          width: 300,
          height:300,
        },
      }}
/>
\`\`\`

#### Important example of responsiveness usage:

##### Don't use:
\`\`\`
<Vertical gap={10} flex={{ mobile: 1, tablet: 1 }}>Text</View>
\`\`\`
##### Do Use:
\`\`\`
<Vertical gap={10} media={{ mobile: {flex:1}, tablet: {flex:1} }}>Text</View>
\`\`\`

### Example of Button Usage

\`\`\`jsx
<Button isAuto>Submit</Button>
\`\`\`


`;
