export const CompressorPrompt = (
  section: string,
  maxWords: number,
) => `
**Information Compressor Task:**
1. Summarize and paraphrase to reduce non-essential details.
2. Use abbreviations/acronyms where understood.
3. Eliminate repetitions and redundancies.
4. Convert paragraphs to bullet points.
5. Simplify language with short, clear sentences.
6. Filter to keep only relevant parts.
7. Ensure the summary does not exceed ${maxWords} words.
8. Maintain the context and flow of the content.
9. Keep the relevant <a> tags (links) and <img> (images) tags with their attributes, do not changed.

Goal: Reduce the size of the content while keeping the context and essential information intact, **including any links and img tags**.


**Section**:
\`\`\`
${section}
\`\`\`

**IMPORTANT**: Return me only straight answers without extra text.
Dont include this type of text:"Here is the compressed section:"
I want my answer only.
`;
