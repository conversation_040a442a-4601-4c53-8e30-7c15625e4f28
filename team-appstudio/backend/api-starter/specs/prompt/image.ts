export const photographyExamples = {
  professionalPortrait: `
      **Example: Professional Portrait**
      - **Subject/Scene**: Elegant and ethereal portrait of a model exuding grace and confidence.
      - **Character/Element Details**: Soft, diffused lighting to highlight facial features; sophisticated composition with the subject positioned using the rule of thirds.
      - **Background and Environment**: Dreamy settings with high fashion elements such as flowing fabrics or stylish accessories; blurred background (bokeh effect) to emphasize the subject.
      - **Lighting**: Rembrandt lighting technique to create depth and dimension.
      - **Composition**: Balanced framing with attention to symmetry and proportion.
      - **Color Palette**: Neutral tones with subtle pops of color to maintain elegance.
      - **Mood/Emotion**: Serene and poised, conveying a sense of timeless beauty.
      - **Style**: Fine art portraiture with a focus on detail and refinement.
    `,

  sophisticatedPortrait: `
      **Example: Sophisticated Portrait**
      - **Subject/Scene**: High-impact portrait photograph exuding sophistication and elegance.
      - **Character/Element Details**: Subject styled with meticulous attention to detail, including attire and accessories that complement the overall look.
      - **Background and Environment**: Meticulously arranged background elements that highlight the subject, such as luxurious fabrics or artistic props.
      - **Lighting**: Dramatic lighting with strong contrasts and highlights to create depth and a theatrical effect.
      - **Composition**: Focused framing with the subject as the central element, utilizing negative space to enhance prominence.
      - **Color Palette**: Rich, deep colors with highlights to emphasize features and attire.
      - **Mood/Emotion**: Confident and poised, conveying a sense of authority and grace.
      - **Style**: High-fashion portraiture with a blend of classic and modern elements.
    `,

  sportPortrait: `
      **Example: Sport Portrait**
      - **Subject/Scene**: Adventurous landscape capturing athletes in extreme outdoor conditions.
      - **Character/Element Details**: Dynamic action poses showcasing strength, agility, and determination in challenging environments.
      - **Background and Environment**: Vast natural landscapes such as rugged mountains, icy terrains, or dense forests emphasizing the raw beauty and difficulty of the sport.
      - **Lighting**: Natural lighting with possible use of backlighting to create silhouettes or highlight muscle definition.
      - **Composition**: Action-oriented framing with a focus on movement and energy; use of motion blur to convey speed.
      - **Color Palette**: Earthy tones with vibrant accents to highlight the athlete and environment.
      - **Mood/Emotion**: Intense and inspiring, capturing the spirit of adventure and perseverance.
      - **Style**: Adventure sports photography with a focus on action and environment.
    `,

  culturalPortrait: `
      **Example: Cultural Portrait**
      - **Subject/Scene**: Vibrant, culturally rich portrait featuring contemporary fashion infused with traditional elements.
      - **Character/Element Details**: Subject's individuality and authenticity highlighted through unique attire, accessories, and expressive gestures.
      - **Background and Environment**: Soft, natural lighting with subtle cultural motifs in the background to complement the subject.
      - **Lighting**: Softbox or window lighting to create a flattering and natural look.
      - **Composition**: Intimate framing with a focus on the subject's face and attire; shallow depth of field to blur the background.
      - **Color Palette**: Bold and harmonious colors that reflect cultural heritage and personal style.
      - **Mood/Emotion**: Empowering and expressive, showcasing pride and cultural identity.
      - **Style**: Contemporary portraiture with cultural storytelling elements.
    `,

  naturalScenePortrait: `
      **Example: Natural Scene Portrait**
      - **Subject/Scene**: Lifestyle photograph with a cinematic feel set in a natural environment.
      - **Character/Element Details**: Relatable, candid moments captured with dynamic compositions; subject interacting naturally with the surroundings.
      - **Background and Environment**: Lush natural settings with elements like trees, water, or open fields blending seamlessly with the subject.
      - **Lighting**: Natural light, preferably during golden hour for a warm and soft glow.
      - **Composition**: Rule of thirds with the subject positioned to interact with the environment; use of foreground elements for depth.
      - **Color Palette**: Soft, earthy tones with natural highlights and shadows.
      - **Mood/Emotion**: Relaxed and genuine, conveying a connection with nature.
      - **Style**: Cinematic portraiture with an emphasis on natural beauty and authenticity.
    `,

  coloredLightPortrait: `
      **Example: Colored Light Portrait**
      - **Subject/Scene**: Whimsical portrait utilizing creative lighting effects such as neon lights or fairy lights.
      - **Character/Element Details**: Dreamy atmosphere created through vibrant colors, playful elements like confetti or light trails, and dynamic poses.
      - **Background and Environment**: Magical settings enhanced by colorful lighting, such as urban nightscapes with neon signs or enchanted forests with fairy lights.
      - **Lighting**: Colored gels or LED lights to cast vibrant hues and create interesting shadows and highlights.
      - **Composition**: Focus on the interplay between light and subject; use of backlighting or rim lighting to create silhouettes or halos.
      - **Color Palette**: Bright, saturated colors with complementary contrasts to enhance the whimsical feel.
      - **Mood/Emotion**: Playful and enchanting, evoking a sense of fantasy and creativity.
      - **Style**: Artistic portraiture with a focus on lighting effects and creative expression.
    `,

  productPhoto: `
      **Example: Product Photo**
      - **Subject/Scene**: High-end tech gadget showcased on a sleek, clean surface.
      - **Character/Element Details**: Minimalist product placement with precise angles to highlight key features; subtle reflections to add realism.
      - **Background and Environment**: Pure white background with soft, diffused shadows to eliminate distractions.
      - **Lighting**: Softbox lighting to ensure even illumination and eliminate harsh shadows.
      - **Composition**: Centralized placement with ample negative space to draw attention to the product.
      - **Color Palette**: Monochromatic with emphasis on the product's colors.
      - **Mood/Emotion**: Modern and sophisticated, emphasizing quality and innovation.
      - **Style**: High-resolution commercial photography with a focus on clarity and detail.
    `,

  eventPhoto: `
      **Example: Event Photo**
      - **Subject/Scene**: Dynamic capture of a vibrant event, showcasing authentic interactions and lively atmosphere.
      - **Character/Element Details**: Diverse group of people engaging in activities, candid expressions, and natural poses.
      - **Background and Environment**: Energetic event setting with colorful decorations, ambient lighting, and dynamic elements like confetti or stage setups.
      - **Lighting**: Natural lighting supplemented with ambient event lights to maintain authenticity.
      - **Composition**: Action-oriented framing with a focus on capturing movement and spontaneity.
      - **Color Palette**: Bright and lively colors to reflect the event's energy.
      - **Mood/Emotion**: Joyful and spirited, conveying the excitement of the event.
      - **Style**: Photojournalistic approach to capture real moments and emotions.
    `,

  urbanScene: `
      **Example: Urban Scene**
      - **Subject/Scene**: Bustling city life featuring iconic architectural landmarks and dynamic street activities.
      - **Character/Element Details**: People walking, vehicles in motion, street vendors, and urban flora.
      - **Background and Environment**: City skyline with a mix of modern and vintage-inspired buildings; elements like graffiti art or street signs to add character.
      - **Lighting**: Golden hour lighting to enhance the city's vibrancy and shadows.
      - **Composition**: Layered depth with foreground, midground, and background elements to create a sense of space.
      - **Color Palette**: Contrasting colors with a mix of warm and cool tones to highlight architectural details.
      - **Mood/Emotion**: Energetic and lively, capturing the essence of urban living.
      - **Style**: Contemporary street photography with an emphasis on architecture and daily life.
    `,

  culturalMoment: `
      **Example: Cultural Moment**
      - **Subject/Scene**: Significant cultural event capturing traditional practices and vibrant festivities.
      - **Character/Element Details**: Participants adorned in traditional attire, engaging in ceremonial activities or dances.
      - **Background and Environment**: Cultural landmarks, intricate decorations, and rich textures that reflect the heritage.
      - **Lighting**: Natural daylight or warm artificial lighting to highlight colors and details.
      - **Composition**: Focus on the central figures with supportive elements framing the scene.
      - **Color Palette**: Rich and saturated colors representing cultural significance.
      - **Mood/Emotion**: Celebratory and reverent, showcasing pride and joy in cultural identity.
      - **Style**: Documentary-style photography with a focus on storytelling and authenticity.
    `,

  scenicNature: `
      **Example: Scenic Nature**
      - **Subject/Scene**: Breathtaking landscape emphasizing untouched natural beauty and grandeur.
      - **Character/Element Details**: Majestic mountains, serene lakes, lush forests, and flowing rivers.
      - **Background and Environment**: Expansive vistas with dramatic skies, misty horizons, and diverse flora.
      - **Lighting**: Soft morning light or golden hour to enhance natural colors and textures.
      - **Composition**: Wide-angle framing to capture the vastness; use of leading lines and depth to guide the viewer's eye.
      - **Color Palette**: Natural hues with vibrant greens, blues, and earthy tones.
      - **Mood/Emotion**: Tranquil and awe-inspiring, evoking a sense of wonder and peace.
      - **Style**: Landscape photography with high dynamic range to capture detail and depth.
    `,

  architecturalSpace: `
      **Example: Architectural Space**
      - **Subject/Scene**: Elegant architectural structures showcasing innovative design and craftsmanship.
      - **Character/Element Details**: Geometric forms, intricate details, and spatial relationships highlighting architectural features.
      - **Background and Environment**: Clean lines with minimalistic surroundings to emphasize the structure; elements like glass reflections or shadows for added depth.
      - **Lighting**: Natural or soft artificial lighting to accentuate textures and materials.
      - **Composition**: Symmetrical framing with a focus on the building's form and structure.
      - **Color Palette**: Neutral tones with metallic or glass accents to highlight modernity.
      - **Mood/Emotion**: Impressive and inspiring, reflecting architectural excellence.
      - **Style**: Architectural photography with a focus on detail and design aesthetics.
    `,

  natureLandscape: `
      **Example: Nature Landscape**
      - **Subject/Scene**: Expansive natural scenery showcasing untouched beauty and diverse ecosystems.
      - **Character/Element Details**: Majestic landscapes featuring elements like towering mountains, serene lakes, dense forests, and cascading waterfalls.
      - **Background and Environment**: Grand natural elements with dramatic skies, misty horizons, and a variety of flora and fauna.
      - **Lighting**: Dynamic lighting such as sunrise or sunset to enhance colors and create dramatic shadows.
      - **Composition**: Wide-angle framing with a focus on depth and scale; use of foreground elements to add dimension.
      - **Color Palette**: Rich, true-to-life colors with a balance of vibrant and natural tones.
      - **Mood/Emotion**: Awe-inspiring and tranquil, emphasizing the grandeur and serenity of nature.
      - **Style**: High-resolution landscape photography with an emphasis on detail and natural beauty.
    `,

  documentaryScene: `
      **Example: Documentary Scene**
      - **Subject/Scene**: Poignant documentary-style photograph highlighting human stories and social issues.
      - **Character/Element Details**: Authentic emotions captured in impactful moments, such as expressions of joy, struggle, or resilience.
      - **Background and Environment**: Contextual settings that provide insight into the subject's environment and the broader social theme.
      - **Lighting**: Natural or available lighting to maintain authenticity and realism.
      - **Composition**: Storytelling framing with attention to context and environment; use of leading lines to guide the viewer's eye.
      - **Color Palette**: Muted or natural colors to enhance the documentary feel.
      - **Mood/Emotion**: Thought-provoking and empathetic, aiming to convey a powerful narrative.
      - **Style**: Photojournalistic approach with a focus on storytelling and realism.
    `,

  storyScene: `
      **Example: Story Scene**
      - **Subject/Scene**: Compelling documentary photograph that tells a meaningful and engaging story.
      - **Character/Element Details**: Emotional connections between subjects, capturing real-life experiences and interactions.
      - **Background and Environment**: Realistic settings that provide context and address relevant social or personal issues.
      - **Lighting**: Natural lighting to preserve the authenticity of the scene.
      - **Composition**: Narrative-focused framing with emphasis on interactions and emotions.
      - **Color Palette**: Balanced colors that support the story without overwhelming the subjects.
      - **Mood/Emotion**: Deeply engaging and relatable, aiming to evoke empathy and understanding.
      - **Style**: Narrative-driven documentary photography with a focus on human interest.
    `,

  vibrantPortrait: `
      **Example: Vibrant Portrait**
      - **Subject/Scene**: Vibrant and dramatic portrait capturing the subject's emotional depth and cultural background.
      - **Character/Element Details**: Strong use of color and captivating composition to highlight the subject's personality and heritage.
      - **Background and Environment**: Balanced combination of natural and artificial lighting to enhance the subject; subtle cultural elements in the background.
      - **Lighting**: Mixed lighting techniques to create highlights and shadows that add depth.
      - **Composition**: Dynamic framing with a focus on the subject's expression and attire.
      - **Color Palette**: Bold and rich colors that complement the subject and background.
      - **Mood/Emotion**: Intense and expressive, conveying a powerful narrative about the subject.
      - **Style**: High-impact portraiture with an emphasis on color and emotional expression.
    `,
};

export const illustrationExamples = {
  styledSilhouetteScene: `
      **Example:  Styled Silhouette Scene**
      - **Subject/Scene**: Individual in a professional setting depicted as a silhouette.
      - **Character/Element Details**: Silhouetted figures such as a man carrying a suitcase and backpack, and a woman holding a briefcase.
      - **Background and Environment**: Silhouette of an airplane against a vibrant sunset, abstract cloud shapes, and an airport atmosphere.
      - **Lighting**: Backlighting to create sharp, defined silhouettes.
      - **Composition**: Balanced framing with the subjects positioned against the backdrop to emphasize their shapes.
      - **Color Palette**: Contrasting colors between the silhouette and the background to enhance separation.
      - **Mood/Emotion**: Determined and focused, conveying a sense of journey or transition.
      - **Style**: Minimalist illustration with a focus on shape and contrast.
    `,

  modernFacelessIllustration: `
      **Example: Modern Faceless Illustration**
      - **Subject/Scene**: Volleyball player captured mid-air without facial features.
      - **Character/Element Details**: Geometric, soft-edged shapes defining the athlete's form and movement.
      - **Background and Environment**: Clean white background with a bold diagonal red line adding dynamic tension.
      - **Lighting**: Even, flat lighting to maintain the faceless aesthetic.
      - **Composition**: Action-focused framing with emphasis on the athlete's pose and the diagonal line.
      - **Color Palette**: Monochromatic with a striking red accent to draw attention.
      - **Mood/Emotion**: Energetic and abstract, emphasizing movement and form.
      - **Style**: Modern, abstract illustration with a focus on geometry and simplicity.
    `,

  colorAnimeClassic: `
      **Example: Classic Color Anime**
      - **Subject/Scene**: Everyday moments filled with emotion in a classic anime style.
      - **Character/Element Details**: Quick sketches with expressive characters and dynamic poses; interplay of light and shadow to add depth.
      - **Background and Environment**: Simple yet evocative compositions that complement the characters without overwhelming them.
      - **Lighting**: Soft, diffuse lighting typical of anime aesthetics to create a warm and inviting atmosphere.
      - **Composition**: Balanced and dynamic, with a focus on character interaction and emotional expression.
      - **Color Palette**: Bright and harmonious colors that enhance the emotional tone.
      - **Mood/Emotion**: Heartwarming and nostalgic, capturing the essence of everyday life.
      - **Style**: Classic anime illustration with a focus on emotion and simplicity.
    `,

  dreamBeauty: `
      **Example: Dream Beauty Illustration**
      - **Subject/Scene**: Ethereal and dreamlike landscapes filled with surreal elements.
      - **Character/Element Details**: Delicate, surreal figures intertwined with natural elements like floating flowers or misty forms.
      - **Background and Environment**: Soft, flowing imagery with harmonious light and shadow blending seamlessly into the surroundings.
      - **Lighting**: Soft, diffused lighting to enhance the dreamlike quality.
      - **Composition**: Fluid and organic, with elements gracefully merging into each other.
      - **Color Palette**: Pastel and muted tones with occasional vibrant accents to highlight focal points.
      - **Mood/Emotion**: Serene and otherworldly, evoking a sense of wonder and tranquility.
      - **Style**: Surreal illustration with a focus on beauty and dreamscapes.
    `,

  fluidForm: `
      **Example: Fluid Form Illustration**
      - **Subject/Scene**: Elegant, flowing forms that convey movement and grace through vivid colors.
      - **Character/Element Details**: Traditional ink and brush techniques merged with digital colorization to create seamless transitions.
      - **Background and Environment**: Harmonious blend of classic Japanese art elements with contemporary design aesthetics.
      - **Lighting**: Subtle gradients and shading to enhance the fluidity of forms.
      - **Composition**: Balanced and flowing, with elements guiding the viewer's eye through the illustration.
      - **Color Palette**: Vibrant colors juxtaposed with monochromatic ink lines for contrast.
      - **Mood/Emotion**: Graceful and dynamic, capturing the essence of fluid motion.
      - **Style**: Fusion of traditional and digital illustration techniques focusing on fluidity and color.
    `,

  girlFairyTale: `
      **Example: Fairy Tale Girl Illustration**
      - **Subject/Scene**: Feminine drawings depicting fairy tale characters with exaggerated proportions.
      - **Character/Element Details**: Hand-drawn sketches with graceful, flowing lines and intricate details in attire and accessories.
      - **Background and Environment**: Warm, inviting scenes such as enchanted forests, castles, or magical gardens with vibrant colors.
      - **Lighting**: Soft, magical lighting to enhance the fairy tale atmosphere.
      - **Composition**: Central focus on the character with supportive background elements to create a cohesive scene.
      - **Color Palette**: Rich and vibrant colors to bring the fairy tale elements to life.
      - **Mood/Emotion**: Whimsical and enchanting, evoking a sense of magic and wonder.
      - **Style**: Fantasy illustration with a focus on detailed, graceful character design.
    `,

  comicIllustration: `
      **Example: Comic Illustration**
      - **Subject/Scene**: Fusion of manga and Western art styles to create dynamic and engaging scenes.
      - **Character/Element Details**: Energetic line work, expressive characters with exaggerated features, and vibrant action elements like speed lines or explosions.
      - **Background and Environment**: Dynamic compositions that convey movement and excitement; urban settings or fantastical landscapes depending on the narrative.
      - **Lighting**: High-contrast lighting with dramatic shadows to emphasize action.
      - **Composition**: Action-packed framing with multiple focal points to guide the viewer's eye.
      - **Color Palette**: Bold and vibrant colors to enhance the dynamic feel.
      - **Mood/Emotion**: Exciting and intense, capturing the energy of comic storytelling.
      - **Style**: Hybrid comic illustration blending the best of manga and Western comic styles.
    `,

  nostalgicIllustration: `
      **Example: Nostalgic Illustration**
      - **Subject/Scene**: Cinematic scenes frozen in time, evoking a sense of nostalgia and reminiscence.
      - **Character/Element Details**: Soft flat colors and silhouettes creating a timeless feel; elements like vintage cars, old buildings, or classic fashion.
      - **Background and Environment**: Subtle texture effects such as grain or sepia tones to enhance the vintage atmosphere.
      - **Lighting**: Gentle, subdued lighting to maintain a nostalgic mood.
      - **Composition**: Balanced and symmetrical, with a focus on iconic elements that define the nostalgic theme.
      - **Color Palette**: Muted and earthy tones with occasional pastel accents.
      - **Mood/Emotion**: Reflective and sentimental, invoking memories and longing.
      - **Style**: Vintage-inspired illustration with a focus on timelessness and emotional depth.
    `,

  storybookIllustration: `
      **Example: Storybook Illustration**
      - **Subject/Scene**: Everyday scenes infused with fantastical elements, perfect for children's storybooks.
      - **Character/Element Details**: Soft pencil strokes with whimsical details like talking animals, magical objects, or playful characters.
      - **Background and Environment**: Light textures and muted color palettes creating a gentle and inviting atmosphere.
      - **Lighting**: Soft, natural lighting to maintain a warm and friendly feel.
      - **Composition**: Clear and simple layouts that guide the viewer through the story effortlessly.
      - **Color Palette**: Pastel and gentle colors that appeal to children and support the whimsical elements.
      - **Mood/Emotion**: Playful and imaginative, encouraging creativity and exploration.
      - **Style**: Whimsical and charming illustration with a focus on storytelling and child-friendly aesthetics.
    `,

  fictionIllustration: `
      **Example: Fiction Illustration**
      - **Subject/Scene**: Surreal, retro scenes reminiscent of 1970s sci-fi covers, blending imagination with nostalgia.
      - **Character/Element Details**: Imaginative, otherworldly motifs such as alien landscapes, futuristic technology, or fantastical creatures.
      - **Background and Environment**: Strong contrasts with intricate details that create a rich and immersive world.
      - **Lighting**: Dramatic and vibrant lighting to enhance the surreal elements.
      - **Composition**: Complex and layered, encouraging viewers to explore every detail.
      - **Color Palette**: Bold and contrasting colors with a vintage feel.
      - **Mood/Emotion**: Mysterious and intriguing, sparking curiosity and wonder.
      - **Style**: Retro-futuristic illustration with a focus on detailed and imaginative world-building.
    `,

  childrenBookIllustration: `
      **Example: Children's Book Illustration**
      - **Subject/Scene**: Playful and engaging scenes designed specifically for children's storybooks.
      - **Character/Element Details**: Friendly characters with approachable designs, such as smiling animals or adventurous children.
      - **Background and Environment**: Whimsical elements like colorful landscapes, magical forests, or playful settings with gentle textures.
      - **Lighting**: Bright and cheerful lighting to create an inviting atmosphere.
      - **Composition**: Clear and simple layouts that are easy for children to follow and understand.
      - **Color Palette**: Soft, vibrant colors that appeal to a young audience and stimulate imagination.
      - **Mood/Emotion**: Joyful and uplifting, encouraging a sense of fun and discovery.
      - **Style**: Bright and colorful illustration with a focus on simplicity and charm.
    `,

  conceptArtIllustration: `
      **Example: Concept Art Illustration**
      - **Subject/Scene**: Fantasy elements blended with realistic details to create immersive and imaginative settings.
      - **Character/Element Details**: Detailed environments and characters, including mythical creatures, elaborate costumes, and intricate architecture.
      - **Background and Environment**: Rich textures and imaginative settings such as enchanted forests, futuristic cities, or ancient ruins.
      - **Lighting**: Dynamic lighting to highlight key elements and create atmospheric depth.
      - **Composition**: Layered and detailed, encouraging viewers to explore the intricacies of the scene.
      - **Color Palette**: Diverse and rich colors that enhance the fantastical elements.
      - **Mood/Emotion**: Epic and adventurous, sparking the imagination and storytelling.
      - **Style**: High-detail concept art with a focus on world-building and visual storytelling.
    `,

  modernAnimeIllustration: `
      **Example: Modern Anime Illustration**
      - **Subject/Scene**: Contemporary anime-style scenes reflecting current trends and storytelling techniques.
      - **Character/Element Details**: Clean lines, expressive characters with modern fashion and accessories.
      - **Background and Environment**: Modern settings such as urban landscapes, cozy interiors, or dynamic action scenes with detailed environments.
      - **Lighting**: Vibrant and dynamic lighting to emphasize character expressions and action.
      - **Composition**: Energetic and balanced, focusing on character interactions and movement.
      - **Color Palette**: Bright and varied colors that reflect the modern aesthetic.
      - **Mood/Emotion**: Engaging and lively, capturing the essence of modern anime storytelling.
      - **Style**: Contemporary anime illustration with a blend of traditional and digital techniques.
    `,

  classicMangaIllustration: `
      **Example: Classic Manga Illustration**
      - **Subject/Scene**: Traditional manga-style narratives featuring iconic storytelling elements.
      - **Character/Element Details**: Characteristic line work, expressive facial features, and dynamic poses typical of classic manga.
      - **Background and Environment**: Detailed, traditional settings such as bustling streets, serene landscapes, or intimate indoor scenes.
      - **Lighting**: High-contrast shading with emphasis on black and white tones to maintain the classic manga aesthetic.
      - **Composition**: Sequential and narrative-driven, guiding the viewer through the story.
      - **Color Palette**: Black and white with strategic use of screentones for depth and texture.
      - **Mood/Emotion**: Varied, ranging from intense and dramatic to heartfelt and emotional.
      - **Style**: Traditional manga illustration with a focus on storytelling and expressive character design.
    `,

  minimalistFigureIllustration: `
      **Example: Minimalist Figure Illustration**
      - **Subject/Scene**: Simplified silhouettes focusing on the human form and movement.
      - **Character/Element Details**: Clean lines without facial features, emphasizing posture and gesture.
      - **Background and Environment**: Minimalist backgrounds with solid colors or subtle gradients to highlight the figure.
      - **Lighting**: Flat or gradient lighting to maintain simplicity and focus on form.
      - **Composition**: Central or off-center positioning to create visual interest with ample negative space.
      - **Color Palette**: Limited color palette with monochromatic or complementary colors to enhance simplicity.
      - **Mood/Emotion**: Elegant and understated, conveying emotion through posture and composition.
      - **Style**: Minimalist illustration with a focus on form and simplicity.
    `,

  geometricVectorSketch: `
      **Example: Geometric Vector Sketch**
      - **Subject/Scene**: Precision-focused geometric shapes forming intricate and compelling compositions.
      - **Character/Element Details**: Clean lines with mathematical accuracy, incorporating shapes like triangles, circles, and polygons.
      - **Background and Environment**: Symmetrical patterns with balanced proportions and harmonious arrangements.
      - **Lighting**: Flat or gradient fills to maintain the geometric aesthetic.
      - **Composition**: Structured and balanced, utilizing symmetry and repetition for visual harmony.
      - **Color Palette**: Bold and contrasting colors to highlight geometric forms.
      - **Mood/Emotion**: Modern and abstract, emphasizing structure and precision.
      - **Style**: Vector-based geometric illustration with a focus on symmetry and balance.
    `,

  abstractShapeIllustration: `
      **Example: Abstract Shape Illustration**
      - **Subject/Scene**: Geometric shapes arranged in a visually compelling and balanced composition.
      - **Character/Element Details**: Bold colors and clean lines creating dynamic interactions between shapes.
      - **Background and Environment**: Strategic use of negative space to enhance the prominence of shapes.
      - **Lighting**: Subtle gradients or shadows to add depth without overpowering the shapes.
      - **Composition**: Asymmetrical or balanced arrangements that guide the viewer's eye through the illustration.
      - **Color Palette**: Vibrant and contrasting colors to create visual interest and emphasis.
      - **Mood/Emotion**: Energetic and modern, evoking a sense of creativity and abstraction.
      - **Style**: Contemporary abstract illustration with a focus on geometric forms and color dynamics.
    `,

  geometricIllustration: `
      **Example: Geometric Illustration**
      - **Subject/Scene**: Bold, vibrant color blocks forming precise geometric shapes.
      - **Character/Element Details**: Clean, simple lines with strong contrasts to define each shape clearly.
      - **Background and Environment**: Strategic use of negative space to ensure shapes stand out prominently.
      - **Lighting**: Minimal to none, maintaining a flat design aesthetic.
      - **Composition**: Structured and harmonious, with shapes arranged in a balanced manner.
      - **Color Palette**: Bright and bold colors with high contrast to emphasize geometric forms.
      - **Mood/Emotion**: Modern and striking, highlighting the beauty of simplicity and structure.
      - **Style**: Vector-based geometric illustration with a focus on color and form.
    `,

  inkWashIllustration: `
      **Example: Ink Wash Illustration**
      - **Subject/Scene**: Delicate scenes rendered with ink wash techniques.
      - **Character/Element Details**: Soft gradients and flowing lines creating depth.
      - **Background and Environment**: Subtle, textured backgrounds enhancing the ink's fluidity.
      - **Color Scheme**: Monochromatic with varying intensities of ink.
      - **Theme/Style**: Soft, ethereal.
      - **Mood/Tone**: Calm, graceful.
        `,
};

export const sketchExamples = {
  blackWhiteForms: `
      **Example: Black & White Forms Illustration**
      - **Subject/Scene**: Intricate and elaborate compositions utilizing black and white forms.
      - **Character/Element Details**: Pen and ink techniques with hyper-detailed line work to create complex patterns and textures.
      - **Background and Environment**: Complex, abstract patterns that complement the central forms; spontaneous artistry adding depth.
      - **Lighting**: High-contrast shading to emphasize details and depth.
      - **Composition**: Dense and layered, encouraging the viewer to explore the intricacies of the design.
      - **Color Palette**: Strictly black and white to maintain focus on form and detail.
      - **Mood/Emotion**: Intense and captivating, drawing the viewer into the complexity of the illustration.
      - **Style**: Detailed black and white illustration with a focus on form and texture.
    `,

  mixedMediaSketch: `
      **Example: Mixed Media Sketch**
      - **Subject/Scene**: Fusion of traditional sketching with other media like watercolor, ink, or digital elements.
      - **Character/Element Details**: Combination of sharp pencil lines and fluid watercolor washes creating a dynamic interplay between mediums.
      - **Background and Environment**: Layered textures and varying media techniques adding depth and complexity to the composition.
      - **Lighting**: Natural or soft lighting to highlight the textures and colors of mixed media elements.
      - **Composition**: Balanced yet eclectic, allowing different media to complement each other harmoniously.
      - **Color Palette**: Diverse colors from various media, blended to create a cohesive look.
      - **Mood/Emotion**: Creative and expressive, showcasing versatility and artistic exploration.
      - **Style**: Mixed media sketching with a focus on blending traditional and contemporary techniques.
    `,

  perspectiveSketch: `
  **Example: Perspective Sketch**
  - **Subject/Scene**: Architectural structures depicted with accurate perspective.
  - **Character/Element Details**: Precise lines and angles emphasizing depth.
  - **Background and Environment**: Urban landscapes with focus on spatial relationships.
  - **Color Scheme**: Monochromatic with emphasis on shading to enhance perspective.
  - **Theme/Style**: Technical, detailed.
  - **Mood/Tone**: Structured, meticulous.
    `,

  technicalSketch: `
    **Example: Technical Sketch**
    - **Subject/Scene**: Detailed technical drawings of machinery or inventions.
    - **Character/Element Details**: Precise annotations and measurements.
    - **Background and Environment**: Clean, grid-lined backgrounds for accuracy.
    - **Color Scheme**: Monochromatic with clear delineation of parts.
    - **Theme/Style**: Analytical, precise.
    - **Mood/Tone**: Professional, informative.
      `,

  sketchDrawHandPortrait: `
      **Example: Sketch Hand Portrait**
      - **Subject/Scene**: Monochromatic portraits emphasizing depth and detail through sketching techniques.
      - **Character/Element Details**: Precise stippling or hatching techniques using ink or pencil to create texture and shading.
      - **Background and Environment**: Intricate compositions with textured backgrounds that complement the portrait without overpowering it.
      - **Lighting**: Directional lighting to create shadows and highlights, adding three-dimensionality to the portrait.
      - **Composition**: Close-up framing focusing on facial features and expressions.
      - **Color Palette**: Black and white to maintain focus on shading and form.
      - **Mood/Emotion**: Intimate and expressive, capturing the essence of the subject through detailed sketching.
      - **Style**: Detailed hand-drawn sketch with a focus on realism and depth.
    `,

  handDrawn: `
      **Example: Hand-Drawn Sketch**
      - **Subject/Scene**: Organic, free-form shapes and lines creating a natural and spontaneous composition.
      - **Character/Element Details**: Natural line variations and artistic textures achieved through freehand drawing techniques.
      - **Background and Environment**: Loose, spontaneous strokes with minimal detail to maintain focus on the main elements.
      - **Lighting**: N/A for hand-drawn sketches, focusing instead on line weight and shading.
      - **Composition**: Fluid and unstructured, allowing for creative expression and movement within the illustration.
      - **Color Palette**: Typically monochromatic or limited color usage to preserve the hand-drawn aesthetic.
      - **Mood/Emotion**: Relaxed and expressive, showcasing the artist's personal style and spontaneity.
      - **Style**: Freehand sketching with an emphasis on organic forms and fluid lines.
    `,

  minimalSketch: `
    **Example: Minimal Sketch**
    - **Subject/Scene**: Simplified forms with an emphasis on essential lines.
    - **Character/Element Details**: Clean, sparse lines reducing the subject to its core elements.
    - **Background and Environment**: Ample negative space enhancing the minimalist aesthetic.
    - **Color Scheme**: Monochromatic or limited color palette.
    - **Theme/Style**: Minimalist, abstract.
    - **Mood/Tone**: Serene, understated.
      `,

  charcoalSketch: `
    **Example: Charcoal Sketch**
    - **Subject/Scene**: Dramatic and moody compositions using charcoal.
    - **Character/Element Details**: Bold, expressive strokes with rich textures.
    - **Background and Environment**: Darkened backgrounds with subtle highlights.
    - **Color Scheme**: Monochromatic with deep blacks and soft grays.
    - **Theme/Style**: Expressive, textured.
    - **Mood/Tone**: Intense, evocative.
      `,

  graphiteSketch: `
    **Example: Graphite Sketch**
    - **Subject/Scene**: Realistic renderings with fine graphite detailing.
    - **Character/Element Details**: Smooth shading and precise lines for lifelike features.
    - **Background and Environment**: Soft gradients and minimalistic backgrounds to highlight the subject.
    - **Color Scheme**: Monochromatic with varying shades of gray.
    - **Theme/Style**: Realistic, refined.
    - **Mood/Tone**: Calm, contemplative.
      `,

  architecturalPlanSketch: `
      **Example: Architectural Plan Sketch**
      - **Subject/Scene**: Detailed architectural plans for residential or commercial buildings.
      - **Character/Element Details**: Precise measurements, annotations, and symbols indicating structural elements like walls, doors, windows, and utilities.
      - **Background and Environment**: Grid lines and scale indicators to provide context and accuracy.
      - **Lighting**: N/A for technical sketches, focusing instead on clear line work and readability.
      - **Composition**: Organized layout with multiple views (e.g., floor plans, elevations, sections) arranged systematically.
      - **Color Palette**: Monochromatic (typically black and white) to emphasize clarity and precision.
      - **Mood/Emotion**: Professional and methodical, reflecting the technical nature of architectural planning.
      - **Style**: Technical sketching with a focus on accuracy, detail, and functional design.
    `,

  wireframeSketch: `
    **Example: Wireframe Sketch**
    - **Subject/Scene**: Basic skeletal framework of a user interface for websites or applications.
    - **Character/Element Details**: Simple lines and boxes representing UI elements like buttons, menus, images, and text areas; placeholder text and icons.
    - **Background and Environment**: Blank or lightly annotated background to focus on the structure of the interface.
    - **Lighting**: N/A for wireframes, emphasizing clear and precise line work.
    - **Composition**: Organized layout with hierarchical placement of elements to reflect user flow and interaction pathways.
    - **Color Palette**: Typically monochromatic or grayscale, using shades to differentiate elements without distraction.
    - **Mood/Emotion**: Functional and straightforward, aimed at planning and organizing interface elements before detailed design.
    - **Style**: Minimalist and utilitarian sketching, focusing on structure and usability over aesthetic details.
  `,

  storyboardSketch: `
    **Example: Storyboard Sketch**
    - **Subject/Scene**: Sequential panels illustrating the narrative flow of a story, project, or user journey.
    - **Character/Element Details**: Rough sketches of characters, key actions, and important events within each panel; arrows and annotations to indicate movement and transitions.
    - **Background and Environment**: Simplified settings to highlight the main actions and interactions in each scene.
    - **Lighting**: N/A for storyboards, focusing on clear depiction of actions and sequences.
    - **Composition**: Linear arrangement of panels in a logical sequence to guide the viewer through the story.
    - **Color Palette**: Typically black and white or limited colors to maintain focus on the narrative rather than visual details.
    - **Mood/Emotion**: Dynamic and illustrative, capturing the essence of the story's progression and emotional beats.
    - **Style**: Rough and expressive sketching, prioritizing storytelling and sequence over fine details.
  `,
};

export const digitalInterfaceExamples = {
  modernInterface: `
      **Example: Modern Interface**
      - **Subject/Scene**: Sleek and contemporary interface layout for digital applications.
      - **Character/Element Details**: Floating UI elements with subtle shadows and modern effects like neumorphism or glassmorphism.
      - **Background and Environment**: Smooth gradient overlays with rounded corners, creating a clean and sophisticated backdrop.
      - **Lighting**: Soft gradients and shadowing to add depth without overwhelming the interface elements.
      - **Composition**: Intuitive and user-friendly layout with clear navigation and accessible components.
      - **Color Palette**: Modern, muted tones with occasional vibrant accents to highlight key elements.
      - **Mood/Emotion**: Professional and inviting, ensuring ease of use and aesthetic appeal.
      - **Style**: Contemporary digital interface design focusing on usability and visual harmony.
    `,

  mobileAppInterface: `
      **Example: Mobile App Interface**
      - **Subject/Scene**: Contemporary mobile app layout optimized for user experience and visual appeal.
      - **Character/Element Details**: Floating UI elements with soft shadows and frosted glass effects, providing a modern and elegant look.
      - **Background and Environment**: Smooth gradient overlays with rounded corners and minimalistic patterns to maintain focus on functionality.
      - **Lighting**: Soft, diffused lighting effects to enhance UI elements without causing visual clutter.
      - **Composition**: User-centric design with intuitive navigation, clear hierarchy, and accessible interactive elements.
      - **Color Palette**: Soft, harmonious colors with strategic use of accent colors for calls-to-action.
      - **Mood/Emotion**: Clean and efficient, promoting a seamless and enjoyable user experience.
      - **Style**: Modern mobile interface design with a focus on aesthetics and functionality.
    `,

  webPlatformInterface: `
      **Example: Web Platform Interface**
      - **Subject/Scene**: Professional web interface designed for scalability and responsiveness across devices.
      - **Character/Element Details**: Sharp vector elements with subtle depth effects, ensuring clarity and precision in design.
      - **Background and Environment**: Clean navigation system with a clear content hierarchy, utilizing whitespace effectively to reduce clutter.
      - **Lighting**: Consistent lighting effects that maintain readability and visual comfort across different sections.
      - **Composition**: Structured layout with intuitive navigation, clear calls-to-action, and well-organized content sections.
      - **Color Palette**: Professional and cohesive color scheme that aligns with the brand identity.
      - **Mood/Emotion**: Trustworthy and user-friendly, encouraging engagement and interaction.
      - **Style**: Modern web interface design focusing on responsiveness, accessibility, and user experience.
    `,

  chartsInterface: `
      **Example: Charts Interface**
      - **Subject/Scene**: Data visualization charts designed for clarity and effectiveness in conveying information.
      - **Character/Element Details**: Clear, concise data representation using charts like bar graphs, pie charts, or line graphs with labeled axes and legends.
      - **Background and Environment**: Minimalist design with a focus on data; clean backgrounds that do not distract from the information presented.
      - **Lighting**: N/A for charts, but ensuring high contrast between data elements and background for readability.
      - **Composition**: Balanced layout with proportional spacing to ensure data is easily interpretable.
      - **Color Palette**: Distinct and contrasting colors to differentiate data sets, adhering to colorblind-friendly palettes where possible.
      - **Mood/Emotion**: Informative and analytical, prioritizing accuracy and clarity.
      - **Style**: Clean and modern data visualization design focusing on functionality and aesthetic simplicity.
    `,

  mockInterface: `
      **Example: Mock Interface**
      - **Subject/Scene**: Prototype interface designed for user testing and feedback.
      - **Character/Element Details**: Wireframe elements with placeholder text and images to represent functionality and layout without final design details.
      - **Background and Environment**: Simplistic background to focus attention on interface elements, using neutral colors to avoid distraction.
      - **Lighting**: N/A for mockups, focusing instead on clear and distinguishable UI components.
      - **Composition**: Logical and intuitive layout that mimics the intended user flow and interaction pathways.
      - **Color Palette**: Basic color scheme with high contrast for clarity, allowing easy identification of different interface elements.
      - **Mood/Emotion**: Functional and objective, aimed at facilitating user testing and iterative design improvements.
      - **Style**: Minimalist and utilitarian mockup design focusing on structure and usability over aesthetic details.
    `,

  isometricDashboardInterface: `
      **Example: Isometric Dashboard Interface**
      - **Subject/Scene**: Isometric view of a comprehensive dashboard for data monitoring and management.
      - **Character/Element Details**: 3D-styled widgets, charts, and interactive elements arranged in an isometric perspective to provide depth and dimension.
      - **Background and Environment**: Subtle isometric grid or layered elements to enhance the 3D effect without cluttering the interface.
      - **Lighting**: Soft, directional lighting to create shadows and highlights, enhancing the isometric illusion.
      - **Composition**: Organized layout with clear sections for different data categories, maintaining balance and hierarchy.
      - **Color Palette**: Harmonious colors with a mix of vibrant and muted tones to differentiate various elements while maintaining cohesiveness.
      - **Mood/Emotion**: Modern and dynamic, promoting an engaging and interactive user experience.
      - **Style**: Isometric illustration combined with modern UI design principles, emphasizing depth and spatial arrangement.
    `,

  isometricMobileAppInterface: `
      **Example: Isometric Mobile App Interface**
      - **Subject/Scene**: Isometric representation of a mobile app interface, showcasing various features and functionalities in a 3D perspective.
      - **Character/Element Details**: Isometric icons, buttons, and interactive elements arranged to simulate a three-dimensional user interface.
      - **Background and Environment**: Layered isometric planes with subtle gradients to enhance the 3D effect without distracting from the app elements.
      - **Lighting**: Consistent isometric lighting with shadows to create depth and highlight key interface components.
      - **Composition**: Intuitive arrangement with logical grouping of features, ensuring ease of navigation and interaction.
      - **Color Palette**: Balanced use of colors to differentiate sections while maintaining a unified aesthetic.
      - **Mood/Emotion**: Innovative and engaging, providing a visually appealing and interactive user experience.
      - **Style**: Isometric design integrated with mobile UI elements, focusing on depth, spatial relationships, and user interaction.
    `,

  isometricWebInterface: `
      **Example: Isometric Web Interface**
      - **Subject/Scene**: Isometric view of a web interface, illustrating various interactive components and layout structures in a 3D space.
      - **Character/Element Details**: Isometric panels, navigation bars, and content sections arranged to create a layered, three-dimensional appearance.
      - **Background and Environment**: Isometric grid background with subtle shading to reinforce the 3D perspective without overpowering the interface elements.
      - **Lighting**: Balanced isometric lighting to cast gentle shadows and highlights, enhancing the depth and realism of the interface.
      - **Composition**: Structured layout with clear divisions for different functionalities, ensuring usability and visual harmony.
      - **Color Palette**: Cohesive color scheme with distinct hues to differentiate various interface sections while maintaining overall consistency.
      - **Mood/Emotion**: Professional and dynamic, offering an engaging and interactive browsing experience.
      - **Style**: Isometric design fused with contemporary web interface aesthetics, emphasizing depth, structure, and user engagement.
    `,
};

export const examples = {
  photography: photographyExamples,
  illustration: illustrationExamples,
  sketch: sketchExamples,
  digitalInterface: digitalInterfaceExamples,
};

export const ImagePrompt = ({context, type}) => `
# How to Respond to This Prompt

To generate the output structure, execute the **[prompt]** provided below. The output must strictly adhere to the provided schema. Start the output with \`\`\`markdown and end with \`\`\`.

## Output Schema
\`\`\`markdown
- **Subject**: [Describe the main subject (max 30 words)]
- **Scene**: [Describe the scene (max 30 words)]
- **Background and Environment**: [Outline the background and environmental elements (max 50 words)]
- **Lighting**: [Describe the lighting techniques used (max 30 words)]
- **Composition**: [Explain the composition and framing (max 30 words)]
- **Color Palette**: [Detail the color scheme and its significance (max 30 words)]
- **Mood/Emotion**: [Describe the mood and emotional impact (max 30 words)]
- **Style**: [Explain the artistic style and its relevance (max 30 words)]
\`\`\`

## Examples

${Object.values(examples[type]).join('\n\n')}

# Prompt

## Role:
Visual Designer and Artist Photographer

## Task:

- Design a visually captivating image inspired by the provided **[context]**.
- Incorporate storytelling elements to create an emotional connection with the audience.
- Select an example from the provided **Examples** section that best aligns with the **[context]**.
- Use the chosen example as a foundation to maintain consistency in quality and structure.

### Guidelines:
- **Mindset**: You are a talented professional with many years of experience in visual design and photography. Your goal is to create an impactful image that leaves a lasting impression.
- **Emotional Resonance**: Evoke the desired emotional response that aligns with the project’s vision.
- **Visual Flow**: Utilize intentional design elements and composition to guide the viewer's eye seamlessly through the image.
- **Text-Free Design**: Exclude any text to maintain a clean and focused visual narrative.
- **Immediate Impact**: Ensure the image is attention-grabbing and leaves a lasting impression.
- **Emotion**: Evoke feelings that support the overall message and storytelling.

## Instructions

### 1. Subject
**Process**:
- **Step 1**: Select an example from the provided list that aligns with the **[context]**.
- **Step 2**: Use the selected example as a foundation to maintain consistency in quality and structure.
- **Step 3**: Define the main subject of the image, ensuring it aligns with the **[context]**.

**Requirements**:
- **Inspiration**: Draw inspiration from the selected example’s subject.
- **Description**: Define the main subject (e.g., object, person) as the focal point.
- **Engagement**: Ensure the subject is visually appealing and captures the essence of the **[context]**.
- **Minimalist**: Keep the subject simple and impactful, avoiding clutter or distractions.

**Restrictions**:
- **Complexity**: Avoid overly complex or cluttered subjects.
- **Alignment**: Ensure the subject aligns with the **[context]**.
- **No Multiple Characters or Objects**: Use one or two elements maximum to maintain focus and clarity.

### 2. Scene
**Process**:
- **Step 1**: Reference the selected example’s scene.
- **Step 2**: Describe the setting, including specific actions or interactions that tell a cohesive story or evoke emotion.
- **Step 3**: Create a scene that captivates and maintains the viewer’s attention.

**Requirements**:
- **Inspiration**: Draw from the selected example’s scene.
- **Details**: Describe the setting with clarity.
- **Engagement**: Ensure the scene is engaging and relevant to the **[context]**.

**Restrictions**:
- **Complexity**: Avoid overly intricate scenes that may overshadow the main subject.
- **Relevance**: Ensure the scene supports the overall narrative.

### 3. Character/Element Details
**Process**:
- **Step 1**: Analyze the selected example’s character or element details.
- **Step 2**: Create characters or elements that are relatable, engaging, and visually appealing.
- **Step 3**: Ensure the characters or elements convey the intended message effectively.

**Requirements**:
- **Inspiration**: Draw from the selected example’s character or element details.
- **Description**: Define the characters or elements with a focus on unique features and interactions.
- **Engagement**: Ensure they are relatable and engaging.
- **Minimalist**: Keep details simple and impactful.

**Restrictions**:
- **Complexity**: Avoid overly detailed or cluttered elements.
- **No Multiple Characters or Objects**: Limit to one or two to maintain focus.

### 4. Background and Environment
**Process**:
- **Step 1**: Utilize the selected example’s background.
- **Step 2**: Outline the setting using abstract shapes, minimalist layouts, or symbolic elements.
- **Step 3**: Ensure the background complements the main subject and maintains visual balance.

**Requirements**:
- **Inspiration**: Draw from the selected example’s background and environment.
- **Description**: Clearly outline the background elements.
- **Balance**: Maintain visual harmony between the background and the subject.

**Restrictions**:
- **Clutter**: Avoid busy or distracting backgrounds.
- **Relevance**: Ensure background elements support the **[context]**.

### 5. Lighting
**Process**:
- **Step 1**: Refer to the selected example’s lighting.
- **Step 2**: Describe lighting methods that create highlights and shadows, adding depth.
- **Step 3**: Use lighting to craft a dynamic and engaging visual experience.

**Requirements**:
- **Inspiration**: Draw from the example’s lighting techniques.
- **Techniques**: Specify lighting styles (e.g., Rembrandt, backlighting).
- **Atmosphere**: Use lighting to enhance the mood and focus.

**Restrictions**:
- **Overexposure/Underexposure**: Ensure the subject is well-lit and visible.

### 6. Composition
**Process**:
- **Step 1**: Base your composition on the selected example.
- **Step 2**: Arrange elements and framing to guide the viewer’s eye.
- **Step 3**: Ensure the composition aligns with the overall vision.

**Requirements**:
- **Inspiration**: Use the example’s composition as a reference.
- **Arrangement**: Detail the placement of elements for visual interest.
- **Balance**: Achieve visual harmony.

**Restrictions**:
- **Imbalance**: Avoid unbalanced compositions.
- **Clutter**: Maintain a clear and focused layout.

### 7. Color Palette
**Process**:
- **Step 1**: Maintain the color scheme from the selected example.
- **Step 2**: Explain your color choices and their role in conveying the mood.
- **Step 3**: Ensure the palette supports the overall message.

**Requirements**:
- **Consistency**: Use colors that align with the selected example.
- **Significance**: Highlight the importance of color in setting the mood.
- **Alignment**: Ensure the palette supports the overall narrative.

**Restrictions**:
- **Clashing Colors**: Avoid conflicting colors that distract.
- **Overuse**: Prevent overwhelming vibrancy.

### 8. Mood and Emotion
**Process**:
- **Step 1**: Draw from the selected example’s mood.
- **Step 2**: Define the intended mood or emotion and its impact on the viewer.
- **Step 3**: Use visual elements to convey emotions that resonate with the audience.

**Requirements**:
- **Inspiration**: Utilize the example’s emotional tone.
- **Description**: Clearly define the desired emotional impact.
- **Connection**: Ensure the mood supports the narrative.

**Restrictions**:
- **Inconsistency**: Avoid conflicting emotional tones.
- **Overemphasis**: Keep the emotion conveyed subtly.

### 9. Style
**Process**:
- **Step 1**: Base the artistic style on the selected example.
- **Step 2**: Ensure the style supports the overall narrative.
- **Step 3**: Use the style to create a cohesive and visually appealing image.

**Requirements**:
- **Consistency**: Maintain the style from the example.
- **Enhancement**: Use style elements to support storytelling and visual appeal.

**Restrictions**:
- **Style Mismatch**: Avoid styles that do not align with the overall vision.
- **Overcomplication**: Keep the style simple and effective.

## Additional Information:

- **Context**:
  """
  ${context}
  """
`;
