export const videoExamples = {
  cinematicNarrative: `
      **Example: Cinematic Narrative**
      - **Subject/Scene**: A solitary figure journeying through diverse landscapes.
      - **Scene**: Gradual transitions from urban to natural environments with deliberate pacing.
      - **Background and Environment**: Shifting backdrops that complement the subject's emotional arc.
      - **Lighting**: Natural and dramatic lighting, emphasizing golden hour and dusk tones.
      - **Camera Movement**: Smooth dolly shots and slow pans to build immersion.
      - **Editing Style**: Seamless crossfades and measured cuts that enhance narrative flow.
      - **Sound/Music**: Subtle orchestral score interwoven with ambient nature sounds.
      - **Color Palette**: Warm hues contrasted with cool shadows for visual depth.
      - **Mood/Emotion**: Reflective and poignant, evoking introspection and hope.
      - **Style**: Modern cinematic approach with an emphasis on storytelling and visual artistry.
    `,

  dynamicAction: `
      **Example: Dynamic Action**
      - **Subject/Scene**: High-energy chase sequences featuring agile characters.
      - **Scene**: Rapid transitions and kinetic movements through bustling urban settings.
      - **Background and Environment**: Vibrant cityscapes with pulsating urban energy.
      - **Lighting**: High-contrast lighting with dramatic shadows to heighten tension.
      - **Camera Movement**: Fast tracking shots and dynamic zooms to amplify excitement.
      - **Editing Style**: Quick cuts and energetic transitions that boost the adrenaline.
      - **Sound/Music**: Intense rhythmic soundtrack paired with sharp, impactful sound effects.
      - **Color Palette**: Bold, saturated colors with deep contrasts to emphasize urgency.
      - **Mood/Emotion**: Thrilling and pulse-pounding, creating a sense of urgency.
      - **Style**: Action-packed visual narrative with modern, dynamic cinematography.
    `,

  emotionalShortFilm: `
      **Example: Emotional Short Film**
      - **Subject/Scene**: Intimate interactions between characters in a heartfelt encounter.
      - **Scene**: Soft transitions between close-ups and expansive shots capturing subtle emotions.
      - **Background and Environment**: Cozy indoor settings that reflect personal connections.
      - **Lighting**: Warm, natural lighting to evoke comfort and intimacy.
      - **Camera Movement**: Subtle handheld or slow-tracking movements to enhance realism.
      - **Editing Style**: Gentle fades and slow dissolves to maintain a tender rhythm.
      - **Sound/Music**: A melancholic piano score underscored by ambient background sounds.
      - **Color Palette**: Muted tones with delicate highlights to underscore vulnerability.
      - **Mood/Emotion**: Poignant and reflective, evoking empathy and tenderness.
      - **Style**: Intimate, narrative-driven style with an emphasis on human emotion.
    `,

  documentaryStyle: `
      **Example: Documentary Style**
      - **Subject/Scene**: Real-life stories capturing genuine moments in everyday life.
      - **Scene**: Naturalistic sequences with observational camera work and minimal staging.
      - **Background and Environment**: Authentic settings that provide context and depth.
      - **Lighting**: Real-world, ambient lighting that maintains a sense of realism.
      - **Camera Movement**: Steady, unobtrusive shots that allow events to unfold organically.
      - **Editing Style**: Minimal transitions and clean cuts that preserve documentary authenticity.
      - **Sound/Music**: Natural soundscapes with minimal musical accompaniment.
      - **Color Palette**: True-to-life colors with subtle enhancements for clarity.
      - **Mood/Emotion**: Sincere and thought-provoking, inviting viewers into real-life narratives.
      - **Style**: Raw and authentic, prioritizing factual storytelling and realism.
    `,

  experimentalArtVideo: `
      **Example: Experimental Art Video**
      - **Subject/Scene**: Abstract visuals exploring themes of time, space, and perception.
      - **Scene**: Non-linear sequences with unconventional transitions and visual effects.
      - **Background and Environment**: Fluid, shifting digital landscapes that challenge perception.
      - **Lighting**: Bold and creative lighting setups that defy traditional norms.
      - **Camera Movement**: Erratic and avant-garde movements enhancing visual abstraction.
      - **Editing Style**: Innovative cuts, overlays, and effects to create a surreal narrative.
      - **Sound/Music**: Ambient, experimental soundscapes that complement the visual dissonance.
      - **Color Palette**: Vivid, contrasting colors with unexpected pairings.
      - **Mood/Emotion**: Ethereal and thought-provoking, evoking mystery and wonder.
      - **Style**: Experimental and avant-garde, challenging conventional storytelling norms.
    `,
};

export const VideoPrompt = ({context, type}) => `
# How to Respond to This Prompt

To generate the output structure, execute the **[prompt]** provided below. The output must strictly adhere to the provided schema. Start the output with \`\`\`markdown and end with \`\`\`.

## Output Schema
\`\`\`markdown
- **Subject**: [Describe the main subject (max 30 words)]
- **Scene**: [Describe the overall scene, including actions and transitions (max 40 words)]
- **Background and Environment**: [Outline the background and dynamic setting elements (max 50 words)]
- **Lighting**: [Describe the lighting techniques used (max 30 words)]
- **Camera Movement**: [Explain the camera movements or angles (max 30 words)]
- **Editing Style**: [Detail the editing techniques and transitions (max 30 words)]
- **Sound/Music**: [Describe the sound design or musical elements (max 30 words)]
- **Color Palette**: [Detail the color scheme and its significance (max 30 words)]
- **Mood/Emotion**: [Describe the mood and emotional impact (max 30 words)]
- **Style**: [Explain the cinematic style and its relevance (max 30 words)]
\`\`\`

## Examples

${Object.values(videoExamples[type]).join('\n\n')}

# Prompt

## Role:
Video Director and Cinematographer

## Task:

- Design a visually captivating video clip inspired by the provided **[context]**.
- Incorporate storytelling elements to create an emotional connection with the audience.
- Select an example from the provided **Examples** section that best aligns with the **[context]**.
- Use the chosen example as a foundation to maintain consistency in quality, pacing, and visual narrative.

### Guidelines:
- **Mindset**: You are a skilled video director with extensive experience in cinematic storytelling. Your goal is to create an impactful video clip that leaves a lasting impression.
- **Emotional Resonance**: Evoke the desired emotional response that aligns with the project’s vision.
- **Visual Flow**: Utilize intentional camera movements, transitions, and editing to guide the viewer's eye seamlessly through the narrative.
- **Text-Free Design**: Exclude any on-screen text to maintain a clean and immersive visual experience.
- **Immediate Impact**: Ensure the video is attention-grabbing from the first frame and sustains engagement throughout.
- **Emotion**: Use visuals, sound, and editing to evoke feelings that support the overall message and storytelling.

## Instructions

### 1. Subject
**Process**:
- **Step 1**: Select an example from the provided list that aligns with the **[context]**.
- **Step 2**: Use the selected example as a foundation to maintain consistency in quality and structure.
- **Step 3**: Define the main subject of the video clip, ensuring it aligns with the **[context]**.

**Requirements**:
- **Inspiration**: Draw inspiration from the selected example’s subject.
- **Description**: Define the primary object or character as the focal point.
- **Engagement**: Ensure the subject is visually appealing and captures the essence of the **[context]**.
- **Minimalist**: Keep the subject simple and impactful to avoid visual clutter.

**Restrictions**:
- **Complexity**: Avoid overly complex or distracting subjects.
- **Alignment**: Ensure the subject aligns with the overall narrative.
- **No Multiple Focal Points**: Use one or two main elements maximum.

### 2. Scene
**Process**:
- **Step 1**: Reference the selected example’s scene.
- **Step 2**: Describe the setting, including dynamic actions, transitions, or interactions that build the narrative.
- **Step 3**: Create a scene that captivates the viewer and supports the intended storytelling.

**Requirements**:
- **Inspiration**: Draw from the selected example’s scene.
- **Details**: Clearly outline the setting and actions.
- **Engagement**: Ensure the scene is dynamic and relevant to the **[context]**.

**Restrictions**:
- **Overcomplication**: Avoid overly intricate scenes that might detract from the main subject.
- **Relevance**: Ensure the scene supports the overall narrative.

### 3. Background and Environment
**Process**:
- **Step 1**: Utilize the selected example’s background elements.
- **Step 2**: Outline the setting using dynamic or symbolic elements that enhance the narrative.
- **Step 3**: Ensure the background complements the subject and contributes to the visual flow.

**Requirements**:
- **Inspiration**: Draw from the example’s background.
- **Description**: Clearly detail background elements that add depth.
- **Balance**: Maintain visual harmony between the background and the subject.

**Restrictions**:
- **Clutter**: Avoid busy or distracting backgrounds.
- **Irrelevance**: Ensure background elements support the **[context]**.

### 4. Lighting
**Process**:
- **Step 1**: Refer to the selected example’s lighting style.
- **Step 2**: Describe the lighting methods that create highlights, shadows, and depth.
- **Step 3**: Use lighting to enhance the mood and visual impact.

**Requirements**:
- **Inspiration**: Draw from the example’s lighting techniques.
- **Techniques**: Specify lighting styles (e.g., natural light, backlighting, or cinematic lighting).
- **Atmosphere**: Use lighting to enhance the narrative mood.

**Restrictions**:
- **Overexposure/Underexposure**: Ensure the subject is properly lit throughout.

### 5. Camera Movement
**Process**:
- **Step 1**: Plan dynamic camera movements inspired by the selected example.
- **Step 2**: Describe the camera angles, pans, tilts, or tracking shots that will enhance the video.
- **Step 3**: Use camera movement to add dynamism and guide the viewer’s attention.

**Requirements**:
- **Inspiration**: Draw from the selected example’s visual flow.
- **Techniques**: Clearly define movements (e.g., slow pan, dolly zoom).
- **Enhancement**: Ensure movements support the narrative without distraction.

**Restrictions**:
- **Jarring Movements**: Avoid abrupt or disorienting camera actions.

### 6. Editing Style
**Process**:
- **Step 1**: Base your editing approach on the selected example.
- **Step 2**: Describe transitions, cuts, and pacing that contribute to the storytelling.
- **Step 3**: Ensure the editing style maintains rhythm and visual coherence.

**Requirements**:
- **Inspiration**: Use the example’s pacing and transitions as reference.
- **Techniques**: Define editing methods (e.g., crossfade, jump cut).
- **Flow**: Maintain a cohesive and engaging narrative flow.

**Restrictions**:
- **Disruption**: Avoid editing styles that disrupt the narrative continuity.

### 7. Sound/Music
**Process**:
- **Step 1**: Consider sound design elements that complement the selected example.
- **Step 2**: Describe the musical score, ambient sounds, or sound effects that enhance the video’s impact.
- **Step 3**: Use sound to create emotional depth and reinforce the narrative.

**Requirements**:
- **Inspiration**: Draw from the mood and tone of the example.
- **Description**: Clearly outline the sound design or musical elements.
- **Impact**: Ensure the sound supports the visual narrative.

**Restrictions**:
- **Overpowering Audio**: Avoid sound that distracts from the visual storytelling.

### 8. Color Palette
**Process**:
- **Step 1**: Maintain consistency with the color scheme from the selected example.
- **Step 2**: Explain your color choices and how they contribute to the mood.
- **Step 3**: Ensure the palette supports the overall narrative and visual impact.

**Requirements**:
- **Consistency**: Use colors that align with the selected example.
- **Significance**: Highlight the importance of the color choices.
- **Balance**: Ensure the palette reinforces the emotional tone.

**Restrictions**:
- **Clashing Colors**: Avoid conflicting colors that disrupt the visual harmony.

### 9. Mood and Emotion
**Process**:
- **Step 1**: Draw from the selected example’s mood.
- **Step 2**: Define the intended mood or emotion and its effect on the viewer.
- **Step 3**: Use visual, audio, and editing elements to evoke the desired emotional response.

**Requirements**:
- **Inspiration**: Utilize the example’s emotional tone.
- **Description**: Clearly define the mood and emotional impact.
- **Connection**: Ensure the mood supports the narrative and storytelling.

**Restrictions**:
- **Inconsistency**: Avoid conflicting emotional cues.
- **Overemphasis**: Keep the emotion balanced and appropriate.

### 10. Style
**Process**:
- **Step 1**: Base the cinematic style on the selected example.
- **Step 2**: Ensure the style supports the narrative and overall visual aesthetic.
- **Step 3**: Use style elements to create a cohesive and visually striking video clip.

**Requirements**:
- **Consistency**: Maintain the style from the example.
- **Enhancement**: Use cinematic techniques to support the storytelling.
- **Relevance**: Ensure the style aligns with the overall vision.

**Restrictions**:
- **Style Mismatch**: Avoid styles that conflict with the intended narrative.
- **Overcomplication**: Keep the style streamlined and effective.

## Additional Information:

- **Context**:
  """
  ${context}
  """
`;
