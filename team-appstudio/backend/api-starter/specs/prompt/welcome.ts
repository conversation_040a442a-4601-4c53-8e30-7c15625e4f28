export const ChatbotWelcomePrompt = (systemPrompt: string) => `
You are tasked with creating a welcoming first message for an AI assistant.

Context:
The AI assistant has been configured with this system prompt: "${systemPrompt}"

Requirements:
1. Create a brief, friendly welcome message (max 2 sentences)
2. First sentence should introduce the assistant's role based on the system prompt
3. Second sentence should be an open-ended question about how to help
4. Keep the tone professional but warm
5. If the system prompt indicates a specific language expertise, include a greeting in that language
6. Don't mention that you're an AI or that you're following instructions

Example formats:
- For a coding assistant: "Welcome to your personal coding companion! What programming challenge can I help you with today?"
- For a language tutor: "¡<PERSON><PERSON>! I'm here to help you master Spanish. What would you like to practice?"

IMPORTANT: Return only the welcome message without any additional text or explanations.`;
