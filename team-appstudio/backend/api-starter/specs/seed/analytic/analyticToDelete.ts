import {AnalyticEvent} from '@prisma/client';
import {SeedSpec} from 'specs/specs.interface';

export const analyticToDelete: SeedSpec = [
  {
    provider: 'AnalyticController',
    action: 'create',
    params: [
      'auth.user',
      {
        name: 'Analytic to delete',
        objectType: 'news',
        objectId: 'seed.newsToRead.result.id',
        event: AnalyticEvent.view,
      },
    ],
  },
];
