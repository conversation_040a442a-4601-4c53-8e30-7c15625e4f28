import {AnalyticEvent} from '@prisma/client';
import {SeedSpec} from 'specs/specs.interface';

export const analyticToFind1: SeedSpec = [
  {
    provider: 'AnalyticController',
    action: 'create',
    params: [
      'auth.user',
      {
        name: 'Analytic to find 1',
        objectType: 'news',
        objectId: 'seed.newsToFind.result.id',
        event: AnalyticEvent.view,
      },
    ],
  },
];
