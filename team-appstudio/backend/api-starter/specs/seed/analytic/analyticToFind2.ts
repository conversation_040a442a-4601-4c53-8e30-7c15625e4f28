import {AnalyticEvent} from '@prisma/client';
import {SeedSpec} from 'specs/specs.interface';

export const analyticToFind2: SeedSpec = [
  {
    provider: 'AnalyticController',
    action: 'create',
    params: [
      'auth.user',
      {
        name: 'Analytic to find 2',
        objectType: 'news',
        objectId: 'seed.newsToFind.result.id',
        event: AnalyticEvent.view,
      },
    ],
  },
];
