import {AnalyticEvent} from '@prisma/client';
import {SeedSpec} from 'specs/specs.interface';

export const analyticToRead: SeedSpec = [
  {
    provider: 'AnalyticController',
    action: 'create',
    params: [
      'auth.user',
      {
        name: 'Analytic to read',
        event: AnalyticEvent.view,
        objectType: 'news',
        objectId: 'seed.newsToRead.result.id',
      },
    ],
  },
];
