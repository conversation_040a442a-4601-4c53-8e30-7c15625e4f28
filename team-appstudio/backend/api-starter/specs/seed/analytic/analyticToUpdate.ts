import { AnalyticEvent } from '@prisma/client';
import {SeedSpec} from 'specs/specs.interface';

export const analyticToUpdate: SeedSpec = [
  {
    provider: 'AnalyticController',
    action: 'create',
    params: [
      'auth.user',
      {
        name: 'Analytic to update',
        objectType:"news",
        objectId:"seed.newsToUpdate.result.id",
        event:AnalyticEvent.view
      },
    ],
  },
];
