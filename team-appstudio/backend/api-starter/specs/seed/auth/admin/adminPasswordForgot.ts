import {SeedSpec} from 'specs/specs.interface';

export const adminPasswordForgot: SeedSpec = [
  {
    provider: 'AuthAdminController',
    action: 'signUp',
    params: [
      {
        email: '<EMAIL>',
        password: 'mock.password',
        name: '<PERSON>lin Password Forgotten',
      },
      'fr',
    ],
  },
  {
    provider: 'AuthAdminController',
    action: 'forgotPassword',
    saveAs: 'adminPasswordForgotToken',
    params: [
      {
        email: '<EMAIL>',
      },
      'fr',
    ],
  },
];
