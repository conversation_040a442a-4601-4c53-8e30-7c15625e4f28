import {SeedSpec} from 'specs/specs.interface';

export const userPasswordForgot: SeedSpec = [
  {
    provider: 'AuthUserController',
    action: 'signUp',
    params: [
      {
        email: '<EMAIL>',
        password: 'mock.password',
        name: 'User Password Forgotten',
      },
      'fr',
    ],
  },
  {
    provider: 'AuthUserController',
    action: 'forgotPassword',
    saveAs: 'userPasswordForgotToken',
    params: [
      {
        email: '<EMAIL>',
      },
      'fr',
    ],
  },
];
