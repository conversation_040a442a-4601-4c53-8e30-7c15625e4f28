import {SeedSpec} from 'specs/specs.interface';

export const commentToReport: SeedSpec = [
  {
    provider: 'CommentController',
    action: 'create',
    params: [
      'auth.user',
      {
        text: 'mock.paragraph',
        objectId: 'seed.newsToRead.result.id',
        objectType: 'news',
      },
    ],
  },
  {
    provider: 'CommentController',
    action: 'reportComment',
    params: [
      'result.id',
      'auth.user',
      {
        text: 'mock.paragraph',
      },
    ],
  },
];
