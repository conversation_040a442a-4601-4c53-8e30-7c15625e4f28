import {SeedSpec} from 'specs/specs.interface';
import {pickRnd} from 'src/mock/mock.utils';

const fields = {
  danceStyle: ['House', 'Hip-hop', 'R&B', 'Rap', 'Rock'],
  category: ['news', 'course', 'casting'],
  eventDanceStyle: ['House', 'Hip-hop', 'R&B', 'Rap', 'Rock'],
  eventCategory: ['news', 'casting', 'course'],
  newsCategory: ['medias', 'innewss'],
};

export function pickSpeceficFieldByIndex(key, index) {
  return 'seed.field_' + key + '_' + index + '.result.id';
}
export function pickRndField(key) {
  // console.log('pickRndField', name);
  return (
    'seed.field_' +
    key +
    '_' +
    Math.floor(Math.random() * fields[key].length) +
    '.result.id'
  );
}

export const fieldDynamic = Object.keys(fields).flatMap(key =>
  fields[key].map((value, index) => ({
    provider: 'FieldController',
    action: 'createDynamic',
    saveAs: 'field_' + key + '_' + index,
    params: [
      {
        key,
        name: value,
        value,
      },
    ],
  })),
);
