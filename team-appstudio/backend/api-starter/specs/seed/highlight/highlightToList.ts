import {SeedSpec} from 'specs/specs.interface';

export const highlightToList: SeedSpec = [
  {
    provider: 'HighlightController',
    action: 'create',
    params: [
      {
        id: 'news',
        location: 'home',
        value: 'seed.newsToRead.result',
      },
    ],
  },

  {
    provider: 'HighlightController',
    action: 'create',
    params: [
      {
        id: 'testHighlight',
        location: 'mock.name',
        value: {},
      },
    ],
  },
];
