import {NewsCategory} from '@prisma/client';
import {SeedSpec} from 'specs/specs.interface';

export const newsToShowOnHome: SeedSpec = [
  {
    provider: 'NewsController',
    action: 'create',
    params: [
      //'auth.admin',
      {
        title: 'News on Home',
      },
    ],
  },
  {
    provider: 'NewsController',
    action: 'update',
    params: [
      'result.id',
      {
        imageUrl: 'mock.banner',
        description: 'mock.paragraph',
        publicationDate: 'mock.today',
        category: NewsCategory.innewss,
      },
    ],
  },
];
