import {SeedSpec} from 'specs/specs.interface';

export const usersForDataTable: SeedSpec = [
  // User 1
  {
    provider: 'AuthUserController',
    action: 'signUp',
    params: [
      {
        email: '<EMAIL>',
        password: 'password123',
        name: '<PERSON>',
      },
      'en',
    ],
    saveAs: 'dataTableUser1',
  },
  {
    provider: 'ProfileController',
    action: 'update',
    params: [
      'result',
      {
        country: 'US',
        gender: 'man',
        job: 'Software Developer',
      },
    ],
  },

  // User 2
  {
    provider: 'AuthUserController',
    action: 'signUp',
    params: [
      {
        email: '<EMAIL>',
        password: 'password123',
        name: '<PERSON>',
      },
      'en',
    ],
    saveAs: 'dataTableUser2',
  },
  {
    provider: 'ProfileController',
    action: 'update',
    params: [
      'result',
      {
        country: 'UK',
        gender: 'woman',
        job: 'Product Manager',
      },
    ],
  },
  // User 3
  {
    provider: 'AuthUserController',
    action: 'signUp',
    params: [
      {
        email: 'micha<PERSON>.<EMAIL>',
        password: 'password123',
        name: '<PERSON>',
      },
      'en',
    ],
    saveAs: 'dataTableUser3',
  },
  {
    provider: 'ProfileController',
    action: 'update',
    params: [
      'result',
      {
        country: 'CA',
        gender: 'man',
        job: 'Data Scientist',
      },
    ],
  },

  // User 4
  {
    provider: 'AuthUserController',
    action: 'signUp',
    params: [
      {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Emily Davis',
      },
      'en',
    ],
    saveAs: 'dataTableUser4',
  },
  {
    provider: 'ProfileController',
    action: 'update',
    params: [
      'result',
      {
        country: 'AU',
        gender: 'woman',
        job: 'UX Designer',
      },
    ],
  },

  // User 5
  {
    provider: 'AuthUserController',
    action: 'signUp',
    params: [
      {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Robert Wilson',
      },
      'en',
    ],
    saveAs: 'dataTableUser5',
  },
  {
    provider: 'ProfileController',
    action: 'update',
    params: [
      'result',
      {
        country: 'DE',
        gender: 'man',
        job: 'Project Manager',
      },
    ],
  },
  // {
  //   provider: 'UserController',
  //   action: 'block',
  //   params: ['result.id'],
  // },

  // User 6
  {
    provider: 'AuthUserController',
    action: 'signUp',
    params: [
      {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Olivia Brown',
      },
      'en',
    ],
    saveAs: 'dataTableUser6',
  },
  {
    provider: 'ProfileController',
    action: 'update',
    params: [
      'result',
      {
        country: 'FR',
        gender: 'woman',
        job: 'Marketing Specialist',
      },
    ],
  },

  // User 7
  {
    provider: 'AuthUserController',
    action: 'signUp',
    params: [
      {
        email: '<EMAIL>',
        password: 'password123',
        name: 'William Taylor',
      },
      'en',
    ],
    saveAs: 'dataTableUser7',
  },
  {
    provider: 'ProfileController',
    action: 'update',
    params: [
      'result',
      {
        country: 'ES',
        gender: 'man',
        job: 'Sales Manager',
      },
    ],
  },
  // {
  //   provider: 'UserController',
  //   action: 'block',
  //   params: ['result.id'],
  // },

  // User 8
  {
    provider: 'AuthUserController',
    action: 'signUp',
    params: [
      {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Sophia Martinez',
      },
      'en',
    ],
    saveAs: 'dataTableUser8',
  },
  {
    provider: 'ProfileController',
    action: 'update',
    params: [
      'result',
      {
        country: 'IT',
        gender: 'woman',
        job: 'Content Writer',
      },
    ],
  },
];
