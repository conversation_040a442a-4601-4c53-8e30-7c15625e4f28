import {ApiResponseOptions} from '@nestjs/swagger';
import {Access} from 'src/acl/acl.interface';

export interface CodeStorySpec {
  auth?: string;
  path?: any;
  file?: string;
  body?: string | any;
  query?: string | any;
}
export interface StoryTest {
  type: 'contains' | 'equal' | 'exists';
  data?: any;
  success?: boolean;
}

export interface CodeResponse {
  status: string;
  description: string;
}

export type StorySpec = {
  filePath: string;
  route: string;
  method: 'POST' | 'GET' | 'DELETE' | 'PATCH' | string;
  access?: Access;
  operation: {
    summary: string;
  };
  skipTest?: boolean;
  codes: {
    [code: string]: {
      response: ApiResponseOptions;
      story?: CodeStorySpec;
      tests?: StoryTest[];
    };
  };
};

export type SeedConfig = {
  /**
   * Service or Controller  to call
   */
  provider: string;
  /**
   * Method called by the provider
   */
  action: string;
  /**
   * parameters used by the action
   */
  params: any[];

  /**
   * Save  data with a key
   */
  saveAs?: string;
};

/**
 * list of action to be called to seed the application
 */
export type SeedSpec = SeedConfig[];

export type Seed = {
  /**
   * list of Seed loaded at start in the application
   */
  [seedId: string]: SeedSpec;
};
