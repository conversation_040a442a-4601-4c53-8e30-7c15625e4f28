import {StorySpec} from 'specs/specs.interface';

export const accessToCreate: StorySpec = {
  filePath: __filename,
  route: '/access',
  method: 'POST',
  operation: {
    summary: 'As an admin, I want to create a access',
  },
  access: {
    resource: 'access',
    action: 'create',
  },
  codes: {
    201: {
      response: {
        status: 201,
        description: 'access created',
      },
    },
    401: {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
  },
};
