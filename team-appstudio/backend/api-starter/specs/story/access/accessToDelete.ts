import {StorySpec} from 'specs/specs.interface';

export const accessToDelete: StorySpec = {
  filePath: __filename,
  route: '/access/:id',
  method: 'DELETE',
  operation: {
    summary: 'As an admin, I want to delete a access',
  },
  access: {
    resource: 'access',
    action: 'delete',
  },
  codes: {
    200: {
      response: {
        status: 200,
        description: 'access deleted',
      },
    },
    403: {
      response: {
        status: 403,
        description: 'Incorrect credentials',
      },
    },
    404: {
      response: {
        status: 404,
        description: 'access not found',
      },
    },
  },
};
