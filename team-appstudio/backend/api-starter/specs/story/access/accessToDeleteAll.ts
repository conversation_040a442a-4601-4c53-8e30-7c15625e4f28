import {StorySpec} from 'specs/specs.interface';

export const accessToDeleteAll: StorySpec = {
  filePath: __filename,
  route: '/access/delete',
  method: 'DELETE',
  operation: {
    summary: 'As an admin, I want to delete all access records',
  },
  access: {
    resource: 'access',
    action: 'delete',
  },
  codes: {
    200: {
      response: {
        status: 200,
        description: 'All access records deleted',
      },
    },
    403: {
      response: {
        status: 403,
        description: 'Incorrect credentials',
      },
    },
  },
};
