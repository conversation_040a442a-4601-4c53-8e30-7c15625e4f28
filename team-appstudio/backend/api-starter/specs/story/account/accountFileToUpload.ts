import {StorySpec} from 'specs/specs.interface';

export const accountFileToUpload: StorySpec = {
  filePath: __filename,
  skipTest: true,
  route: '/account/file',
  method: 'POST',
  operation: {
    summary: 'As a user, i want to upload a file',
  },
  access: {
    resource: 'account',
    action: 'update',
    owner: true,
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'user file created',
      },
      story: {
        auth: 'user',
        body: {fileUrl: 'mock.image'},
      },
    },
    '401': {
      response: {
        status: 401,
        description: 'Not authorized',
      },
      story: {},
    },
  },
};
