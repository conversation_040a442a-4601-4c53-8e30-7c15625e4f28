import {StorySpec} from 'specs/specs.interface';
export const accountNotificationToCreate: StorySpec = {
  filePath: __filename,
  route: '/account/notification',
  method: 'POST',
  operation: {
    summary: 'As a user, i want to create a fake notification',
  },
  access: {
    resource: 'notification',
    action: 'create',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Notifications data.',
      },
      story: {
        auth: 'user',
        body: {
          link: 'mock.notificationLink',
          title: {
            fr: 'mock.description',
            en: 'mock.description',
          },
          message: {
            fr: 'mock.description',
            en: 'mock.description',
          },
          type: 'mock.notificationType',
        },
      },
    },
    '401': {
      response: {
        status: 401,
        description: 'Not authorized',
      },
    },
  },
};
