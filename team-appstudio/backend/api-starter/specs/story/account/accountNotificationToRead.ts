import {StorySpec} from 'specs/specs.interface';
export const accountNotificationToRead: StorySpec = {
  filePath: __filename,
  route: '/account/notification/:id',
  method: 'GET',
  operation: {
    summary: 'As a user, i want to mark as read a notification',
  },
  access: {
    resource: 'notification',
    action: 'read',
    owner: true,
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Notifications data.',
      },
      story: {
        auth: 'user',
        path: {
          id: 'seed.notificationToRead.result.id',
        },
      },
    },
    '401': {
      response: {
        status: 401,
        description: 'Not authorized',
      },
    },
  },
};
