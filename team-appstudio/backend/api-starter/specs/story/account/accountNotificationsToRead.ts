import {StorySpec} from 'specs/specs.interface';

export const accountNotificationsToRead: StorySpec = {
  filePath: __filename,
  route: '/account/notification',
  method: 'GET',
  operation: {
    summary: 'As a user, i want to read my notifications',
  },
  access: {
    resource: 'notification',
    action: 'read',
    owner: true,
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Notifications data.',
      },
      story: {
        auth: 'user',
      },
    },
    '401': {
      response: {
        status: 401,
        description: 'Not authorized',
      },
    },
  },
};
