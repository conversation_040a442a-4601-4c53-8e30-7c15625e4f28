import {StorySpec} from 'specs/specs.interface';

export const accountToRead: StorySpec = {
  filePath: __filename,
  route: '/account',
  method: 'GET',
  operation: {
    summary: 'As a user, i want to read my account',
  },
  access: {
    resource: 'account',
    action: 'read',
    owner: true,
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Account data.',
      },
      story: {
        auth: 'user',
      },
    },
    '401': {
      response: {
        status: 401,
        description: 'Not authorized',
      },
    },
  },
};
