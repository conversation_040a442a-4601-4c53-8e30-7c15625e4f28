import {StorySpec} from 'specs/specs.interface';

export const accountToUpdate: StorySpec = {
  filePath: __filename,
  route: '/account',
  method: 'PATCH',
  operation: {
    summary: 'As a user, i want to update my account',
  },
  access: {
    resource: 'account',
    action: 'update',
    owner: true,
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'account update succeed',
      },
      story: {
        auth: 'user',
        body: {birthdate: 'mock.date(10, 8, 1987)'},
      },
      tests: [
        {
          type: 'equal',
          data: {birthdate: 'params.birthdate'},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Not authorized',
      },
    },
  },
};
