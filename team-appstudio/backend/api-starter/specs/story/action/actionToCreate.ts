import {StorySpec} from 'specs/specs.interface';

export const actionToCreate: StorySpec = {
  filePath: __filename,
  route: '/action',
  method: 'POST',
  operation: {
    summary: 'As an admin, I want to create a action',
  },
  access: {
    resource: 'action',
    action: 'create',
  },
  codes: {
    201: {
      response: {
        status: 201,
        description: 'action created',
      },
    },
    401: {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
  },
};
