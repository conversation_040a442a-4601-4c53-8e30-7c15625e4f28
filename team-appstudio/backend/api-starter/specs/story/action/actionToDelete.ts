import {StorySpec} from 'specs/specs.interface';

export const actionToDelete: StorySpec = {
  filePath: __filename,
  route: '/action',
  method: 'DELETE',
  operation: {
    summary: 'As an admin, I want to delete a action',
  },
  access: {
    resource: 'action',
    action: 'delete',
  },
  codes: {
    200: {
      response: {
        status: 200,
        description: 'action deleted',
      },
    },
    403: {
      response: {
        status: 403,
        description: 'Incorrect credentials',
      },
    },
    404: {
      response: {
        status: 404,
        description: 'action not found',
      },
    },
  },
};
