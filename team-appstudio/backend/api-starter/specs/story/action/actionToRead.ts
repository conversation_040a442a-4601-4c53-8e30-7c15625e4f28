import {StorySpec} from 'specs/specs.interface';

export const actionToRead: StorySpec = {
  filePath: __filename,
  route: '/action/:id',
  method: 'GET',
  operation: {
    summary: 'As a user, I want to read a specific action',
  },
  access: {
    resource: 'action',
    action: 'read',
  },
  codes: {
    200: {
      response: {
        status: 200,
        description: 'action details fetched',
      },
    },
    404: {
      response: {
        status: 404,
        description: 'action not found',
      },
    },
  },
};
