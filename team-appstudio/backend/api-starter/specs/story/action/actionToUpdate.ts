import {StorySpec} from 'specs/specs.interface';

export const actionToUpdate: StorySpec = {
  filePath: __filename,
  route: '/action/:id',
  method: 'PATCH',
  operation: {
    summary: 'As an admin, I want to update a action',
  },
  access: {
    resource: 'action',
    action: 'update',
  },
  codes: {
    200: {
      response: {
        status: 200,
        description: 'action updated',
      },
    },
    401: {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
    404: {
      response: {
        status: 404,
        description: 'action not found',
      },
    },
  },
};
