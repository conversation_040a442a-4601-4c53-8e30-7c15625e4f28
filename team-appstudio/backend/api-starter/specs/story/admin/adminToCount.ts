import {StorySpec} from 'specs/specs.interface';

export const adminToCount: StorySpec = {
  filePath: __filename,
  route: '/admin/count',
  method: 'POST',
  operation: {
    summary: 'As a admin, i want to count admin found by name',
  },
  access: {
    resource: 'admin',
    action: 'read',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Number of Exemples found',
      },
      story: {
        auth: 'admin',
        body: {
          name: 'Admin to find',
        },
      },
      tests: [
        {
          type: 'equal',
          data: 2,
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
  },
};
