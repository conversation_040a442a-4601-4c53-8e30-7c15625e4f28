import {Right} from '@prisma/client';
import {StorySpec} from 'specs/specs.interface';

export const adminToCreate: StorySpec = {
  filePath: __filename,
  route: '/admin',
  method: 'POST',
  operation: {
    summary: 'create an admin',
  },
  access: {
    resource: 'admin',
    action: 'create',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Admin Created Succeed',
      },
      story: {
        auth: 'admin',
        body: {
          name: 'mock.name',
          email: 'mock.email',
          contents: 'mock.contents',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {name: 'params.name'},
        },
      ],
    },
    '400': {
      response: {
        status: 400,
        description: 'Required fields is empty.',
      },
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
    '403': {
      response: {
        status: 403,
        description: 'Forbidden. You do not have the rights.',
      },
    },
  },
};
