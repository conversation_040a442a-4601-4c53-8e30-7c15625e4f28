import {StorySpec} from 'specs/specs.interface';

export const adminToDelete: StorySpec = {
  filePath: __filename,
  route: '/admin/:id',
  method: 'DELETE',
  operation: {
    summary: 'As a admin, i want to delete an admin',
  },
  access: {
    resource: 'admin',
    action: 'delete',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Admin delete',
      },
      story: {
        auth: 'admin',

        path: {
          id: 'seed.adminToDelete.result.id',
        },
      },
      tests: [
        {
          type: 'equal',
          success: true,
        },
      ],
    },
    '404': {
      response: {
        status: 404,
        description: "Admin doesn't exists",
      },
      story: {
        auth: 'admin',
        path: {
          id: 'seed.adminToDelete.result.id',
        },
      },
    },
  },
};
