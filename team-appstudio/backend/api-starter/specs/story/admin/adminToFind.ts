import {StorySpec} from 'specs/specs.interface';

export const adminToFind: StorySpec = {
  filePath: __filename,
  route: '/admin/find',
  method: 'POST',
  operation: {
    summary: 'As a admin, i want to find by name',
  },
  access: {
    resource: 'admin',
    action: 'read',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'admins found',
      },
      story: {
        auth: 'admin',
        body: {
          name: 'admin to find',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {length: 2},
        },
      ],
    },
  },
};
