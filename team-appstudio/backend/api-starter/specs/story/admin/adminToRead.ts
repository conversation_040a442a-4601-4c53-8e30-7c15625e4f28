import {StorySpec} from 'specs/specs.interface';

export const adminToRead: StorySpec = {
  filePath: __filename,
  route: '/admin/:id',
  method: 'GET',
  operation: {
    summary: 'As a admin, i want to read an admin',
  },
  access: {
    resource: 'admin',
    action: 'read',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: "Exemple's data",
      },
      story: {
        auth: 'admin',
        path: {
          id: 'seed.adminToRead.result.id',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {name: 'seed.adminToRead.params[1].name'},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
    '404': {
      response: {
        status: 404,
        description: "Exemple doesn't exists",
      },
    },
  },
};
