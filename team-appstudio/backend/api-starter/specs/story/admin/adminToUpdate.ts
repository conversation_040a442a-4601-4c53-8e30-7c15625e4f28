import {StorySpec} from 'specs/specs.interface';

export const adminToUpdate: StorySpec = {
  filePath: __filename,
  route: '/admin/:id',
  method: 'PATCH',
  operation: {
    summary: 'as a admin, i want to update an admin',
  },
  access: {
    resource: 'admin',
    action: 'update',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Admin Updated',
      },
      story: {
        auth: 'admin',
        path: {
          id: 'seed.adminToUpdate.result.id',
        },
        body: {
          name: 'Updated Admin',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {name: 'params.name'},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
    '404': {
      response: {
        status: 404,
        description: "Exemple doesn't exists",
      },
      story: {
        auth: 'admin',
        path: {
          id: 'unknowExempleToRead',
        },
      },
    },
  },
};
