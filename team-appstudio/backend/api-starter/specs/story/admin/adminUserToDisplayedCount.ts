import {StorySpec} from 'specs/specs.interface';

export const adminUserToDisplayedCount: StorySpec = {
  filePath: __filename,
  route: '/admin/user/displayedCount',
  method: 'GET',
  operation: {
    summary: 'as Admin return the number of user to display',
  },
  access: {
    resource: 'home',
    action: 'read',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Return userCount datas.',
      },
      story: {
        auth: 'user',
      },
    },
    '400': {
      response: {
        status: 400,
        description: 'userCount data is empty.',
      },
    },
    '404': {
      response: {
        status: 404,
        description: 'userCount is not found.',
      },
    },
  },
};
