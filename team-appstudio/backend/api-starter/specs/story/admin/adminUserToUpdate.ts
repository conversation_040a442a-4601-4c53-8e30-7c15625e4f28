import {StorySpec} from 'specs/specs.interface';

export const adminUserToUpdate: StorySpec = {
  filePath: __filename,
  route: '/admin/user/displayedCount',
  method: 'POST',
  operation: {
    summary: 'As an Admin I want to update userCount',
  },
  access: {
    resource: 'home',
    action: 'update',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Displayed Count updated.',
      },
      story: {
        auth: 'admin',
        body: {
          value: 1245,
        },
      },
      tests: [
        {
          type: 'equal',
          data: {value: 1245},
        },
      ],
    },
    '400': {
      response: {
        status: 400,
        description: 'Displayed Count not updated.',
      },
    },
    '403': {
      response: {
        status: 403,
        description: 'Not authorized.',
      },
    },
    '404': {
      response: {
        status: 404,
        description: 'Displayed Count number is not found.',
      },
    },
  },
};
