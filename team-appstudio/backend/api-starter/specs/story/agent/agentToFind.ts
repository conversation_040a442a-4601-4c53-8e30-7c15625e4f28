import {StorySpec} from 'specs/specs.interface';

export const agentToFind: StorySpec = {
  filePath: __filename,
  route: '/chat/agent/find',
  method: 'POST',
  operation: {
    summary: 'Find agents by criteria',
  },
  access: {
    resource: 'agent',
    action: 'read',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'List of agents matching criteria',
      },
      story: {
        auth: 'admin',
        body: {
          name: 'Spanish',
          take: 10,
          skip: 0,
        },
      },
      tests: [
        {
          type: 'exists',
          data: {length: true},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Unauthorized',
      },
    },
  },
};
