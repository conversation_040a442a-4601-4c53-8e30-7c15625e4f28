import {StorySpec} from 'specs/specs.interface';

export const agentToUpdate: StorySpec = {
  filePath: __filename,
  route: '/chat/agent/:id',
  method: 'PATCH',
  operation: {
    summary:
      'Update an existing agent with optional AI-regeneration of details',
  },
  access: {
    resource: 'agent',
    action: 'update',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Agent updated successfully',
        schema: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Agent ID',
            },
            name: {
              type: 'string',
              description:
                'Agent name (can be AI-regenerated if only systemPrompt is provided)',
            },
            description: {
              type: 'string',
              description:
                'Agent description (can be AI-regenerated if only systemPrompt is provided)',
            },
            systemPrompt: {
              type: 'string',
              description: 'System prompt used to initialize the agent',
            },
            starters: {
              type: 'array',
              items: {
                type: 'string',
              },
              description:
                'Conversation starters (can be AI-regenerated if only systemPrompt is provided)',
            },
          },
        },
      },
      story: {
        auth: 'user',
        path: {
          id: 'seed.agentToUpdate.result.id',
        },
        body: {
          systemPrompt:
            'You are an updated Spanish language teacher. You help users learn Spanish vocabulary, grammar, and cultural aspects. You specialize in teaching beginners and intermediate students.',
        },
      },
      tests: [
        {
          type: 'exists',
          data: {id: true, systemPrompt: true},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Unauthorized',
      },
    },
    '404': {
      response: {
        status: 404,
        description: 'Agent not found',
      },
    },
  },
};
