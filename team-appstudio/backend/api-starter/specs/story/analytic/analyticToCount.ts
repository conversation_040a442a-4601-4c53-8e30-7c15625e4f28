import {StorySpec} from 'specs/specs.interface';

export const analyticToCount: StorySpec = {
  filePath: __filename,
  route: '/analytic/count',
  method: 'POST',
  operation: {
    summary: 'As a user, i want to count analytics found by name',
  },
  access: {
    resource: 'analytic',
    action: 'read',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Number of Analytics found',
      },
      story: {
        auth: 'user',
        body: {
          name: 'Analytic to find',
        },
      },
      tests: [
        {
          type: 'equal',
          data: 2,
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
  },
};
