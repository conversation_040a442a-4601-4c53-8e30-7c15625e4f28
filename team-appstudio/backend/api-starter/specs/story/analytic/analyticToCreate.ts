import {StorySpec} from 'specs/specs.interface';

export const analyticToCreate: StorySpec = {
  filePath: __filename,
  route: '/analytic',
  method: 'POST',
  operation: {
    summary: 'As a user, i want to create an analytic',
  },
  access: {
    resource: 'analytic',
    action: 'create',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Analytic created',
      },
      story: {
        auth: 'user',
        body: {
          objectId: 'seed.newsToRead.result.id',
          objectType: 'news',
          event: 'view',
        },
      },
      tests: [
        {
          type: 'contains',
          data: {numberOfViews: 'params.name'},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
  },
};
