import {StorySpec} from 'specs/specs.interface';

export const analyticToDelete: StorySpec = {
  filePath: __filename,
  route: '/analytic/:id',
  method: 'DELETE',
  operation: {
    summary: 'As a user, i want to delete an analytic',
  },
  access: {
    resource: 'analytic',
    action: 'delete',
    owner: true,
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Analytic delete',
      },
      story: {
        auth: 'user',

        path: {
          id: 'seed.analyticToDelete.result.id',
        },
      },
      tests: [
        {
          type: 'equal',
          success: true,
        },
      ],
    },
    '403': {
      response: {
        status: 403,
        description: 'Incorrect credentials',
      },
      story: {
        auth: 'userAlreadyRegistered',
        path: {
          id: 'seed.analyticToFind1.result.id',
        },
      },
    },
    '404': {
      response: {
        status: 404,
        description: "Analytic doesn't exists",
      },
      story: {
        auth: 'user',
        path: {
          id: 'seed.analyticToDelete.result.id',
        },
      },
    },
  },
};
