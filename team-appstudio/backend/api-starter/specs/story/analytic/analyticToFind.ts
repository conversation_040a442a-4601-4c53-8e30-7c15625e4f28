import {StorySpec} from 'specs/specs.interface';

export const analyticToFind: StorySpec = {
  filePath: __filename,
  route: '/analytic/find',
  method: 'POST',
  operation: {
    summary: 'As a user, i want to find by name',
  },
  access: {
    resource: 'analytic',
    action: 'read',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Analytics found',
      },
      story: {
        auth: 'user',
        body: {
          name: 'Analytic to find',
        },
      },
      tests: [
        {
          type: 'equal',

          data: {length: 2},
        },
      ],
    },
  },
};
