import {StorySpec} from 'specs/specs.interface';

export const analyticToRead: StorySpec = {
  filePath: __filename,
  route: '/analytic/view',
  method: 'POST',
  operation: {
    summary: 'As a user, i want to read an analytic',
  },
  access: {
    resource: 'analytic',
    action: 'read',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: "Analytic's data",
      },
      story: {
        auth: 'admin',
        body: {
          objectId: 'seed.newsToRead.result.id',
          objectType: 'news',
        },
      },
      tests: [
        {
          type: 'contains',
          data: {numberOfViews: 0},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
    '404': {
      response: {
        status: 404,
        description: "Analytic doesn't exists",
      },
    },
  },
};
