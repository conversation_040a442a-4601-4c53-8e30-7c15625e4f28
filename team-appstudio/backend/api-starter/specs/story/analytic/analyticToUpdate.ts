import {StorySpec} from 'specs/specs.interface';

export const analyticToUpdate: StorySpec = {
  filePath: __filename,
  route: '/analytic',
  method: 'PATCH',
  operation: {
    summary: 'as a user, i want to update an analytic',
  },
  access: {
    resource: 'analytic',
    action: 'update',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Analytic Updated',
      },
      story: {
        auth: 'user',
        path: {
          id: 'seed.analyticToUpdate.result.id',
        },
        body: {
          name: 'Updated Analytic',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {name: 'params.name'},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
    '404': {
      response: {
        status: 404,
        description: "Analytic doesn't exists",
      },
      story: {
        auth: 'user',
        path: {
          id: 'unknowAnalyticToRead',
        },
      },
    },
  },
};
