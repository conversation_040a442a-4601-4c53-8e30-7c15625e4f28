import {StorySpec} from 'specs/specs.interface';

export const audioToUploadAndTranscribe: StorySpec = {
  filePath: __filename,
  route: '/workflow/audio/transcribe',
  method: 'POST',
  operation: {
    summary: 'Upload and transcribe an audio file',
  },
  access: {
    resource: 'audio',
    action: 'create',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'The workflow ID for the upload and transcription task',
      },
      story: {
        auth: 'user',
        body: {
          file: 'mock.audio',
        },
      },
    },
    '401': {
      response: {
        status: 401,
        description: 'Unauthorized',
      },
    },
  },
};
