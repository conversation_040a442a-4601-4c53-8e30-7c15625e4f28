import {StorySpec} from 'specs/specs.interface';

export const adminToForgotPassword: StorySpec = {
  filePath: __filename,
  route: '/auth/admin/forgotPassword',
  method: 'POST',
  operation: {
    summary: 'As a admin, i want remenber my password',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Admin email sended',
      },
      story: {
        body: {
          email: 'seed.admin.params[0].email',
        },
      },
    },
    '404': {
      response: {
        status: 404,
        description: 'Admin Not Found',
      },
      story: {
        body: {
          email: '<EMAIL>',
        },
      },
    },
  },
};
