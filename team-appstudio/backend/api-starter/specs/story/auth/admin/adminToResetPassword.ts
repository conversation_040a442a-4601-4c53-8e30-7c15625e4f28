import {StorySpec} from 'specs/specs.interface';

export const adminToResetPassword: StorySpec = {
  filePath: __filename,
  route: '/auth/admin/resetPassword',
  method: 'POST',
  operation: {
    summary: 'As a admin, i want reset my password',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'password updated',
      },
      story: {
        body: {
          newPassword: 'mock.password',
          passwordToken: 'seed.adminPasswordForgotToken.result.token',
        },
      },
    },
    '404': {
      response: {
        status: 404,
        description: 'Token Not found',
      },
      story: {
        body: {
          newPassword: 'mock.password',
          passwordToken: 'wrongToken',
        },
      },
    },
  },
};
