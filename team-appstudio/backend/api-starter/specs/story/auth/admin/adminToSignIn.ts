import {StorySpec} from 'specs/specs.interface';

export const adminToSignIn: StorySpec = {
  filePath: __filename,
  route: '/auth/admin/signIn',
  method: 'POST',
  operation: {
    summary: 'As an admin, i want to login',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Login succeed',
      },
      story: {
        body: {
          email: 'seed.admin.params[0].email',
          password: 'seed.admin.params[0].password',
        },
      },
      tests: [
        {
          type: 'exists',
          data: {token: true},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
      story: {
        body: {
          email: 'seed.admin.params[0].email',
          password: 'error',
        },
      },
    },
  },
};
