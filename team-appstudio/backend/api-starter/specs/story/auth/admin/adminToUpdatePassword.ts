import {StorySpec} from 'specs/specs.interface';

export const adminToUpdatePassword: StorySpec = {
  filePath: __filename,
  route: '/auth/admin/password',
  method: 'PATCH',
  operation: {
    summary: 'As a admin, i want update my password',
  },
  access: {
    resource: 'auth',
    action: 'update',
    owner: true,
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Password has been updated.',
      },
      story: {
        auth: 'adminWantingToChangePassword',
        body: {
          oldPassword: 'seed.adminWantingToChangePassword.params[0].password',
          newPassword: 'mock.password',
        },
      },
    },
    '403': {
      response: {
        status: 403,
        description: "Old Password doesn't Found match",
      },
      story: {
        auth: 'adminWantingToChangePassword',
        body: {
          oldPassword: 'WrongPassword',
          newPassword: 'mock.password',
        },
      },
    },
  },
};
