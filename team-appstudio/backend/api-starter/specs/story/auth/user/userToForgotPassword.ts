import {StorySpec} from 'specs/specs.interface';

export const userToForgotPassword: StorySpec = {
  filePath: __filename,
  route: '/auth/user/forgotPassword',
  method: 'POST',
  operation: {
    summary: 'As a user, i want remenber my password',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'User email sended',
      },
      story: {
        body: {
          email: 'seed.user.params[0].email',
        },
      },
    },
    '404': {
      response: {
        status: 404,
        description: 'User Not Found',
      },
      story: {
        body: {
          email: '<EMAIL>',
        },
      },
    },
  },
};
