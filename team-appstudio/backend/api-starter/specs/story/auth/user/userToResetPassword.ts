import {StorySpec} from 'specs/specs.interface';

export const userToResetPassword: StorySpec = {
  filePath: __filename,
  route: '/auth/user/resetPassword',
  method: 'POST',
  operation: {
    summary: 'As a user, i want reset my password',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'password updated',
      },
      story: {
        body: {
          newPassword: 'mock.password',
          passwordToken: 'seed.userPasswordForgotToken.result.token',
        },
      },
    },
    '404': {
      response: {
        status: 404,
        description: 'Token Not found',
      },
      story: {
        body: {
          newPassword: 'mock.password',
          passwordToken: 'wrongToken',
        },
      },
    },
  },
};
