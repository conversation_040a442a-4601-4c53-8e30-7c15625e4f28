import {StorySpec} from 'specs/specs.interface';

export const userToSignIn: StorySpec = {
  filePath: __filename,
  route: '/auth/user/signIn',
  method: 'POST',
  operation: {
    summary: 'As a user, i want to sign in',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Login succeed',
      },
      story: {
        body: {
          email: 'seed.user.params[0].email',
          password: 'seed.user.params[0].password',
        },
      },
      tests: [
        {
          type: 'exists',
          data: {token: true},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
      story: {
        body: {
          email: 'seed.user.params[0].email',
          password: 'error',
        },
      },
    },
  },
};
