import {StorySpec} from 'specs/specs.interface';

export const userToSignUp: StorySpec = {
  filePath: __filename,
  route: '/auth/user/signUp',
  method: 'POST',
  operation: {
    summary: 'As a user, i want sign up',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Registration succeed',
      },
      story: {
        body: {
          email: 'mock.email',
          password: 'mock.password',
          name: 'mock.name',
        },
      },
      tests: [
        {
          type: 'contains',
          data: {
            name: 'params.name',
          },
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'User already registered',
      },
      story: {
        body: 'seed.userAlreadyRegistered.params[0]',
      },
    },
  },
};
