import {StorySpec} from 'specs/specs.interface';

export const userToUpdatePassword: StorySpec = {
  filePath: __filename,
  route: '/auth/user/password',
  method: 'PATCH',
  operation: {
    summary: 'As a user, i want update my password',
  },
  access: {
    resource: 'auth',
    action: 'update',
    owner: true,
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Password has been updated.',
      },
      story: {
        auth: 'userWantingToChangePassword',
        body: {
          oldPassword: 'seed.userWantingToChangePassword.params[0].password',
          newPassword: 'mock.password',
        },
      },
    },
    '403': {
      response: {
        status: 403,
        description: "Old Password doesn't Found match",
      },
      story: {
        auth: 'userWantingToChangePassword',
        body: {
          oldPassword: 'WrongPassword',
          newPassword: 'mock.password',
        },
      },
    },
  },
};
