import {StorySpec} from 'specs/specs.interface';

export const chatMessageToCreateLiveMessage: StorySpec = {
  filePath: __filename,
  route: '/chat/chatMessage/create/live',
  method: 'POST',
  operation: {
    summary: 'Create a new message in real-time for live conversations',
  },
  access: {
    resource: 'chatMessage',
    action: 'create',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Message created successfully',
        schema: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Message ID',
            },
            conversationId: {
              type: 'string',
              description: 'ID of the conversation this message belongs to',
            },
            userId: {
              type: 'string',
              description: 'ID of the user who created the message',
            },
            role: {
              type: 'string',
              description: 'Message role (user, assistant, or system)',
              enum: ['user', 'assistant', 'system', 'model'],
            },
            content: {
              type: 'string',
              description: 'Message content',
            },
            messageType: {
              type: 'string',
              description: 'Type of message (text, audio, etc.)',
              enum: ['text', 'audio', 'image', 'video'],
            },
            metadata: {
              type: 'object',
              description: 'Additional metadata for the message',
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'When the message was created',
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              description: 'When the message was last updated',
            },
          },
        },
      },
      story: {
        auth: 'user',
        body: {
          conversationId: 'seed.conversation.result.id',
          content: 'This is a live message',
          role: 'user',
          messageType: 'text',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {content: 'params.content'},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Unauthorized',
      },
    },
  },
};
