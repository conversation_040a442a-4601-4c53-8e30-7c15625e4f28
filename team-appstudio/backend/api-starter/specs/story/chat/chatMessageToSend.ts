import {StorySpec} from 'specs/specs.interface';

export const chatMessageToSend: StorySpec = {
  filePath: __filename,
  route: '/chat/chatMessage/send',
  method: 'POST',
  operation: {
    summary: 'Send a message to chat',
  },
  access: {
    resource: 'chatMessage',
    action: 'create',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Message sent successfully',
      },
      story: {
        auth: 'user',
        body: {
          message: 'mock.paragraph',
          conversationId: 'seed.conversation.result.id',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {message: 'params.message'},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
  },
};
