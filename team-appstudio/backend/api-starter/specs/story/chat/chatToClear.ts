import {StorySpec} from 'specs/specs.interface';

export const chatToClear: StorySpec = {
  filePath: __filename,
  route: '/chat/chatConversation/clear/:id',
  method: 'DELETE',
  operation: {
    summary: 'Clear all messages in a chat conversation',
  },
  access: {
    resource: 'chatConversation',
    action: 'update',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Chat messages cleared successfully',
      },
      story: {
        auth: 'user',
        path: {
          id: 'seed.chat.result.id',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {cleared: true},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Unauthorized access',
      },
    },
    '404': {
      response: {
        status: 404,
        description: 'Chat not found',
      },
    },
  },
};
