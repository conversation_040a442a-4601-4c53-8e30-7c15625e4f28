import {StorySpec} from 'specs/specs.interface';

export const chatToFind: StorySpec = {
  filePath: __filename,
  route: '/chat/chatConversation/find',
  method: 'POST',
  operation: {
    summary: 'Find chat conversations',
  },
  access: {
    resource: 'chatConversation',
    action: 'read',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'List of conversations retrieved successfully',
      },
      story: {
        auth: 'user',
      },
      tests: [
        {
          type: 'equal',
          data: {length: 1},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
  },
};
