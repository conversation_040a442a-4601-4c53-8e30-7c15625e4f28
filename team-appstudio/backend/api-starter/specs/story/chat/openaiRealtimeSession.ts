import {StorySpec} from 'specs/specs.interface';

export const openaiRealtimeSession: StorySpec = {
  filePath: __filename,
  route: '/chat/openai-realtime/session',
  method: 'GET',
  operation: {
    summary: 'Get an ephemeral OpenAI Realtime session token',
  },
  access: {
    resource: 'chat',
    action: 'read',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Session token retrieved successfully',
        schema: {
          type: 'object',
          properties: {
            sessionToken: {
              type: 'string',
              description: 'OpenAI realtime session token',
            },
            expiresAt: {
              type: 'string',
              format: 'date-time',
              description: 'Token expiration timestamp',
            },
          },
        },
      },
      story: {
        auth: 'user',
      },
      tests: [
        {
          type: 'exists',
          data: {sessionToken: true},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Unauthorized',
      },
    },
  },
};