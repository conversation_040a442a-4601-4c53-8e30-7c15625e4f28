import {StorySpec} from 'specs/specs.interface';

export const answerToCreate: StorySpec = {
  filePath: __filename,
  route: '/comment/answer',
  method: 'POST',
  operation: {
    summary: 'As a user, i want to comment a comment',
  },
  access: {
    resource: 'comment',
    action: 'create',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Comment created',
      },
      story: {
        auth: 'user',
        body: {
          commentId: 'seed.commentToRead.result.id',
          text: 'mock.paragraph',
          objectId: 'seed.newsToRead.result.id',
          objectType: 'news',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {text: 'params.text'},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
  },
};
