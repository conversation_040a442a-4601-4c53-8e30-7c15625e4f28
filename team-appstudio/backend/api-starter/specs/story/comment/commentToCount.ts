import {StorySpec} from 'specs/specs.interface';

export const commentToCount: StorySpec = {
  filePath: __filename,
  route: '/comment/find',
  method: 'POST',
  operation: {
    summary: 'As a user, i want to count comments by objectType & objectId',
  },
  access: {
    resource: 'comment',
    action: 'read',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Number of Comments found',
      },
      story: {
        auth: 'user',
        body: {
          objectType: 'news',
          objectId: 'seed.newsToRead.result.id',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {length: 7},
        },
      ],
    },
  },
};
