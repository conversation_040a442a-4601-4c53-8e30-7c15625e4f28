import {StorySpec} from 'specs/specs.interface';

export const commentToCreate: StorySpec = {
  filePath: __filename,
  route: '/comment',
  method: 'POST',
  operation: {
    summary: 'As a user, i want to create a comment',
  },
  access: {
    resource: 'comment',
    action: 'create',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Comment created',
      },
      story: {
        auth: 'user',
        body: {
          text: 'mock.paragraph',
          objectId: 'seed.newsToRead.result.id',
          objectType: 'news',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {text: 'params.text'},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
  },
};
