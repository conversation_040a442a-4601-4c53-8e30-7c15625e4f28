import {StorySpec} from 'specs/specs.interface';

export const commentToDelete: StorySpec = {
  filePath: __filename,
  route: '/comment/:id',
  method: 'DELETE',
  operation: {
    summary: 'As a user, i want to delete a comment',
  },
  access: {
    resource: 'comment',
    action: 'delete',
    owner: true,
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Comment delete',
      },
      story: {
        auth: 'user',
        path: {
          id: 'seed.commentToDelete.result.id',
        },
      },
      tests: [
        {
          type: 'equal',
          success: true,
        },
      ],
    },
    '403': {
      response: {
        status: 403,
        description: 'Incorrect credentials',
      },
      story: {
        auth: 'userAlreadyRegistered',
        path: {
          id: 'seed.commentToFind1.result.id',
        },
      },
    },
    '404': {
      response: {
        status: 404,
        description: "Comment doesn't exists",
      },
      story: {
        auth: 'user',
        path: {
          id: 'seed.commentToDelete.result.id',
        },
      },
    },
  },
};
