import {StorySpec} from 'specs/specs.interface';

export const commentToDeleteByAdmin: StorySpec = {
  filePath: __filename,
  route: '/comment/admin/:id',
  method: 'DELETE',
  operation: {
    summary: 'As a admin, i want to delete a comment',
  },
  access: {
    resource: 'comment',
    action: 'delete',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Comment delete',
      },
      story: {
        auth: 'admin',
        path: {
          id: 'seed.commentToDeleteByAdmin.result.id',
        },
      },
      tests: [
        {
          type: 'equal',
          success: true,
        },
      ],
    },
    '403': {
      response: {
        status: 403,
        description: 'Incorrect credentials',
      },
      story: {
        auth: 'userAlreadyRegistered',
        path: {
          id: 'seed.commentToFind1.result.id',
        },
      },
    },
  },
};
