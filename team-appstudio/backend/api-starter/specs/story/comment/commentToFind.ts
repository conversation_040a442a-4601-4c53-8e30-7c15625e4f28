import {StorySpec} from 'specs/specs.interface';

export const commentToFind: StorySpec = {
  filePath: __filename,
  route: '/comment/find',
  method: 'POST',
  operation: {
    summary: 'As a user, i want to find by objectType & objectId',
  },
  access: {
    resource: 'comment',
    action: 'read',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Comment found',
      },
      story: {
        auth: 'user',
        body: {
          objectType: 'news',
          objectId: 'seed.newsToRead.result.id',
        },
      },
      tests: [
        {
          type: 'equal',

          data: {length: 7},
        },
      ],
    },
  },
};
