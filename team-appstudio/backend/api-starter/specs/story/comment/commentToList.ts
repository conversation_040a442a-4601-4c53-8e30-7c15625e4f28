import {StorySpec} from 'specs/specs.interface';

export const commentToList: StorySpec = {
  filePath: __filename,
  skipTest: true,
  route: '/comment/list',
  method: 'GET',
  operation: {
    summary: 'As a user, i want to read an comment',
  },
  access: {
    resource: 'comment',
    action: 'read',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: "Comment's data",
      },
      story: {
        auth: 'user',
        path: {
          id: 'seed.commentToRead.result.id',
          objectId: 'seed.newsToRead.result.id',
          objectType: 'news',
        },
      },
      tests: [
        {
          type: 'equal',

          data: {length: 1},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
    '404': {
      response: {
        status: 404,
        description: "Exemple doesn't exists",
      },
    },
  },
};
