import {StorySpec} from 'specs/specs.interface';

export const commentToUpdate: StorySpec = {
  filePath: __filename,
  route: '/comment/:id',
  method: 'PATCH',
  operation: {
    summary: 'as a user, i want to update a comment',
  },
  access: {
    resource: 'comment',
    action: 'update',
    owner: true,
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Comment Updated',
      },
      story: {
        auth: 'user',
        path: {
          id: 'seed.commentToUpdate.result.id',
        },
        body: {
          text: 'Updated Comment',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {text: 'params.text'},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
    '404': {
      response: {
        status: 404,
        description: "Comment doesn't exists",
      },
      story: {
        auth: 'user',
        path: {
          id: 'unknowCommentToRead',
        },
      },
    },
  },
};
