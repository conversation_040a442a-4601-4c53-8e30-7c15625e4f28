import {StorySpec} from 'specs/specs.interface';

export const commentToUpdateByAdmin: StorySpec = {
  filePath: __filename,
  route: '/comment/admin/:id',
  method: 'PATCH',
  operation: {
    summary: 'as a admin, i want to update a comment',
  },
  access: {
    resource: 'comment',
    action: 'update',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Comment Updated by admin',
      },
      story: {
        auth: 'admin',
        path: {
          id: 'seed.commentToUpdateByAdmin.result.id',
        },
        body: {
          text: 'Updated Comment by admin',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {text: 'params.text'},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
  },
};
