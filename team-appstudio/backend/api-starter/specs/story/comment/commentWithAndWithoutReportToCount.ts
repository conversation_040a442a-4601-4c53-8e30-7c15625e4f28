import {StorySpec} from 'specs/specs.interface';

export const commentWithAndWithoutReportToCount: StorySpec = {
  filePath: __filename,
  route: '/comment/reported/count',
  method: 'POST',
  operation: {
    summary:
      'As a admin, i want to count by objectType & objectId & reported status',
  },
  access: {
    resource: 'reportComment',
    action: 'read',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Comment without report count',
      },
      story: {
        body: {
          objectId: 'seed.newsToRead.result.id',
          objectType: 'news',
        },
      },
      tests: [
        {
          type: 'equal',
          data: 8,
        },
      ],
    },
  },
};
