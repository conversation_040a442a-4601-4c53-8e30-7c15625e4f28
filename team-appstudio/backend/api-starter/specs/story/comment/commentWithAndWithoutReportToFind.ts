import {StorySpec} from 'specs/specs.interface';

export const commentWithAndWithoutReportToFind: StorySpec = {
  filePath: __filename,
  route: '/comment/reported',
  method: 'POST',
  operation: {
    summary:
      'As a admin, i want to find by objectType & objectId & reported status',
  },
  access: {
    resource: 'reportComment',
    action: 'read',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Comment without report found',
      },
      story: {
        body: {
          objectId: 'seed.newsToRead.result.id',
          objectType: 'news',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {length: 8},
        },
      ],
    },
  },
};
