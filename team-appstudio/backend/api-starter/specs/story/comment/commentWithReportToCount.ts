import {StorySpec} from 'specs/specs.interface';

export const commentWithReportToCount: StorySpec = {
  filePath: __filename,
  route: '/comment/reported/count',
  method: 'POST',
  operation: {
    summary:
      'As a admin, i want to count by objectType & objectId & reported status',
  },
  access: {
    resource: 'reportComment',
    action: 'read',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Comment with report count',
      },
      story: {
        body: {
          objectId: 'seed.newsToRead.result.id',
          objectType: 'news',
          reported: true,
        },
      },
      tests: [
        {
          type: 'equal',
          data: 1,
        },
      ],
    },
  },
};
