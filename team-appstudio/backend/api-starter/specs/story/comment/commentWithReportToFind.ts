import {StorySpec} from 'specs/specs.interface';

export const commentWithReportToFind: StorySpec = {
  filePath: __filename,
  route: '/comment/reported',
  method: 'POST',
  operation: {
    summary:
      'As a admin, i want to find by objectType & objectId & reported status',
  },
  access: {
    resource: 'reportComment',
    action: 'read',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Comment with report found',
      },
      story: {
        body: {
          objectId: 'seed.newsToRead.result.id',
          objectType: 'news',
          reported: true,
        },
      },
      tests: [
        {
          type: 'equal',
          data: {length: 1},
        },
      ],
    },
  },
};
