{"resource": "report", "story": {"report": [{"controller": "create", "description": "report a comment", "params": [{"name": "report"}], "tests": [{"type": "equal", "result": {"name": "report"}}]}]}, "routes": {"/report": {"POST": {"operation": {"summary": "Create report"}, "access": {"type": "user", "roles": ["user"]}, "codes": {"200": {"response": {"status": 200, "description": "report created"}}}}, "GET": {"operation": {"summary": "Return all the report"}, "access": {"type": "user", "roles": ["user"]}, "codes": {"200": {"response": {"status": 200, "description": "return list of report"}}, "400": {"response": {"status": 400, "description": "Report not found"}}, "404": {"response": {"status": 404, "description": "Report not found"}}}}}, "/report/:id": {"GET": {"operation": {"summary": "Return a report"}, "access": {"type": "report", "roles": ["report"]}, "codes": {"200": {"response": {"status": 200, "description": "Return a report"}}, "400": {"response": {"status": 400, "description": "Report not found"}}, "404": {"response": {"status": 404, "description": "Report not found"}}}}, "PATCH": {"operation": {"summary": "Update a report"}, "access": {"type": "report", "roles": ["report"]}, "codes": {"200": {"response": {"status": 200, "description": "Update a comment"}}, "404": {"response": {"status": 404, "description": "Report not found"}}}}}, "/report/list": {"GET": {"operation": {"summary": "Return a list of report"}, "access": {"type": "report", "roles": ["report"]}, "codes": {"200": {"response": {"status": 200, "description": "Return list of report"}}, "400": {"response": {"status": 400, "description": "Report not found"}}, "404": {"response": {"status": 404, "description": "Report not found"}}}}}}}