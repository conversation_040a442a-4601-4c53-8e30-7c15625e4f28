import {StorySpec} from 'specs/specs.interface';

export const reportCommentToCreate: StorySpec = {
  filePath: __filename,
  route: '/comment/:id/report',
  method: 'POST',
  operation: {
    summary: 'As a user, i want to create a report comment',
  },
  access: {
    resource: 'reportComment',
    action: 'create',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Report Comment created',
      },
      story: {
        auth: 'user',
        path: {
          id: 'seed.commentToRead.result.id',
        },
        body: {
          text: 'mock.paragraph',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {text: 'params.text'},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
  },
};
