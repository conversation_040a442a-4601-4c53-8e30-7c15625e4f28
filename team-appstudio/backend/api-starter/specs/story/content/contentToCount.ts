import {StorySpec} from 'specs/specs.interface';

export const contentToCount: StorySpec = {
  filePath: __filename,
  route: '/content/count',
  method: 'POST',
  operation: {
    summary: 'As a admin, i want to count contents found by name',
  },
  access: {
    resource: 'content',
    action: 'read',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Number of contents found',
      },
      story: {
        body: {
          name: 'seed.contentToRead.result.name',
        },
      },
      tests: [
        // {
        //   type: 'equal',
        //   data: 10,
        // },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
  },
};
