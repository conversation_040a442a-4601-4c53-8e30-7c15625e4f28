import {Right} from '@prisma/client';
import {StorySpec} from 'specs/specs.interface';

export const contentToCreate: StorySpec = {
  filePath: __filename,
  route: '/content',
  method: 'POST',
  operation: {
    summary: 'As a admin, i want to create an content',
  },
  access: {
    resource: 'content',
    action: 'create',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'content created',
      },
      story: {
        auth: 'admin',
        body: {
          name: Right.News,
          rightType: 'Read',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {
            name: 'seed.contentToRead.result.name',
          },
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
  },
};
