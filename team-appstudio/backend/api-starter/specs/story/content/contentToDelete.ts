import {StorySpec} from 'specs/specs.interface';

export const contentToDelete: StorySpec = {
  filePath: __filename,
  route: '/content/:id',
  method: 'DELETE',
  operation: {
    summary: 'As a admin, i want to delete an content',
  },
  access: {
    resource: 'content',
    action: 'delete',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'content delete',
      },
      story: {
        auth: 'admin',
        path: {
          id: 'seed.contentToDelete.result.id',
        },
      },
      tests: [
        {
          type: 'equal',
          success: true,
        },
      ],
    },
    '404': {
      response: {
        status: 404,
        description: "content doesn't exists",
      },
      story: {
        auth: 'admin',
        path: {
          id: 'seed.contentToDelete.result.id',
        },
      },
    },
  },
};
