import {Right} from '@prisma/client';
import {StorySpec} from 'specs/specs.interface';

export const contentToFind: StorySpec = {
  filePath: __filename,
  route: '/content/find',
  method: 'POST',
  operation: {
    summary: 'As a admin, i want to find by name',
  },
  access: {
    resource: 'content',
    action: 'read',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'contents found',
      },
      story: {
        auth: 'admin',
        body: {
          name: 'seed.contentToRead.result.name',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {length: 10},
        },
      ],
    },
  },
};
