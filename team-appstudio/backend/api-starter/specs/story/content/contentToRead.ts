import {StorySpec} from 'specs/specs.interface';

export const contentToRead: StorySpec = {
  filePath: __filename,
  route: '/content/:id',
  method: 'GET',
  operation: {
    summary: 'As a admin, i want to read an content',
  },
  access: {
    resource: 'content',
    action: 'read',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: "content's data",
      },
      story: {
        auth: 'admin',
        path: {
          id: 'seed.contentToRead.result.id',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {name: 'seed.contentToRead.params[1].name'},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
    '404': {
      response: {
        status: 404,
        description: "content doesn't exists",
      },
    },
  },
};
