import {Right, RightType} from '@prisma/client';
import {StorySpec} from 'specs/specs.interface';

export const contentToUpdate: StorySpec = {
  filePath: __filename,
  route: '/content/:id',
  method: 'PATCH',
  operation: {
    summary: 'as a admin, i want to update an content',
  },
  access: {
    resource: 'content',
    action: 'update',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'content Updated',
      },
      story: {
        auth: 'admin',
        path: {
          id: 'seed.contentToUpdate.result.id',
        },
        body: {
          name: Right.News,
          rightType: RightType.Read,
        },
      },
      tests: [
        {
          type: 'equal',
          data: {name: 'seed.contentToUpdate.result.name'},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
    '404': {
      response: {
        status: 404,
        description: "content doesn't exists",
      },
      story: {
        auth: 'admin',
        path: {
          id: 'unknowContentToRead',
        },
      },
    },
  },
};
