import {StorySpec} from 'specs/specs.interface';

export const exempleFileToUpload: StorySpec = {
  filePath: __filename,
  route: '/exemple/:id/file',
  method: 'POST',
  operation: {
    summary: 'Update an exemple file.',
  },
  access: {
    resource: 'exemple',
    action: 'update',
    owner: true,
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Picture Upload Succeed',
      },
      story: {
        auth: 'user',
        body: {fileUrl: 'mock.image'},
        path: {
          id: 'seed.exempleToUpdate.result.id',
        },
      },
      tests: [
        {
          type: 'exists',
          data: {id: true},
        },
      ],
    },
    '400': {
      response: {
        status: 400,
        description: 'Required file is empty',
      },
    },
    '403': {
      response: {
        status: 403,
        description: 'Forbidden. You do not have the rights.',
      },
    },
  },
};
