import {StorySpec} from 'specs/specs.interface';

export const exempleToCount: StorySpec = {
  filePath: __filename,
  route: '/exemple/count',
  method: 'POST',
  operation: {
    summary: 'As a user, i want to count exemples found by name',
  },
  access: {
    resource: 'exemple',
    action: 'read',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Number of Exemples found',
      },
      story: {
        auth: 'user',
        body: {
          name: 'Exemple to find',
        },
      },
      tests: [
        {
          type: 'equal',
          data: 2,
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
  },
};
