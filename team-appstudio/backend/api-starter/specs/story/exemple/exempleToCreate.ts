import {StorySpec} from 'specs/specs.interface';

export const exempleToCreate: StorySpec = {
  filePath: __filename,
  route: '/exemple',
  method: 'POST',
  operation: {
    summary: 'As a user, i want to create an exemple',
  },
  access: {
    resource: 'exemple',
    action: 'create',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Exemple created',
      },
      story: {
        auth: 'user',
        body: {
          name: 'mock.name',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {name: 'params.name'},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
  },
};
