import {StorySpec} from 'specs/specs.interface';

export const exempleToDelete: StorySpec = {
  filePath: __filename,
  route: '/exemple/:id',
  method: 'DELETE',
  operation: {
    summary: 'As a user, i want to delete an exemple',
  },
  access: {
    resource: 'exemple',
    action: 'delete',
    owner: true,
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Exemple delete',
      },
      story: {
        auth: 'user',

        path: {
          id: 'seed.exempleToDelete.result.id',
        },
      },
      tests: [
        {
          type: 'equal',
          success: true,
        },
      ],
    },
    '403': {
      response: {
        status: 403,
        description: 'Incorrect credentials',
      },
      story: {
        auth: 'userAlreadyRegistered',
        path: {
          id: 'seed.exempleToFind1.result.id',
        },
      },
    },
    '404': {
      response: {
        status: 404,
        description: "Exemple doesn't exists",
      },
      story: {
        auth: 'user',
        path: {
          id: 'seed.exempleToDelete.result.id',
        },
      },
    },
  },
};
