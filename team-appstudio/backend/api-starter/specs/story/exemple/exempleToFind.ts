import {StorySpec} from 'specs/specs.interface';

export const exempleToFind: StorySpec = {
  filePath: __filename,
  route: '/exemple/find',
  method: 'POST',
  operation: {
    summary: 'As a user, i want to find by name',
  },
  access: {
    resource: 'exemple',
    action: 'read',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Exemples found',
      },
      story: {
        auth: 'user',
        body: {
          name: 'Exemple to find',
        },
      },
      tests: [
        {
          type: 'equal',

          data: {length: 2},
        },
      ],
    },
  },
};
