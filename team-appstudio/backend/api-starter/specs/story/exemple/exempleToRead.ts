import {StorySpec} from 'specs/specs.interface';

export const exempleToRead: StorySpec = {
  filePath: __filename,
  route: '/exemple/:id',
  method: 'GET',
  operation: {
    summary: 'As a user, i want to read an exemple',
  },
  access: {
    resource: 'exemple',
    action: 'read',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: "Exemple's data",
      },
      story: {
        auth: 'user',
        path: {
          id: 'seed.exempleToRead.result.id',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {name: 'seed.exempleToRead.params[1].name'},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
    '404': {
      response: {
        status: 404,
        description: "Exemple doesn't exists",
      },
    },
  },
};
