import {StorySpec} from 'specs/specs.interface';

export const exempleToUpdate: StorySpec = {
  filePath: __filename,
  route: '/exemple/:id',
  method: 'PATCH',
  operation: {
    summary: 'as a user, i want to update an exemple',
  },
  access: {
    resource: 'exemple',
    action: 'update',
    owner: true,
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Exemple Updated',
      },
      story: {
        auth: 'user',
        path: {
          id: 'seed.exempleToUpdate.result.id',
        },
        body: {
          name: 'Updated Exemple',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {name: 'params.name'},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
    '404': {
      response: {
        status: 404,
        description: "Exemple doesn't exists",
      },
      story: {
        auth: 'user',
        path: {
          id: 'unknowExempleToRead',
        },
      },
    },
  },
};
