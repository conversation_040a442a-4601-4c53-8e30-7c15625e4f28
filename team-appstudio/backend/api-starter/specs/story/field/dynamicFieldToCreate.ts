import {StorySpec} from 'specs/specs.interface';

export const dynamicFieldToCreate: StorySpec = {
  filePath: __filename,
  route: '/field/dynamic',
  method: 'POST',
  operation: {
    summary: 'As a user, i want to create a dynamic field value',
  },
  access: {
    resource: 'field',
    action: 'create',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Field created',
      },
      story: {
        auth: 'admin',
        body: {
          key: 'mock.name',
          name: 'mock.name',
          value: 'mock.name',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {name: 'params.name'},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
  },
};
