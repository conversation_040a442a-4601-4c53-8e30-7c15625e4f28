import {StorySpec} from 'specs/specs.interface';

export const dynamicFieldToDelete: StorySpec = {
  filePath: __filename,
  route: '/field/:id',
  method: 'DELETE',
  operation: {
    summary: 'As a user, i want to delete a dynamic field value',
  },
  access: {
    resource: 'field',
    action: 'delete',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Field deleted',
      },
      story: {
        auth: 'admin',
        path: {
          id: 'seed.dynamicFieldToDelete.result.id',
        },
      },
      tests: [
        {
          type: 'equal',
          success: true,
        },
      ],
    },
    '404': {
      response: {
        status: 404,
        description: "Field doesn't exists",
      },
      story: {
        auth: 'admin',
        path: {
          id: 'seed.dynamicFieldToDelete.result.id',
        },
      },
    },
  },
};
