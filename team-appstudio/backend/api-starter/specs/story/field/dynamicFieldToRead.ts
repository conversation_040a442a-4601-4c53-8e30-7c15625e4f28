import {StorySpec} from 'specs/specs.interface';

export const dynamicFieldToRead: StorySpec = {
  filePath: __filename,
  route: '/field/dynamic',
  method: 'GET',
  operation: {
    summary: 'As a user, i want to read a field',
  },
  access: {
    resource: 'field',
    action: 'read',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: "Field's data",
      },
      story: {
        auth: 'admin',
        query: {
          key: 'seed.dynamicFieldToRead.result.key',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {length: 1},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
  },
};
