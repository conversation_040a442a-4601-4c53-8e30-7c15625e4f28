import {StorySpec} from 'specs/specs.interface';

export const staticFieldToRead: StorySpec = {
  filePath: __filename,
  route: '/field/static',
  method: 'GET',
  operation: {
    summary: 'As a user, i want to read an field',
  },
  access: {
    resource: 'field',
    action: 'read',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: "Field's data",
      },
      story: {
        auth: 'admin',
        query: {
          key: 'SortOrder',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {length: 2},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
    '404': {
      response: {
        status: 404,
        description: "Field doesn't exists",
      },
    },
  },
};
