import {StorySpec} from 'specs/specs.interface';

export const highlightToDelete: StorySpec = {
  filePath: __filename,
  route: '/highlight/:id',
  method: 'DELETE',
  operation: {
    summary: 'As a user, i want to delete an highlight',
  },
  access: {
    resource: 'highlight',
    action: 'delete',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Highlight delete',
      },
      story: {
        auth: 'admin',
        path: {
          id: 'seed.highlightToDelete.result.id',
        },
      },
      tests: [
        {
          type: 'equal',
          success: true,
        },
      ],
    },
    '403': {
      response: {
        status: 403,
        description: 'Incorrect credentials',
      },
      story: {
        auth: 'userAlreadyRegistered',
        path: {
          id: 'seed.highlightToDelete.result.id',
        },
      },
    },
    '404': {
      response: {
        status: 404,
        description: "Highlight doesn't exists",
      },
      story: {
        auth: 'admin',
        path: {
          id: 'unknownHighlightId',
        },
      },
    },
  },
};
