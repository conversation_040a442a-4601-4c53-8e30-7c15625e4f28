import {StorySpec} from 'specs/specs.interface';

export const highlightToFind: StorySpec = {
  filePath: __filename,
  route: '/highlight',
  method: 'GET',
  operation: {
    summary: 'As a user, i want to get highlight sections',
  },
  access: {
    resource: 'highlight',
    action: 'read',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: "Highlight's Data",
      },
    },
  },
};
