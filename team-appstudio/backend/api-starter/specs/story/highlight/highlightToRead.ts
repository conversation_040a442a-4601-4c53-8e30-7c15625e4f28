import {StorySpec} from 'specs/specs.interface';

export const highlightToRead: StorySpec = {
  filePath: __filename,
  route: '/highlight/:id',
  method: 'GET',
  operation: {
    summary: 'As a user, i want to read an highlight',
  },
  access: {
    resource: 'highlight',
    action: 'read',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: "Highlight's data",
      },
      story: {
        path: {
          id: 'seed.highlightToRead.result.id',
        },
      },
      tests: [
        {
          type: 'contains',
          data: {},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
    '404': {
      response: {
        status: 404,
        description: "Highlight section doesn't exists",
      },
    },
  },
};
