import {StorySpec} from 'specs/specs.interface';

export const homeToDelete: StorySpec = {
  filePath: __filename,
  route: '/home/<USER>',
  method: 'DELETE',
  operation: {
    summary: 'As a user, i want to delete an home',
  },
  access: {
    resource: 'home',
    action: 'delete',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Home delete',
      },
      story: {
        auth: 'admin',
        path: {
          id: 'seed.homeToDelete.result.id',
        },
      },
      tests: [
        {
          type: 'equal',
          success: true,
        },
      ],
    },
    '403': {
      response: {
        status: 403,
        description: 'Incorrect credentials',
      },
      story: {
        auth: 'userAlreadyRegistered',
        path: {
          id: 'seed.homeToDelete.result.id',
        },
      },
    },
    '404': {
      response: {
        status: 404,
        description: "Home doesn't exists",
      },
      story: {
        auth: 'admin',
        path: {
          id: 'unknownHomeId',
        },
      },
    },
  },
};
