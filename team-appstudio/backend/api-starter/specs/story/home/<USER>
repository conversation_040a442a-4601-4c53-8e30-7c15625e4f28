import {StorySpec} from 'specs/specs.interface';

export const homeToCreate: StorySpec = {
  filePath: __filename,
  route: '/home',
  method: 'POST',
  operation: {
    summary: 'As a user, i want to create an home',
  },
  access: {
    resource: 'home',
    action: 'create',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Home created',
      },
      story: {
        auth: 'admin',
        body: {
          id: 'mock.id',
          value: {},
        },
      },
      tests: [
        {
          type: 'equal',
          data: {id: 'params.id'},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
  },
};
