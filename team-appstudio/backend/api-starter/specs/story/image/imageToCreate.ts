import {StorySpec} from 'specs/specs.interface';

export const imageToCreate: StorySpec = {
  filePath: __filename,
  route: '/workflow/image/create',
  method: 'POST',
  operation: {
    summary: 'As a user, I want to create a image',
  },
  access: {
    resource: 'image',
    action: 'create',
    owner: false,
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'image created',
      },
      story: {
        auth: 'user',
      },
      tests: [{type: 'equal', data: {length: 0}}],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
    '403': {
      response: {
        status: 403,
        description: 'Forbidden',
      },
    },
    '404': {
      response: {
        status: 404,
        description: "image doesn't exist",
      },
    },
  },
};
