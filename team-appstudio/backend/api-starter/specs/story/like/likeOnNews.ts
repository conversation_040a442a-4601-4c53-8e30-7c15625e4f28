import {StorySpec} from 'specs/specs.interface';

export const likeOnNews: StorySpec = {
  filePath: __filename,
  route: '/like/exists/:ownerType/:ownerId',
  method: 'GET',
  operation: {
    summary:
      'As a User I want to display a number of like/unlike for an ownerId for a news',
  },
  access: {
    resource: 'like',
    action: 'read',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'likes unlikes',
      },
      story: {
        auth: 'user',
        path: {
          ownerId: 'seed.likeOnNews.params[1].ownerId',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {likes: 1, total: 1},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
  },
};
