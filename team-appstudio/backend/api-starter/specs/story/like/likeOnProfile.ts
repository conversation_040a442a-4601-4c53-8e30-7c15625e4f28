import {StorySpec} from 'specs/specs.interface';

export const likeOnProfile: StorySpec = {
  filePath: __filename,
  route: '/like/profile',
  method: 'POST',
  operation: {
    summary:
      'As a User I want to display a number of like/unlike for an ownerId for a dancer profile',
  },
  access: {
    resource: 'like',
    action: 'read',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'likes unlikes',
      },
      story: {
        auth: 'user',
        body: {
          ownerId: 'seed.likeOnProfile.params[1].ownerId',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {length: 1},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
  },
};
