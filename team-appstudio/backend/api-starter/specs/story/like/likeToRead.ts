import {StorySpec} from 'specs/specs.interface';

export const likeToRead: StorySpec = {
  filePath: __filename,
  route: '/like/:ownerId/:ownerType',
  method: 'GET',
  operation: {
    summary: 'I want to read an like',
  },
  access: {
    resource: 'like',
    action: 'read',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Like data',
      },
      story: {
        auth: 'user',
        path: {
          id: 'seed.likeToRead.result.id',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {name: 'seed.likeToRead.params[0].name'},
        },
      ],
    },
    '404': {
      response: {
        status: 404,
        description: 'Like does not exists',
      },
    },
  },
};
