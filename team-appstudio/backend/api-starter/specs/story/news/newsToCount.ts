import {StorySpec} from 'specs/specs.interface';

export const newsToCount: StorySpec = {
  filePath: __filename,
  route: '/news/count',
  method: 'POST',
  operation: {
    summary: 'I want to count found by title',
  },
  access: {
    resource: 'news',
    action: 'read',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Number of News found',
      },
      story: {
        auth: 'user',
        body: {
          title: 'News to find',
        },
      },
      // tests: [
      //   {
      //     type: 'contains',
      //     data: {count: 1},
      //   },
      // ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
  },
};
