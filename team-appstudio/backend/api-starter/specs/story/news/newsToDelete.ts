import {StorySpec} from 'specs/specs.interface';

export const newsToDelete: StorySpec = {
  filePath: __filename,
  route: '/news/:id',
  method: 'DELETE',
  operation: {
    summary: 'As a admin, i want to delete a news',
  },
  access: {
    resource: 'news',
    action: 'delete',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'news delete',
      },
      story: {
        auth: 'admin',

        path: {
          id: 'seed.newsToDelete.result.id',
        },
      },
      tests: [
        {
          type: 'equal',
          success: true,
        },
      ],
    },
    '403': {
      response: {
        status: 403,
        description: 'Incorrect credentials',
      },
      story: {
        auth: 'userAlreadyRegistered',
        path: {
          id: 'seed.newsToFind.result.id',
        },
      },
    },
    '404': {
      response: {
        status: 404,
        description: 'News does not exists',
      },
      story: {
        auth: 'admin',
        path: {
          id: 'seed.newsToDelete.result.id',
        },
      },
    },
  },
};
