import {StorySpec} from 'specs/specs.interface';

export const newsToFind: StorySpec = {
  filePath: __filename,
  route: '/news/find',
  method: 'POST',
  operation: {
    summary: 'I want to find news by title',
  },
  access: {
    resource: 'news',
    action: 'read',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'News found',
      },
      story: {
        auth: 'user',
        body: {
          title: 'News to find',
        },
      },
      tests: [
        {
          type: 'equal',

          data: {length: 1},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
  },
};
