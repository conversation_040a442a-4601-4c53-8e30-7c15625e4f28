import {StorySpec} from 'specs/specs.interface';

export const newsToReadWithSubscription: StorySpec = {
  filePath: __filename,
  route: '/news/:id',
  method: 'GET',
  operation: {
    summary: 'I want to read a news with subscription',
  },
  access: {
    resource: 'news',
    action: 'read',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'news needs subscription',
      },
      story: {
        auth: 'user',
        path: {
          id: 'seed.newsToSubscriptionDemo.result.id',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {
            payWithSubscription:
              'seed.newsToSubscriptionDemo.result.payWithSubscription',
            priceWithSubscription:
              'seed.newsToSubscriptionDemo.result.priceWithSubscription',
          },
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
    '404': {
      response: {
        status: 404,
        description: "news doesn't exists",
      },
      story: {
        auth: 'user',
        path: {
          id: 'unknownewsToRead',
        },
      },
    },
  },
};
