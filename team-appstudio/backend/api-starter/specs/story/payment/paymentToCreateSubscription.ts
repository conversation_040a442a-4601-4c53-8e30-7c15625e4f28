import {StorySpec} from 'specs/specs.interface';

export const paymentToCreateSubscription: StorySpec = {
  filePath: __filename,
  route: '/payment/stripe/subscription',
  method: 'POST',
  operation: {
    summary: 'As a admin, i want to buy a subscription',
  },
  access: {
    resource: 'payment',
    action: 'create',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Payment created',
      },
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
  },
};
