import {StorySpec} from 'specs/specs.interface';

export const paymentToFind: StorySpec = {
  filePath: __filename,
  route: '/payment/find',
  method: 'POST',
  operation: {
    summary: 'I want to find payment by name',
  },
  access: {
    resource: 'payment',
    action: 'read',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Payment found',
      },
      story: {
        auth: 'user',
        body: {
          name: 'Payment to find',
        },
      },
      tests: [
        {
          type: 'equal',

          data: {length: 1},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
  },
};
