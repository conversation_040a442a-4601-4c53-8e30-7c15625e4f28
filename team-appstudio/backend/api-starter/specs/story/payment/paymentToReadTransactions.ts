import {StorySpec} from 'specs/specs.interface';

export const paymentToReadTransactions: StorySpec = {
  filePath: __filename,
  route: '/payment/transactions',
  method: 'GET',
  operation: {
    summary: 'I want to read the transactions',
  },
  access: {
    resource: 'payment',
    action: 'read',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Transactions data',
      },
    },
    '404': {
      response: {
        status: 404,
        description: 'No Transactions',
      },
    },
  },
};
