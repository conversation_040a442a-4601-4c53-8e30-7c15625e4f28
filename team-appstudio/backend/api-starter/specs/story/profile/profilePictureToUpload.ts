import {StorySpec} from 'specs/specs.interface';

export const profilePictureToUpload: StorySpec = {
  filePath: __filename,
  route: '/profile/picture',
  method: 'POST',
  operation: {
    summary:
      'Update a profile picture. Only image files are supported (mime type image/*).',
  },
  access: {
    resource: 'profile',
    action: 'update',
    owner: true,
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Picture Upload Succeed',
      },
      story: {
        auth: 'user',
        body: {fileUrl: 'mock.avatar'},
      },
      tests: [
        {
          type: 'equal',
          success: true,
        },
      ],
    },
    '400': {
      response: {
        status: 400,
        description:
          'Required picture is empty or the file type is not an image.',
      },
    },
    '403': {
      response: {
        status: 403,
        description: 'Forbidden. You do not have the rights.',
      },
    },
  },
};
