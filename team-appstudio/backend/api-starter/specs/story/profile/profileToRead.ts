import {StorySpec} from 'specs/specs.interface';

export const profileToRead: StorySpec = {
  filePath: __filename,
  route: '/profile',
  method: 'GET',
  operation: {
    summary: "As a user, i want to read an user's profile",
  },
  access: {
    resource: 'profile',
    action: 'read',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: "User Profile's data",
      },
      story: {
        auth: 'user',
      },
      tests: [
        {
          type: 'equal',
          data: {country: 'seed.profileToRead.params[1].country'},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
    '404': {
      response: {
        status: 404,
        description: "Exemple doesn't exists",
      },
    },
  },
};
