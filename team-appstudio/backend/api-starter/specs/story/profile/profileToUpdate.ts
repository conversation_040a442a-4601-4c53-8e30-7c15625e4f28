import {Gender} from '@prisma/client';
import {StorySpec} from 'specs/specs.interface';

export const profileToUpdate: StorySpec = {
  filePath: __filename,
  route: '/profile',
  method: 'PATCH',
  operation: {
    summary: 'as a user, i want to update an user profile',
  },
  access: {
    resource: 'profile',
    action: 'update',
    owner: true,
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Profile Updated',
      },
      story: {
        auth: 'user',
        body: {
          gender: Gender.private,
        },
      },
      tests: [
        {
          type: 'contains',
          data: {gender: 'params.gender'},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
    '404': {
      response: {
        status: 404,
        description: "Profile doesn't exists",
      },
    },
  },
};
