import {StorySpec} from 'specs/specs.interface';

export const userPictureToUpload: StorySpec = {
  filePath: __filename,
  route: '/user/:id/picture',
  method: 'POST',
  operation: {
    summary:
      'Update an user picture. Only image files are supported (mime type image/*).',
  },
  access: {
    resource: 'user',
    action: 'update',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Picture Upload Succeed',
      },
      story: {
        auth: 'admin',
        body: {fileUrl: 'mock.avatar'},
        path: {
          id: 'seed.user.result.id',
        },
      },
      tests: [
        {
          type: 'equal',
          success: true,
        },
      ],
    },
    '400': {
      response: {
        status: 400,
        description:
          'Required picture is empty or the file type is not an image.',
      },
    },
    '403': {
      response: {
        status: 403,
        description: 'Forbidden. You do not have the rights.',
      },
    },
  },
};
