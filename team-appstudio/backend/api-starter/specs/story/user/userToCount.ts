import {StorySpec} from 'specs/specs.interface';

export const userToCount: StorySpec = {
  filePath: __filename,
  route: '/user/count',
  method: 'POST',
  operation: {
    summary: 'As a user, i want to count found by name',
  },
  access: {
    resource: 'user',
    action: 'read',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Number of Exemples found',
      },
      story: {
        auth: 'user',
        body: {
          name: 'Exemple to find',
        },
      },
      tests: [
        {
          type: 'equal',
          data: 2,
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
  },
};
