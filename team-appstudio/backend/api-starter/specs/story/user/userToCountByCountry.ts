import {StorySpec} from 'specs/specs.interface';

export const userToCountByCountry: StorySpec = {
  filePath: __filename,
  route: '/user/count/ByCountry',
  method: 'GET',
  operation: {
    summary: 'As a user, i want to count users by countries',
  },
  access: {
    resource: 'user',
    action: 'read',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'count users by countries',
      },
      tests: [
        {
          type: 'equal',
          data: {length: 116},
        },
      ],
    },
  },
};
