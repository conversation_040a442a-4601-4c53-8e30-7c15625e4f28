import {StorySpec} from 'specs/specs.interface';

export const userToDelete: StorySpec = {
  filePath: __filename,
  route: '/user/:id',
  method: 'DELETE',
  operation: {
    summary: 'As a user, i want to delete an user',
  },
  access: {
    resource: 'user',
    action: 'delete',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Exemple delete',
      },
      story: {
        auth: 'admin',

        path: {
          id: 'seed.userToDelete.result.id',
        },
      },
      tests: [
        {
          type: 'equal',
          success: true,
        },
      ],
    },
    '403': {
      response: {
        status: 403,
        description: 'Incorrect credentials',
      },
      story: {
        auth: 'userAlreadyRegistered',
        path: {
          id: 'seed.userToFind1.result.id',
        },
      },
    },
    '404': {
      response: {
        status: 404,
        description: "Exemple doesn't exists",
      },
      story: {
        auth: 'user',
        path: {
          id: 'seed.userToDelete.result.id',
        },
      },
    },
  },
};
