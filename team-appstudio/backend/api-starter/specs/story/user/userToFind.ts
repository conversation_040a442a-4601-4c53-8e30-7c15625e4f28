import {StorySpec} from 'specs/specs.interface';

export const userToFind: StorySpec = {
  filePath: __filename,
  route: '/user/find',
  method: 'POST',
  operation: {
    summary: 'As a user, i want to find by name',
  },
  access: {
    resource: 'user',
    action: 'read',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Users found',
      },
      story: {
        auth: 'user',
        body: {
          name: 'User to find',
        },
      },
      tests: [
        {
          type: 'equal',

          data: {length: 2},
        },
      ],
    },
  },
};
