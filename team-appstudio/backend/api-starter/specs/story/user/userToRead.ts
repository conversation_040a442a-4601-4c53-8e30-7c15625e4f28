import {StorySpec} from 'specs/specs.interface';

export const userToRead: StorySpec = {
  filePath: __filename,
  route: '/user/:id',
  method: 'GET',
  operation: {
    summary: 'As a user, i want to read an user',
  },
  access: {
    resource: 'user',
    action: 'read',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: "Exemple's data",
      },
      story: {
        auth: 'user',
        path: {
          id: 'seed.userToRead.result.id',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {name: 'seed.userToRead.params[1].name'},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
    '404': {
      response: {
        status: 404,
        description: "Exemple doesn't exists",
      },
    },
  },
};
