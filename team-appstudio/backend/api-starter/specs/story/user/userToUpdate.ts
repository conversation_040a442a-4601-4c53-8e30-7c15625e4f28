import {StorySpec} from 'specs/specs.interface';

export const userToUpdate: StorySpec = {
  filePath: __filename,
  route: '/user/:id',
  method: 'PATCH',
  operation: {
    summary: 'As a user, I want to update my information',
  },
  access: {
    resource: 'user',
    action: 'update',
    owner: true,
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'User updated successfully',
      },
      story: {
        auth: 'user',
        path: {
          id: 'seed.userToUpdate.result.id',
        },
        body: {
          model: 'seed.userToUpdate.params[1].model',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {model: 'seed.userToUpdate.params[1].model'},
        },
      ],
    },
    '403': {
      response: {
        status: 403,
        description: 'Incorrect credentials',
      },
      story: {
        auth: 'userAlreadyRegistered',
        path: {
          id: 'seed.userToUpdate.result.id',
        },
        body: {
          model: 'seed.userToUpdate.params[1].model',
        },
      },
    },
    '404': {
      response: {
        status: 404,
        description: "User doesn't exists",
      },
      story: {
        auth: 'user',
        path: {
          id: 'unkownUserId',
        },
      },
    },
  },
};
