import {StorySpec} from 'specs/specs.interface';

export const userTotal: StorySpec = {
  filePath: __filename,
  route: '/user/total',
  method: 'GET',
  operation: {
    summary: 'Return the number of total users',
  },
  access: {
    resource: 'home',
    action: 'read',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'As user i want to return number of users',
      },
      story: {
        auth: 'admin',
      },
    },
    '400': {
      response: {
        status: 400,
        description: 'displayedUserCount data id is empty.',
      },
      story: {},
    },
    '404': {
      response: {
        status: 404,
        description: 'displayedUserCount is not found.',
      },
      story: {},
    },
  },
};
