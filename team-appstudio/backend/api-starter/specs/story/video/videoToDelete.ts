import {StorySpec} from 'specs/specs.interface';

export const videoToDelete: StorySpec = {
  filePath: __filename,
  route: '/workflow/video/delete',
  method: 'POST',
  operation: {
    summary: 'As a user, I want to delete a video',
  },
  access: {
    resource: 'video',
    action: 'delete',
    owner: true,
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'video deleted',
      },
      story: {
        auth: 'user',
      },
      tests: [{type: 'equal', data: {length: 0}}],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
    '403': {
      response: {
        status: 403,
        description: 'Forbidden',
      },
    },
    '404': {
      response: {
        status: 404,
        description: "video doesn't exist",
      },
    },
  },
};
