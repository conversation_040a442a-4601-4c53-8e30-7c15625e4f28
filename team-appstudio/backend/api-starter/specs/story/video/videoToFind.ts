import {StorySpec} from 'specs/specs.interface';

export const videoToFind: StorySpec = {
  filePath: __filename,
  route: '/workflow/video/find',
  method: 'POST',
  operation: {
    summary: 'As a user, I want to find a video',
  },
  access: {
    resource: 'video',
    action: 'read',
    owner: true,
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'video found',
      },
      story: {
        auth: 'user',
      },
      tests: [{type: 'equal', data: {length: 0}}],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
    '403': {
      response: {
        status: 403,
        description: 'Forbidden',
      },
    },
    '404': {
      response: {
        status: 404,
        description: "video doesn't exist",
      },
    },
  },
};
