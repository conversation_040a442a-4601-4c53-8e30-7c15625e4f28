import {StorySpec} from 'specs/specs.interface';

export const videoToRead: StorySpec = {
  filePath: __filename,
  route: '/workflow/video/read',
  method: 'POST',
  operation: {
    summary: 'As a user, I want to read a video',
  },
  access: {
    resource: 'video',
    action: 'read',
    owner: true,
  },
  codes: {
    '200': {
      response: {
        status: 201,
        description: 'video read',
      },
      story: {
        auth: 'user',
      },
      tests: [{type: 'equal', data: {length: 0}}],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
    '403': {
      response: {
        status: 403,
        description: 'Forbidden',
      },
    },
    '404': {
      response: {
        status: 404,
        description: "video doesn't exist",
      },
    },
  },
};
