import {StorySpec} from 'specs/specs.interface';

export const workflowToCount: StorySpec = {
  filePath: __filename,
  route: '/workflow/count',
  method: 'POST',
  operation: {
    summary: 'As a user, i want to count workflows found by name',
  },
  access: {
    resource: 'workflow',
    action: 'read',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Number of Workflows found',
      },
      story: {
        auth: 'user',
        body: {
          name: 'Workflow to find',
        },
      },
      tests: [
        {
          type: 'equal',
          data: 2,
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
  },
};
