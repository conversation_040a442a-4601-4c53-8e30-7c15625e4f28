import {StorySpec} from 'specs/specs.interface';

export const workflowToDelete: StorySpec = {
  filePath: __filename,
  route: '/workflow/:id',
  method: 'DELETE',
  operation: {
    summary: 'As a user, i want to delete an workflow',
  },
  access: {
    resource: 'workflow',
    action: 'delete',
    owner: true,
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: 'Workflow delete',
      },
      story: {
        auth: 'user',

        path: {
          id: 'seed.workflowToDelete.result.id',
        },
      },
      tests: [
        {
          type: 'equal',
          success: true,
        },
      ],
    },
    '403': {
      response: {
        status: 403,
        description: 'Incorrect credentials',
      },
      story: {
        auth: 'userAlreadyRegistered',
        path: {
          id: 'seed.workflowToFind1.result.id',
        },
      },
    },
    '404': {
      response: {
        status: 404,
        description: "Workflow doesn't exists",
      },
      story: {
        auth: 'user',
        path: {
          id: 'seed.workflowToDelete.result.id',
        },
      },
    },
  },
};
