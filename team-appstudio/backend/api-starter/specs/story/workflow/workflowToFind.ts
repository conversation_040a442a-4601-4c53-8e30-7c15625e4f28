import {StorySpec} from 'specs/specs.interface';

export const workflowToFind: StorySpec = {
  filePath: __filename,
  route: '/workflow/find',
  method: 'POST',
  operation: {
    summary: 'As a user, i want to find by name',
  },
  access: {
    resource: 'workflow',
    action: 'read',
  },
  codes: {
    '201': {
      response: {
        status: 201,
        description: 'Workflows found',
      },
      story: {
        auth: 'user',
        body: {
          name: 'Workflow to find',
        },
      },
      tests: [
        {
          type: 'equal',

          data: {length: 2},
        },
      ],
    },
  },
};
