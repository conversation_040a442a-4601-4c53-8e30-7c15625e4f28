import {StorySpec} from 'specs/specs.interface';

export const workflowToRead: StorySpec = {
  filePath: __filename,
  route: '/workflow/:id',
  method: 'GET',
  operation: {
    summary: 'As a user, i want to read an workflow',
  },
  access: {
    resource: 'workflow',
    action: 'read',
  },
  codes: {
    '200': {
      response: {
        status: 200,
        description: "Workflow's data",
      },
      story: {
        auth: 'user',
        path: {
          id: 'seed.workflowToRead.result.id',
        },
      },
      tests: [
        {
          type: 'equal',
          data: {name: 'seed.workflowToRead.params[1].name'},
        },
      ],
    },
    '401': {
      response: {
        status: 401,
        description: 'Incorrect credentials',
      },
    },
    '404': {
      response: {
        status: 404,
        description: "Workflow doesn't exists",
      },
    },
  },
};
