export const NOTIFICATION_CONFIG = {
  "commentCreatePostParams": {
    "requestId": "createComment",
    "requestPath": "/comment/create",
    "requestType": "POST",
    "useWorkflow": false,
    "dynamicRedirectionParams": [
      "id"
    ],
    "link": "/posts/:id",
    "target": "me",
    "title": "Comment Submitted",
    "message": "Your comment has been successfully submitted."
  },
  "postUpdateIdPatchParams": {
    "requestId": "publishPost",
    "requestPath": "/post/update/:id",
    "requestType": "PATCH",
    "useWorkflow": false,
    "dynamicRedirectionParams": [
      "id"
    ],
    "link": "/posts/:id",
    "target": "me",
    "title": "Post Published",
    "message": "Your blog post has been successfully published!"
  }
}