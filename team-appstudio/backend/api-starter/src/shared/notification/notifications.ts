const Notification = {
  userLike: {
    link: '/profile',
    message: undefined,
    title: {
      fr: 'V<PERSON> avez de nouveaux likes sur votre profile',
      en: 'you have received a news like on your profile',
    },
    type: 'info',
  },
  commentAnswer: commentId => ({
    link: `/comment/${commentId}`,
    message: undefined,
    title: {
      fr: 'Vous avez recue une réponse a votre commentaire',
      en: 'you have received an answer on your comment',
    },
    type: 'info',
  }),

    "commentCreatePost": (id) => ({
        link: `/posts/${id}`,
        title: {
            fr: "Commentaire Soumis",
            en: "Comment Submitted"
        },
        message: {
            fr: "Votre commentaire a été soumis avec succès.",
            en: "Your comment has been successfully submitted."
        },
        type: "email"
    }),
    "postUpdateIdPatch": (id) => ({
        link: `/posts/${id}`,
        title: {
            fr: "Publication Publiée",
            en: "Post Published"
        },
        message: {
            fr: "Votre article de blog a été publié avec succès !",
            en: "Your blog post has been successfully published!"
        },
        type: "email"
    })

};

export default Notification;
