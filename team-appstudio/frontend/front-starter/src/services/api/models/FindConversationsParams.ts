/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type FindConversationsParams = {
  status?: 'active' | 'archived' | 'deleted';
  /**
   * Order by field
   */
  sortField?: 'id' | 'userId' | 'title' | 'prompt' | 'status' | 'agentId' | 'createdAt' | 'updatedAt';
  /**
   * Order sort
   */
  sortOrder?: 'asc' | 'desc';
  /**
   * Number or result to return
   */
  take?: number;
  /**
   * Number or result to skip
   */
  skip?: number;
};
