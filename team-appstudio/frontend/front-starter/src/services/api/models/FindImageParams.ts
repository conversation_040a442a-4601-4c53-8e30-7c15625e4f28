/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type FindImageParams = {
  /**
   * Order by field
   */
  sortField?:
    | 'id'
    | 'userId'
    | 'publicId'
    | 'url'
    | 'thumbnailUrl'
    | 'json'
    | 'type'
    | 'workflowId'
    | 'topic'
    | 'createdAt'
    | 'updatedAt';
  /**
   * Order sort
   */
  sortOrder?: 'asc' | 'desc';
  /**
   * Number of results to return
   */
  take?: number;
  /**
   * Number of results to skip
   */
  skip?: number;
};
