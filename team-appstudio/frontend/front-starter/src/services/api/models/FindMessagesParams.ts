/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type FindMessagesParams = {
  userId?: string;
  conversationId?: string;
  /**
   * Order by field
   */
  sortField?:
    | 'id'
    | 'userId'
    | 'role'
    | 'messageType'
    | 'content'
    | 'metadata'
    | 'conversationId'
    | 'createdAt'
    | 'updatedAt';
  /**
   * Order sort
   */
  sortOrder?: 'asc' | 'desc';
  /**
   * Number or result to return
   */
  take?: number;
  /**
   * Number or result to skip
   */
  skip?: number;
};
