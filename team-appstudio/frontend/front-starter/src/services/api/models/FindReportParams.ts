/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type FindReportParams = {
  /**
   * ObjectType :news,comment,profile,audio
   */
  objectType: string;
  /**
   * Object Id
   */
  objectId: string;
  /**
   * true/false comment reported
   */
  reported?: boolean;
  /**
   * Number or result to return
   */
  take?: number;
  /**
   * Number or result to skip
   */
  skip?: number;
  /**
   * Order by field
   */
  sortField?: 'id' | 'userId' | 'text' | 'objectType' | 'objectId' | 'commentId' | 'createdAt' | 'updatedAt';
  /**
   * Order sort
   */
  sortOrder?: 'asc' | 'desc';
};
