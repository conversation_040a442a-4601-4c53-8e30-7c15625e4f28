/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */

export type FindUserParams = {
  name?: string;
  /**
   * Order by field
   */
  sortField?:
    | 'id'
    | 'name'
    | 'blocked'
    | 'inactive'
    | 'role'
    | 'language'
    | 'imageUrl'
    | 'image'
    | 'textModel'
    | 'textProvider'
    | 'textTemp'
    | 'audioModel'
    | 'audioProvider'
    | 'audioTemp'
    | 'imageModel'
    | 'imageProvider'
    | 'imageTemp'
    | 'videoModel'
    | 'videoProvider'
    | 'videoTemp'
    | 'setupCompleted'
    | 'setupCurrentStep'
    | 'setupProgress'
    | 'createdAt'
    | 'updatedAt';
  /**
   * Order sort
   */
  sortOrder?: 'asc' | 'desc';
  /**
   * Number or result to return
   */
  take?: number;
  /**
   * Number or result to skip
   */
  skip?: number;
};
