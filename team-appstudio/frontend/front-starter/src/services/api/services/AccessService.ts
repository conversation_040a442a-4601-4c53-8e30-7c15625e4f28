/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CreateAccessParams } from '../models/CreateAccessParams';
import type { FindAccessParams } from '../models/FindAccessParams';
import type { UpdateAccessParams } from '../models/UpdateAccessParams';
import type { CancelablePromise } from '../core/CancelablePromise';
import { request as __request, upload } from '../core/request';
import { useRequest, UseRequestOption, UseRequestProperties } from '@app-studio/react-request';

/**
 * As an admin, I want to create a access
 * @param requestBody
 * @returns any access created
 * @throws ApiError
 */
export const accessControllerCreate = (requestBody: CreateAccessParams): CancelablePromise<any> => {
  return __request({
    method: 'POST',
    path: `/access`,
    body: requestBody,
    mediaType: 'application/json',
    errors: {
      401: `Incorrect credentials`,
    },
  });
};

/**
 * As a user, I want to find a specific access by criteria
 * @param requestBody
 * @returns any access found
 * @throws ApiError
 */
export const accessControllerFind = (requestBody: FindAccessParams): CancelablePromise<any> => {
  return __request({
    method: 'POST',
    path: `/access/find`,
    body: requestBody,
    mediaType: 'application/json',
  });
};

/**
 * As a user, I want to read a specific access
 * @param id
 * @returns any access details fetched
 * @throws ApiError
 */
export const accessControllerRead = (id: string): CancelablePromise<any> => {
  return __request({
    method: 'GET',
    path: `/access/${id}`,
    errors: {
      404: `access not found`,
    },
  });
};

/**
 * As an admin, I want to delete a access
 * @param id
 * @returns any access deleted
 * @throws ApiError
 */
export const accessControllerDelete = (id: string): CancelablePromise<any> => {
  return __request({
    method: 'DELETE',
    path: `/access/${id}`,
    errors: {
      403: `Incorrect credentials`,
      404: `access not found`,
    },
  });
};

/**
 * As an admin, I want to update a access
 * @param id
 * @param requestBody
 * @returns any access updated
 * @throws ApiError
 */
export const accessControllerUpdate = (id: string, requestBody: UpdateAccessParams): CancelablePromise<any> => {
  return __request({
    method: 'PATCH',
    path: `/access/${id}`,
    body: requestBody,
    mediaType: 'application/json',
    errors: {
      401: `Incorrect credentials`,
      404: `access not found`,
    },
  });
};

/**
 * As an admin, I want to delete all access records
 * @returns any All access records deleted
 * @throws ApiError
 */
export const accessControllerDeleteAll = (): CancelablePromise<any> => {
  return __request({
    method: 'DELETE',
    path: `/access/delete`,
    errors: {
      403: `Incorrect credentials`,
    },
  });
};

export const useAccessControllerCreateService = ({ method = 'POST', ...options }: UseRequestOption = {}): {
  run: (requestBody: CreateAccessParams) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(accessControllerCreate, { method, ...options });
};

export const useAccessControllerFindService = ({ method = 'POST', ...options }: UseRequestOption = {}): {
  run: (requestBody: FindAccessParams) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(accessControllerFind, { method, ...options });
};

export const useAccessControllerReadService = ({ method = 'GET', ...options }: UseRequestOption = {}): {
  run: (id: string) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(accessControllerRead, { method, ...options });
};

export const useAccessControllerDeleteService = ({ method = 'DELETE', ...options }: UseRequestOption = {}): {
  run: (id: string) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(accessControllerDelete, { method, ...options });
};

export const useAccessControllerUpdateService = ({ method = 'PATCH', ...options }: UseRequestOption = {}): {
  run: (id: string, requestBody: UpdateAccessParams) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(accessControllerUpdate, { method, ...options });
};

export const useAccessControllerDeleteAllService = ({ method = 'DELETE', ...options }: UseRequestOption = {}): {
  run: () => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(accessControllerDeleteAll, { method, ...options });
};
