/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CreateActivityLogParams } from '../models/CreateActivityLogParams';
import type { CancelablePromise } from '../core/CancelablePromise';
import { request as __request, upload } from '../core/request';
import { useRequest, UseRequestOption, UseRequestProperties } from '@app-studio/react-request';

/**
 * Create a new activity log entry
 * @param requestBody
 * @returns any Activity log created successfully
 * @throws ApiError
 */
export const activityLogControllerCreate = (requestBody: CreateActivityLogParams): CancelablePromise<any> => {
  return __request({
    method: 'POST',
    path: `/activity-log`,
    body: requestBody,
    mediaType: 'application/json',
    errors: {
      401: `Unauthorized`,
    },
  });
};

/**
 * List activity logs
 * @param userId User ID to filter logs by
 * @param actionType Action type to filter logs by
 * @param entityType Entity type to filter logs by
 * @param entityId Entity ID to filter logs by
 * @param startDate Start date for filtering logs
 * @param endDate End date for filtering logs
 * @param skip Number of records to skip
 * @param take Number of records to take
 * @returns any Activity logs retrieved successfully
 * @throws ApiError
 */
export const activityLogControllerList = (
  userId?: string,
  actionType?:
    | 'CREATE'
    | 'READ'
    | 'UPDATE'
    | 'DELETE'
    | 'LOGIN'
    | 'LOGOUT'
    | 'UPLOAD'
    | 'DOWNLOAD'
    | 'SHARE'
    | 'COMMENT'
    | 'LIKE'
    | 'PAYMENT'
    | 'SUBSCRIPTION'
    | 'SETTINGS_CHANGE'
    | 'OTHER',
  entityType?: string,
  entityId?: string,
  startDate?: string,
  endDate?: string,
  skip?: number,
  take: number = 10
): CancelablePromise<any> => {
  return __request({
    method: 'GET',
    path: `/activity-log`,
    query: {
      userId: userId,
      actionType: actionType,
      entityType: entityType,
      entityId: entityId,
      startDate: startDate,
      endDate: endDate,
      skip: skip,
      take: take,
    },
  });
};

/**
 * Get an activity log by ID
 * @param id Activity log ID
 * @returns any Activity log retrieved successfully
 * @throws ApiError
 */
export const activityLogControllerGet = (id: string): CancelablePromise<any> => {
  return __request({
    method: 'GET',
    path: `/activity-log/${id}`,
    errors: {
      404: `Activity log not found`,
    },
  });
};

/**
 * Get activity logs for a specific user
 * Retrieve all activity logs for a specific user
 * @param userId
 * @returns any Activity logs retrieved successfully
 * @throws ApiError
 */
export const activityLogControllerGetUserActivityLogs = (userId: string): CancelablePromise<any> => {
  return __request({
    method: 'GET',
    path: `/activity-log/user/${userId}`,
  });
};

/**
 * Get activity logs for dashboard
 * Retrieve recent activity logs for the dashboard view
 * @param limit Number of records to take
 * @param actionTypes Filter by action types
 * @param entityTypes Filter by entity types
 * @param groupByDate Group activities by date
 * @returns any Dashboard activity logs retrieved successfully
 * @throws ApiError
 */
export const activityLogControllerGetDashboardActivity = (
  limit: number = 10,
  actionTypes?: Array<
    | 'CREATE'
    | 'READ'
    | 'UPDATE'
    | 'DELETE'
    | 'LOGIN'
    | 'LOGOUT'
    | 'UPLOAD'
    | 'DOWNLOAD'
    | 'SHARE'
    | 'COMMENT'
    | 'LIKE'
    | 'PAYMENT'
    | 'SUBSCRIPTION'
    | 'SETTINGS_CHANGE'
    | 'OTHER'
  >,
  entityTypes?: Array<string>,
  groupByDate: boolean = false
): CancelablePromise<any> => {
  return __request({
    method: 'GET',
    path: `/activity-log/dashboard`,
    query: {
      limit: limit,
      actionTypes: actionTypes,
      entityTypes: entityTypes,
      groupByDate: groupByDate,
    },
  });
};

export const useActivityLogControllerCreateService = ({ method = 'POST', ...options }: UseRequestOption = {}): {
  run: (requestBody: CreateActivityLogParams) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(activityLogControllerCreate, { method, ...options });
};

export const useActivityLogControllerListService = ({ method = 'GET', ...options }: UseRequestOption = {}): {
  run: (
    userId?: string,
    actionType?:
      | 'CREATE'
      | 'READ'
      | 'UPDATE'
      | 'DELETE'
      | 'LOGIN'
      | 'LOGOUT'
      | 'UPLOAD'
      | 'DOWNLOAD'
      | 'SHARE'
      | 'COMMENT'
      | 'LIKE'
      | 'PAYMENT'
      | 'SUBSCRIPTION'
      | 'SETTINGS_CHANGE'
      | 'OTHER',
    entityType?: string,
    entityId?: string,
    startDate?: string,
    endDate?: string,
    skip?: number,
    take?: number
  ) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(activityLogControllerList, { method, ...options });
};

export const useActivityLogControllerGetService = ({ method = 'GET', ...options }: UseRequestOption = {}): {
  run: (id: string) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(activityLogControllerGet, { method, ...options });
};

export const useActivityLogControllerGetUserActivityLogsService = ({
  method = 'GET',
  ...options
}: UseRequestOption = {}): {
  run: (userId: string) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(activityLogControllerGetUserActivityLogs, {
    method,
    ...options,
  });
};

export const useActivityLogControllerGetDashboardActivityService = ({
  method = 'GET',
  ...options
}: UseRequestOption = {}): {
  run: (
    limit: number,
    actionTypes?: Array<
      | 'CREATE'
      | 'READ'
      | 'UPDATE'
      | 'DELETE'
      | 'LOGIN'
      | 'LOGOUT'
      | 'UPLOAD'
      | 'DOWNLOAD'
      | 'SHARE'
      | 'COMMENT'
      | 'LIKE'
      | 'PAYMENT'
      | 'SUBSCRIPTION'
      | 'SETTINGS_CHANGE'
      | 'OTHER'
    >,
    entityTypes?: Array<string>,
    groupByDate?: boolean
  ) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(activityLogControllerGetDashboardActivity, {
    method,
    ...options,
  });
};
