/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CreateAgentParams } from '../models/CreateAgentParams';
import type { FindAgentParams } from '../models/FindAgentParams';
import type { CancelablePromise } from '../core/CancelablePromise';
import { request as __request, upload } from '../core/request';
import { useRequest, UseRequestOption, UseRequestProperties } from '@app-studio/react-request';

/**
 * Create a new agent
 * @param requestBody
 * @returns any Agent created successfully
 * @throws ApiError
 */
export const agentControllerCreate = (requestBody: CreateAgentParams): CancelablePromise<any> => {
  return __request({
    method: 'POST',
    path: `/chat/agent/create`,
    body: requestBody,
    mediaType: 'application/json',
    errors: {
      401: `Unauthorized`,
    },
  });
};

/**
 * Get agent by ID
 * @param id
 * @returns any Agent created successfully
 * @throws ApiError
 */
export const agentControllerGet = (id: string): CancelablePromise<any> => {
  return __request({
    method: 'GET',
    path: `/chat/agent/${id}`,
    errors: {
      401: `Unauthorized`,
    },
  });
};

/**
 * Update an existing agent with optional AI-regeneration of details
 * @param id
 * @returns any Agent updated successfully
 * @throws ApiError
 */
export const agentControllerUpdate = (
  id: string
): CancelablePromise<{
  /**
   * Agent ID
   */
  id?: string;
  /**
   * Agent name (can be AI-regenerated if only systemPrompt is provided)
   */
  name?: string;
  /**
   * Agent description (can be AI-regenerated if only systemPrompt is provided)
   */
  description?: string;
  /**
   * System prompt used to initialize the agent
   */
  systemPrompt?: string;
  /**
   * Conversation starters (can be AI-regenerated if only systemPrompt is provided)
   */
  starters?: Array<string>;
}> => {
  return __request({
    method: 'PATCH',
    path: `/chat/agent/${id}`,
    errors: {
      401: `Unauthorized`,
    },
  });
};

/**
 * Delete an agent
 * @param id
 * @returns any Agent deleted successfully
 * @throws ApiError
 */
export const agentControllerDelete = (id: string): CancelablePromise<any> => {
  return __request({
    method: 'DELETE',
    path: `/chat/agent/${id}`,
    errors: {
      401: `Unauthorized`,
    },
  });
};

/**
 * Find agents by criteria
 * @param requestBody
 * @returns any List of agents matching criteria
 * @throws ApiError
 */
export const agentControllerFind = (requestBody: FindAgentParams): CancelablePromise<any> => {
  return __request({
    method: 'POST',
    path: `/chat/agent/find`,
    body: requestBody,
    mediaType: 'application/json',
  });
};

export const useAgentControllerCreateService = ({ method = 'POST', ...options }: UseRequestOption = {}): {
  run: (requestBody: CreateAgentParams) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(agentControllerCreate, { method, ...options });
};

export const useAgentControllerGetService = ({ method = 'GET', ...options }: UseRequestOption = {}): {
  run: (id: string) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(agentControllerGet, { method, ...options });
};

export const useAgentControllerUpdateService = ({ method = 'PATCH', ...options }: UseRequestOption = {}): {
  run: (id: string) => void;
  data: {
    /**
     * Agent ID
     */
    id?: string;
    /**
     * Agent name (can be AI-regenerated if only systemPrompt is provided)
     */
    name?: string;
    /**
     * Agent description (can be AI-regenerated if only systemPrompt is provided)
     */
    description?: string;
    /**
     * System prompt used to initialize the agent
     */
    systemPrompt?: string;
    /**
     * Conversation starters (can be AI-regenerated if only systemPrompt is provided)
     */
    starters?: Array<string>;
  };
} & UseRequestProperties => {
  return useRequest(agentControllerUpdate, { method, ...options });
};

export const useAgentControllerDeleteService = ({ method = 'DELETE', ...options }: UseRequestOption = {}): {
  run: (id: string) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(agentControllerDelete, { method, ...options });
};

export const useAgentControllerFindService = ({ method = 'POST', ...options }: UseRequestOption = {}): {
  run: (requestBody: FindAgentParams) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(agentControllerFind, { method, ...options });
};
