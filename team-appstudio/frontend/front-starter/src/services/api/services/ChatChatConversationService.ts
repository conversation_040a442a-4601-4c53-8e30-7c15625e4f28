/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CreateConversationParams } from '../models/CreateConversationParams';
import type { FindConversationsParams } from '../models/FindConversationsParams';
import type { UpdateConversationParams } from '../models/UpdateConversationParams';
import type { CancelablePromise } from '../core/CancelablePromise';
import { request as __request, upload } from '../core/request';
import { useRequest, UseRequestOption, UseRequestProperties } from '@app-studio/react-request';

/**
 * Create a new conversation with system prompt
 * @param requestBody
 * @returns any Conversation created successfully
 * @throws ApiError
 */
export const chatControllerCreate = (
  requestBody: CreateConversationParams
): CancelablePromise<{
  /**
   * Conversation ID
   */
  id?: string;
  /**
   * Conversation title
   */
  title?: string;
  /**
   * System prompt used to initialize the conversation
   */
  systemPrompt?: string;
}> => {
  return __request({
    method: 'POST',
    path: `/chat/chatConversation/create`,
    body: requestBody,
    mediaType: 'application/json',
    errors: {
      401: `Unauthorized`,
    },
  });
};

/**
 * Find chat conversations
 * @param requestBody
 * @returns any List of conversations retrieved successfully
 * @throws ApiError
 */
export const chatControllerFind = (requestBody: FindConversationsParams): CancelablePromise<any> => {
  return __request({
    method: 'POST',
    path: `/chat/chatConversation/find`,
    body: requestBody,
    mediaType: 'application/json',
    errors: {
      401: `Incorrect credentials`,
    },
  });
};

/**
 * Get chat conversation
 * @param id
 * @returns any Conversation retrieved successfully
 * @throws ApiError
 */
export const chatControllerGet = (id: string): CancelablePromise<any> => {
  return __request({
    method: 'GET',
    path: `/chat/chatConversation/${id}`,
    errors: {
      401: `Incorrect credentials`,
    },
  });
};

/**
 * Update chat conversation status
 * @param id
 * @param requestBody
 * @returns any Conversation status updated successfully
 * @throws ApiError
 */
export const chatControllerUpdate = (id: string, requestBody: UpdateConversationParams): CancelablePromise<any> => {
  return __request({
    method: 'PATCH',
    path: `/chat/chatConversation/update/${id}`,
    body: requestBody,
    mediaType: 'application/json',
    errors: {
      401: `Incorrect credentials`,
    },
  });
};

/**
 * Delete chat conversation
 * @param id
 * @returns any Chat deleted successfully
 * @throws ApiError
 */
export const chatControllerDelete = (id: string): CancelablePromise<any> => {
  return __request({
    method: 'DELETE',
    path: `/chat/chatConversation/delete/${id}`,
    errors: {
      404: `Chat not found`,
    },
  });
};

/**
 * Clear all messages in a chat conversation
 * @param id
 * @returns any Chat messages cleared successfully
 * @throws ApiError
 */
export const chatControllerClear = (id: string): CancelablePromise<any> => {
  return __request({
    method: 'DELETE',
    path: `/chat/chatConversation/clear/${id}`,
    errors: {
      404: `Chat not found`,
    },
  });
};

export const useChatControllerCreateService = ({ method = 'POST', ...options }: UseRequestOption = {}): {
  run: (requestBody: CreateConversationParams) => void;
  data: {
    /**
     * Conversation ID
     */
    id?: string;
    /**
     * Conversation title
     */
    title?: string;
    /**
     * System prompt used to initialize the conversation
     */
    systemPrompt?: string;
  };
} & UseRequestProperties => {
  return useRequest(chatControllerCreate, { method, ...options });
};

export const useChatControllerFindService = ({ method = 'POST', ...options }: UseRequestOption = {}): {
  run: (requestBody: FindConversationsParams) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(chatControllerFind, { method, ...options });
};

export const useChatControllerGetService = ({ method = 'GET', ...options }: UseRequestOption = {}): {
  run: (id: string) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(chatControllerGet, { method, ...options });
};

export const useChatControllerUpdateService = ({ method = 'PATCH', ...options }: UseRequestOption = {}): {
  run: (id: string, requestBody: UpdateConversationParams) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(chatControllerUpdate, { method, ...options });
};

export const useChatControllerDeleteService = ({ method = 'DELETE', ...options }: UseRequestOption = {}): {
  run: (id: string) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(chatControllerDelete, { method, ...options });
};

export const useChatControllerClearService = ({ method = 'DELETE', ...options }: UseRequestOption = {}): {
  run: (id: string) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(chatControllerClear, { method, ...options });
};
