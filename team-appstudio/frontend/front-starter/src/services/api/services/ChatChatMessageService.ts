/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CreateChatMessageParams } from '../models/CreateChatMessageParams';
import type { FindMessagesParams } from '../models/FindMessagesParams';
import type { SendMessageParams } from '../models/SendMessageParams';
import type { CancelablePromise } from '../core/CancelablePromise';
import { request as __request, upload } from '../core/request';
import { useRequest, UseRequestOption, UseRequestProperties } from '@app-studio/react-request';

/**
 * Find chat messages
 * @param requestBody
 * @returns any List of messages retrieved successfully
 * @throws ApiError
 */
export const chatMessageControllerGetConversations = (requestBody: FindMessagesParams): CancelablePromise<any> => {
  return __request({
    method: 'POST',
    path: `/chat/chatMessage/find`,
    body: requestBody,
    mediaType: 'application/json',
    errors: {
      401: `Incorrect credentials`,
    },
  });
};

/**
 * Create a new conversation with initial message
 * @param requestBody
 * @returns any Conversation created successfully with initial messages
 * @throws ApiError
 */
export const chatMessageControllerCreate = (
  requestBody: CreateChatMessageParams
): CancelablePromise<{
  /**
   * Conversation ID
   */
  id?: string;
  /**
   * Conversation title
   */
  title?: string;
  /**
   * System prompt used to initialize the conversation
   */
  prompt?: string;
  /**
   * Messages in the conversation
   */
  messages?: Array<{
    /**
     * Message ID
     */
    id?: string;
    /**
     * Message role (user, assistant, or system)
     */
    role?: string;
    /**
     * Message content
     */
    content?: string;
    /**
     * Type of message (text, audio, etc.)
     */
    messageType?: string;
  }>;
}> => {
  return __request({
    method: 'POST',
    path: `/chat/chatMessage/create`,
    body: requestBody,
    mediaType: 'application/json',
    errors: {
      401: `Unauthorized`,
    },
  });
};

/**
 * Create a new message in real-time for live conversations
 * @param requestBody
 * @returns any Message created successfully
 * @throws ApiError
 */
export const chatMessageControllerCreateLiveMessage = (
  requestBody: CreateChatMessageParams
): CancelablePromise<{
  /**
   * Message ID
   */
  id?: string;
  /**
   * ID of the conversation this message belongs to
   */
  conversationId?: string;
  /**
   * ID of the user who created the message
   */
  userId?: string;
  /**
   * Message role (user, assistant, or system)
   */
  role?: 'user' | 'assistant' | 'system' | 'model';
  /**
   * Message content
   */
  content?: string;
  /**
   * Type of message (text, audio, etc.)
   */
  messageType?: 'text' | 'audio' | 'image' | 'video';
  /**
   * Additional metadata for the message
   */
  metadata?: any;
  /**
   * When the message was created
   */
  createdAt?: string;
  /**
   * When the message was last updated
   */
  updatedAt?: string;
}> => {
  return __request({
    method: 'POST',
    path: `/chat/chatMessage/create/live`,
    body: requestBody,
    mediaType: 'application/json',
    errors: {
      401: `Unauthorized`,
    },
  });
};

/**
 * Send a message to chat
 * @param requestBody
 * @returns any Message sent successfully
 * @throws ApiError
 */
export const chatMessageControllerSendMessage = (requestBody: SendMessageParams): CancelablePromise<any> => {
  return __request({
    method: 'POST',
    path: `/chat/chatMessage/send`,
    body: requestBody,
    mediaType: 'application/json',
    errors: {
      401: `Incorrect credentials`,
    },
  });
};

export const useChatMessageControllerGetConversationsService = ({
  method = 'POST',
  ...options
}: UseRequestOption = {}): {
  run: (requestBody: FindMessagesParams) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(chatMessageControllerGetConversations, {
    method,
    ...options,
  });
};

export const useChatMessageControllerCreateService = ({ method = 'POST', ...options }: UseRequestOption = {}): {
  run: (requestBody: CreateChatMessageParams) => void;
  data: {
    /**
     * Conversation ID
     */
    id?: string;
    /**
     * Conversation title
     */
    title?: string;
    /**
     * System prompt used to initialize the conversation
     */
    prompt?: string;
    /**
     * Messages in the conversation
     */
    messages?: Array<{
      /**
       * Message ID
       */
      id?: string;
      /**
       * Message role (user, assistant, or system)
       */
      role?: string;
      /**
       * Message content
       */
      content?: string;
      /**
       * Type of message (text, audio, etc.)
       */
      messageType?: string;
    }>;
  };
} & UseRequestProperties => {
  return useRequest(chatMessageControllerCreate, { method, ...options });
};

export const useChatMessageControllerCreateLiveMessageService = ({
  method = 'POST',
  ...options
}: UseRequestOption = {}): {
  run: (requestBody: CreateChatMessageParams) => void;
  data: {
    /**
     * Message ID
     */
    id?: string;
    /**
     * ID of the conversation this message belongs to
     */
    conversationId?: string;
    /**
     * ID of the user who created the message
     */
    userId?: string;
    /**
     * Message role (user, assistant, or system)
     */
    role?: 'user' | 'assistant' | 'system' | 'model';
    /**
     * Message content
     */
    content?: string;
    /**
     * Type of message (text, audio, etc.)
     */
    messageType?: 'text' | 'audio' | 'image' | 'video';
    /**
     * Additional metadata for the message
     */
    metadata?: any;
    /**
     * When the message was created
     */
    createdAt?: string;
    /**
     * When the message was last updated
     */
    updatedAt?: string;
  };
} & UseRequestProperties => {
  return useRequest(chatMessageControllerCreateLiveMessage, {
    method,
    ...options,
  });
};

export const useChatMessageControllerSendMessageService = ({ method = 'POST', ...options }: UseRequestOption = {}): {
  run: (requestBody: SendMessageParams) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(chatMessageControllerSendMessage, { method, ...options });
};
