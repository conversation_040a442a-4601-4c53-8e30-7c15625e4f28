/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CancelablePromise } from '../core/CancelablePromise';
import { request as __request, upload } from '../core/request';
import { useRequest, UseRequestOption, UseRequestProperties } from '@app-studio/react-request';

/**
 * Get an ephemeral OpenAI Realtime session token
 * @returns any Session token retrieved successfully
 * @throws ApiError
 */
export const openAiRealtimeControllerGetSessionToken = (): CancelablePromise<{
  /**
   * OpenAI realtime session token
   */
  sessionToken?: string;
  /**
   * Token expiration timestamp
   */
  expiresAt?: string;
}> => {
  return __request({
    method: 'GET',
    path: `/chat/openai-realtime/session`,
    errors: {
      401: `Unauthorized`,
    },
  });
};

export const useOpenAiRealtimeControllerGetSessionTokenService = ({
  method = 'GET',
  ...options
}: UseRequestOption = {}): {
  run: () => void;
  data: {
    /**
     * OpenAI realtime session token
     */
    sessionToken?: string;
    /**
     * Token expiration timestamp
     */
    expiresAt?: string;
  };
} & UseRequestProperties => {
  return useRequest(openAiRealtimeControllerGetSessionToken, {
    method,
    ...options,
  });
};
