/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CancelablePromise } from '../core/CancelablePromise';
import { request as __request, upload } from '../core/request';
import { useRequest, UseRequestOption, UseRequestProperties } from '@app-studio/react-request';

/**
 * Get setup wizard steps
 * @returns any Setup wizard steps
 * @throws ApiError
 */
export const setupControllerGetSetupSteps = (): CancelablePromise<any> => {
  return __request({
    method: 'GET',
    path: `/setup/steps`,
    errors: {
      401: `Unauthorized`,
    },
  });
};

/**
 * Get user setup status
 * @returns any User setup status
 * @throws ApiError
 */
export const setupControllerGetUserSetupStatus = (): CancelablePromise<any> => {
  return __request({
    method: 'GET',
    path: `/setup/status`,
    errors: {
      401: `Unauthorized`,
    },
  });
};

/**
 * Update setup progress
 * @returns any Setup progress updated
 * @throws ApiError
 */
export const setupControllerUpdateSetupProgress = (): CancelablePromise<any> => {
  return __request({
    method: 'POST',
    path: `/setup/progress`,
    errors: {
      401: `Unauthorized`,
    },
  });
};

/**
 * Complete setup wizard
 * @returns any Setup wizard completed
 * @throws ApiError
 */
export const setupControllerCompleteSetup = (): CancelablePromise<any> => {
  return __request({
    method: 'POST',
    path: `/setup/complete`,
    errors: {
      401: `Unauthorized`,
    },
  });
};

export const useSetupControllerGetSetupStepsService = ({ method = 'GET', ...options }: UseRequestOption = {}): {
  run: () => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(setupControllerGetSetupSteps, { method, ...options });
};

export const useSetupControllerGetUserSetupStatusService = ({ method = 'GET', ...options }: UseRequestOption = {}): {
  run: () => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(setupControllerGetUserSetupStatus, { method, ...options });
};

export const useSetupControllerUpdateSetupProgressService = ({ method = 'POST', ...options }: UseRequestOption = {}): {
  run: () => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(setupControllerUpdateSetupProgress, { method, ...options });
};

export const useSetupControllerCompleteSetupService = ({ method = 'POST', ...options }: UseRequestOption = {}): {
  run: () => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(setupControllerCompleteSetup, { method, ...options });
};
