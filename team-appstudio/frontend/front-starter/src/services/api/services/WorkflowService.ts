/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CreateImageParams } from '../models/CreateImageParams';
import type { FindImageParams } from '../models/FindImageParams';
import type { FindWorkflowParams } from '../models/FindWorkflowParams';
import type { getTaskParams } from '../models/getTaskParams';
import type { RequestTask } from '../models/RequestTask';
import type { UpdateTaskStatus } from '../models/UpdateTaskStatus';
import type { CancelablePromise } from '../core/CancelablePromise';
import { request as __request, upload } from '../core/request';
import { useRequest, UseRequestOption, UseRequestProperties } from '@app-studio/react-request';

/**
 * As a user, i want to read an workflow
 * @param id
 * @returns any Workflow's data
 * @throws ApiError
 */
export const workflowControllerGet = (id: string): CancelablePromise<any> => {
  return __request({
    method: 'GET',
    path: `/workflow/${id}`,
    errors: {
      404: `Workflow doesn't exists`,
    },
  });
};

/**
 * As a user, i want to find by name
 * @param requestBody
 * @returns any Workflows found
 * @throws ApiError
 */
export const workflowControllerFind = (requestBody: FindWorkflowParams): CancelablePromise<any> => {
  return __request({
    method: 'POST',
    path: `/workflow/find`,
    body: requestBody,
    mediaType: 'application/json',
  });
};

/**
 * Request a new task
 * @param requestBody
 * @returns any Workflow created
 * @throws ApiError
 */
export const workflowControllerRequestTask = (requestBody: RequestTask): CancelablePromise<any> => {
  return __request({
    method: 'POST',
    path: `/workflow/task/request`,
    body: requestBody,
    mediaType: 'application/json',
    errors: {
      401: `Incorrect credentials`,
    },
  });
};

/**
 * get a task
 * @param taskId taskId
 * @param resetNbOfExecution resetNbOfExecution
 * @returns any
 * @throws ApiError
 */
export const workflowControllerGetTask = (taskId: string, resetNbOfExecution?: boolean): CancelablePromise<any> => {
  return __request({
    method: 'GET',
    path: `/workflow/task`,
    query: {
      taskId: taskId,
      resetNbOfExecution: resetNbOfExecution,
    },
  });
};

/**
 * get a workflow tasks
 * @param workflowId workflowId
 * @returns any
 * @throws ApiError
 */
export const workflowControllerGetTasks = (workflowId: string): CancelablePromise<any> => {
  return __request({
    method: 'GET',
    path: `/workflow/tasks`,
    query: {
      workflowId: workflowId,
    },
  });
};

/**
 * Reset a task
 * @param requestBody
 * @returns any Task assigned
 * @throws ApiError
 */
export const workflowControllerResetTask = (requestBody: getTaskParams): CancelablePromise<any> => {
  return __request({
    method: 'POST',
    path: `/workflow/task/reset`,
    body: requestBody,
    mediaType: 'application/json',
  });
};

/**
 * Reset a task
 * @param requestBody
 * @returns any Task assigned
 * @throws ApiError
 */
export const workflowControllerResetTaskImage = (requestBody: getTaskParams): CancelablePromise<any> => {
  return __request({
    method: 'POST',
    path: `/workflow/task/reset/image`,
    body: requestBody,
    mediaType: 'application/json',
  });
};

/**
 * Update task status
 * @param requestBody
 * @returns any Status updated
 * @throws ApiError
 */
export const workflowControllerUpdateTaskStatus = (requestBody: UpdateTaskStatus): CancelablePromise<any> => {
  return __request({
    method: 'PATCH',
    path: `/workflow/task/status`,
    body: requestBody,
    mediaType: 'application/json',
    errors: {
      401: `Incorrect credentials`,
    },
  });
};

/**
 * As a user, I want to edit an image
 * @param formData
 * @returns any image edited
 * @throws ApiError
 */
export const imageWorkflowControllerCreate = (formData: CreateImageParams): CancelablePromise<any> => {
  return __request({
    method: 'POST',
    path: `/workflow/image/edit`,
    formData: formData,
    mediaType: 'multipart/form-data',
    errors: {
      401: `Incorrect credentials`,
    },
  });
};

/**
 * As a user, I want to find a image
 * @param requestBody
 * @returns any image found
 * @throws ApiError
 */
export const imageWorkflowControllerFind = (requestBody: FindImageParams): CancelablePromise<any> => {
  return __request({
    method: 'POST',
    path: `/workflow/image/find`,
    body: requestBody,
    mediaType: 'application/json',
  });
};

/**
 * As a user, I want to read a image
 * @param id
 * @returns any image read
 * @throws ApiError
 */
export const imageWorkflowControllerRead = (id: string): CancelablePromise<any> => {
  return __request({
    method: 'GET',
    path: `/workflow/image/${id}`,
    errors: {
      401: `Incorrect credentials`,
      404: `image doesn't exist`,
    },
  });
};

/**
 * Upload and transcribe an audio file
 * @param formData
 * @returns any The workflow ID for the upload and transcription task
 * @throws ApiError
 */
export const audioWorkflowControllerTranscribe = (formData: { file: Blob }): CancelablePromise<any> => {
  return __request({
    method: 'POST',
    path: `/workflow/audio/transcribe`,
    formData: formData,
    mediaType: 'multipart/form-data',
    errors: {
      401: `Unauthorized`,
    },
  });
};

export const useWorkflowControllerGetService = ({ method = 'GET', ...options }: UseRequestOption = {}): {
  run: (id: string) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(workflowControllerGet, { method, ...options });
};

export const useWorkflowControllerFindService = ({ method = 'POST', ...options }: UseRequestOption = {}): {
  run: (requestBody: FindWorkflowParams) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(workflowControllerFind, { method, ...options });
};

export const useWorkflowControllerRequestTaskService = ({ method = 'POST', ...options }: UseRequestOption = {}): {
  run: (requestBody: RequestTask) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(workflowControllerRequestTask, { method, ...options });
};

export const useWorkflowControllerGetTaskService = ({ method = 'GET', ...options }: UseRequestOption = {}): {
  run: (taskId: string, resetNbOfExecution?: boolean) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(workflowControllerGetTask, { method, ...options });
};

export const useWorkflowControllerGetTasksService = ({ method = 'GET', ...options }: UseRequestOption = {}): {
  run: (workflowId: string) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(workflowControllerGetTasks, { method, ...options });
};

export const useWorkflowControllerResetTaskService = ({ method = 'POST', ...options }: UseRequestOption = {}): {
  run: (requestBody: getTaskParams) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(workflowControllerResetTask, { method, ...options });
};

export const useWorkflowControllerResetTaskImageService = ({ method = 'POST', ...options }: UseRequestOption = {}): {
  run: (requestBody: getTaskParams) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(workflowControllerResetTaskImage, { method, ...options });
};

export const useWorkflowControllerUpdateTaskStatusService = ({ method = 'PATCH', ...options }: UseRequestOption = {}): {
  run: (requestBody: UpdateTaskStatus) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(workflowControllerUpdateTaskStatus, { method, ...options });
};

export const useImageWorkflowControllerCreateService = ({ method = 'POST', ...options }: UseRequestOption = {}): {
  run: (formData: CreateImageParams) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(imageWorkflowControllerCreate, { method, ...options });
};

export const useImageWorkflowControllerFindService = ({ method = 'POST', ...options }: UseRequestOption = {}): {
  run: (requestBody: FindImageParams) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(imageWorkflowControllerFind, { method, ...options });
};

export const useImageWorkflowControllerReadService = ({ method = 'GET', ...options }: UseRequestOption = {}): {
  run: (id: string) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(imageWorkflowControllerRead, { method, ...options });
};

export const useAudioWorkflowControllerTranscribeService = ({ method = 'POST', ...options }: UseRequestOption = {}): {
  run: (formData: { file: Blob }) => void;
  data: any;
} & UseRequestProperties => {
  return useRequest(audioWorkflowControllerTranscribe, { method, ...options });
};
