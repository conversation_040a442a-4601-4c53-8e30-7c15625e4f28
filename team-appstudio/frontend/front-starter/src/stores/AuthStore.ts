import { create } from 'zustand';
import * as AsyncStorage from 'src/utils/localstorage';
import { AccountService, AuthService, OpenAPI } from 'src/services/api';
import { setToken } from 'src/utils/request';
import { useAppStore } from './AppStore';
import moment from 'moment';
import AppAnalytics from 'src/utils/analytics';

type User = {
  id: string;
  blocked: boolean;
  createdAt: string;
  image?: any;
  imageUrl?: string;
  inactive: boolean;
  language: string;
  birthdate: string;
  email: string;
  enterprise: string;
  textModel: string;
  name: string;
  textProvider: string;
  role: string;
  updatedAt: string;
  audioModel: string;
  audioProvider: string;
  audioTemp: number;
  imageModel: string;
  imageProvider: string;
  imageTemp: number;
  videoModel: string;
  videoProvider: string;
  videoTemp: number;
  textTemp: number;
};

type AuthState = {
  isAuthentificated: boolean;
  token: boolean | string;
  device: boolean;
  user: null | User;
  checkAccount: boolean;
  account: any;
  location: string;
  setAccount: (account: any) => void;
  setLocation: (location: any) => void;
  setCheckAccount: (checkAccount: any) => void;
  setToken: (token: string, me?: any, onSuccess?: Function) => void;
  login: (params: any, onSuccess?: Function) => void;
  loadAccount: () => void;
  logout: (location?: string) => void;
  refresh: () => void;
  isSubscribed: boolean | null;
  hasSubscription: () => Promise<void>;
};

export const useAuthStore = create<AuthState>((set, get) => ({
  isAuthentificated: false,
  token: false,
  device: false,
  user: null,
  account: null,
  checkAccount: true,
  location: '/',
  isSubscribed: null,
  setLocation: (location) => set({ location }),
  setAccount: (account) => set({ account }),
  setCheckAccount: (checkAccount) => set({ checkAccount }),
  setToken: async (token, me, onSuccess) => {
    try {
      if (token) {
        setToken(token);
        await AsyncStorage.write('@token', token);
        //   await AsyncStorage.write('@email', email);

        const storedToken = await AsyncStorage.read('@token');

        try {
          OpenAPI.HEADERS = {
            Authorization: `Bearer ${storedToken}`,
          };
          if (me) {
            await set({ token: storedToken, isAuthentificated: true, user: me });
            AppAnalytics.identify(me.id, me);
          } else {
            const response: any = await AuthService.authUserControllerMe();
            if (response.data) {
              await set({ token: storedToken, isAuthentificated: true, user: response.data });
              AppAnalytics.identify(response.data.id, response.data);
            }
          }
          if (onSuccess) {
            onSuccess();
          }
        } catch (e) {
          console.warn(e);
          set({ isAuthentificated: false, token: false });
        }
      } else {
        await AsyncStorage.remove('@token');
        set({ token: false });
      }
      useAppStore.getState().setReady();
    } catch (error) {
      console.warn(error);
      set({ isAuthentificated: false, token: false });
      useAppStore.getState().setReady();
    }
  },
  refresh: async () => {
    const user = get().user;
    if (user) {
      try {
        const response: any = await AuthService.authUserControllerMe();

        if (response) {
          set({ user: response.data });
        }

        console.log('response', response);
        useAppStore.getState().setReady();
      } catch (e) {
        console.warn(e);
        set({ isAuthentificated: false, token: false });
      }
    }
  },
  hasSubscription: async () => {
    const { setAccount, isSubscribed } = get();

    // If the subscription status is already set, return it
    if (isSubscribed !== null) {
      return;
    }
    // If account data is not available, fetch it
    try {
      const { data } = await AccountService.accountControllerMe();
      setAccount(data); // Store the account data

      const isValidSubscription = checkAndValidateSubscription(data);
      set({ isSubscribed: isValidSubscription }); // Cache the result
    } catch (error) {
      console.warn(error);
      set({ isSubscribed: false });
    }

    // Function to check and validate subscription
    function checkAndValidateSubscription(data: any): boolean {
      if (!data || !data.startSubscriptionDate || !data.endSubscriptionDate) {
        return false;
      }

      const now = moment.utc();
      const start = moment.utc(data.startSubscriptionDate);
      const end = moment.utc(data.endSubscriptionDate);

      return now.isBetween(start, end, null, '[]') && data.subscription !== 'free';
    }
  },

  loadAccount: async () => {
    try {
      const { data } = await AccountService.accountControllerMe();
      // console.log('account', data);
      set({ checkAccount: false, account: data });
    } catch (error) {
      console.warn(error);
      // Handle error
    }
  },
  login: async (params, onSuccess) => {
    try {
      const {
        data: { token, me },
      } = await AuthService.authUserControllerSignIn(params);
      console.log('token', token);
      useAuthStore.getState().setToken(token, me, onSuccess);
    } catch (error) {
      console.warn(error);
    }
  },

  onLoginSucceed: async (token) => {},
  logout: async (location = '/') => {
    set({ isAuthentificated: false, token: false, user: null, location });
    OpenAPI.HEADERS = {};
    await AsyncStorage.remove('@token');
    await AsyncStorage.remove('@email');
    await AsyncStorage.remove('@redirectData');
    await AsyncStorage.remove('@redirectUrl');
    AppAnalytics.reset();
    window.location.href = '/auth/login';
    return true;
  },
}));

export const isAuthentificated = () => {
  try {
    return useAuthStore.getState().isAuthentificated;
  } catch {}
  return false;
};

export const onSuccessAuthentication = () => {
  const redirectUrl = AsyncStorage.read('@redirectUrl');

  console.log({ redirectUrl });

  if (redirectUrl && Object.keys(redirectUrl).length > 0) {
    window.location.href = redirectUrl;
    AsyncStorage.remove('@redirectUrl');
  } else {
    window.location.href = '/home';
  }
};
(async () => {
  const token = await AsyncStorage.read('@token');
  const email = await AsyncStorage.read('@email');
  AppAnalytics.reset();
  if (typeof token == 'string') {
    useAuthStore.getState().setToken(token);
  } else {
    useAppStore.getState().setReady();
  }
})();
