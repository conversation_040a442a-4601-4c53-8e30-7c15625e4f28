import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface Product {
  id: string;
  name: string;
  price: number;
}

export interface CartItem extends Product {
  quantity: number;
}

interface CartStore {
  items: CartItem[];
  totalItems: number;
  totalAmount: number;
  addItem: (item: Omit<CartItem, 'quantity'> & { quantity?: number }) => void;
  removeItem: (itemId: string) => void;
  updateQuantity: (itemId: string, quantity: number) => void;
  deleteItem: (itemId: string) => void;
  clearCart: () => void;
}

const useCartStore = create<CartStore>()(
  persist(
    (set) => ({
      items: [],
      totalItems: 0,
      totalAmount: 0,
      addItem: (item) =>
        set((state) => {
          const quantity = item.quantity ?? 1;
          const existingItem = state.items.find((i) => i.id === item.id);
          if (existingItem) {
            const updatedItems = state.items.map((i) =>
              i.id === item.id ? { ...i, quantity: i.quantity + quantity } : i
            );
            return {
              items: updatedItems,
              totalItems: state.totalItems + quantity,
              totalAmount: state.totalAmount + item.price * quantity,
            };
          }
          return {
            items: [...state.items, { ...item, quantity }],
            totalItems: state.totalItems + quantity,
            totalAmount: state.totalAmount + item.price * quantity,
          };
        }),
      removeItem: (itemId) =>
        set((state) => {
          const itemToRemove = state.items.find((item) => item.id === itemId);
          if (!itemToRemove) return state;
          return {
            items: state.items.filter((item) => item.id !== itemId),
            totalItems: state.totalItems - itemToRemove.quantity,
            totalAmount: state.totalAmount - itemToRemove.price * itemToRemove.quantity,
          };
        }),
      updateQuantity: (itemId, quantity) =>
        set((state) => {
          const itemToUpdate = state.items.find((item) => item.id === itemId);
          if (!itemToUpdate) return state;

          if (quantity === 0) {
            // Handle removal directly here instead of calling removeItem
            return {
              items: state.items.filter((item) => item.id !== itemId),
              totalItems: state.totalItems - itemToUpdate.quantity,
              totalAmount: state.totalAmount - itemToUpdate.price * itemToUpdate.quantity,
            };
          }

          const updatedItems = state.items.map((item) => (item.id === itemId ? { ...item, quantity } : item));

          return {
            items: updatedItems,
            totalItems: updatedItems.reduce((sum, item) => sum + item.quantity, 0),
            totalAmount: updatedItems.reduce((sum, item) => sum + item.price * item.quantity, 0),
          };
        }),
      deleteItem: (itemId) =>
        set((state) => {
          const itemToDelete = state.items.find((item) => item.id === itemId);
          if (!itemToDelete) return state;
          return {
            items: state.items.filter((item) => item.id !== itemId),
            totalItems: state.totalItems - itemToDelete.quantity,
            totalAmount: state.totalAmount - itemToDelete.price * itemToDelete.quantity,
          };
        }),
      clearCart: () =>
        set({
          items: [],
          totalItems: 0,
          totalAmount: 0,
        }),
    }),
    {
      name: 'cart-storage',
    }
  )
);

export default useCartStore;
