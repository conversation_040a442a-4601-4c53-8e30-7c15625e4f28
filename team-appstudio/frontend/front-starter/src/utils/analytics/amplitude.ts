import { init, add, setUserId, logEvent, Revenue, flush, getDeviceId, reset } from '@amplitude/analytics-browser';

import { AMPLITUDE_KEY } from 'src/configs/AppConfig';
import * as sessionReplay from '@amplitude/session-replay-browser';
import moment from 'moment';

export const AmplitudeAnalytics = {
  init() {
    init(AMPLITUDE_KEY, { serverZone: 'EU', autocapture: { elementInteractions: true } });
    console.log('AmplitudeAnalytics init');
    sessionReplay.init(AMPLITUDE_KEY, {
      deviceId: navigator.userAgent + '-' + navigator.platform,
    });
    // When you send events to Amplitude, call this event to get
    // the most up to date session replay properties for the event
  },

  identify({ userId, userDetails = {} }) {
    setUserId(userId);
    add(userDetails);

    sessionReplay.setSessionId(moment().format('YYYY-MM-DDTHH:mm:ss') + '-' + userId);
    const sessionReplayProperties = sessionReplay.getSessionReplayProperties();
    logEvent('sessionReplay', sessionReplayProperties);
  },

  sessionUrl({ sessionUrl }) {
    logEvent('sessionUrl', { sessionUrl });
  },
  screen({ eventName, properties }) {
    logEvent(eventName, properties);
  },
  track({ eventName, properties }) {
    logEvent(eventName, properties);
  },
  purchasedEvent({ eventName, properties: { order, cart, place, items, nbItems, price } }) {
    logEvent(eventName, {
      order,
      cart,
      place,
      items,
      nbItems,
      price,
    });

    items.map((item) => {
      const revenue: any = new Revenue()
        .setProductId(item.product.id)
        .setPrice(item.product.price)
        .setQuantity(item.quantity)
        .setEventProperties({
          order,
          cart,
          place,
          items,
          nbItems,
          price,
        });
      revenue(revenue);
    });
  },
  reset() {
    logEvent('Logout');
    flush(); // not string 'null'
    getDeviceId();
    reset();
  },
};
