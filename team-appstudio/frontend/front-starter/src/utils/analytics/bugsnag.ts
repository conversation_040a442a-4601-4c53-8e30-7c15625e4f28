import { isDev } from 'src/utils/env';
import bugsnagClient from 'src/utils/bugsnag';

export const BugsnagAnalytics = {
  identify({ userId, userDetails = {} }) {
    bugsnagClient.leaveBreadcrumb('user', { userId, userDetails });
  },

  sessionUrl({ sessionUrl }) {
    bugsnagClient.leaveBreadcrumb('sessionUrl', { sessionUrl });
  },

  screen({ eventName }) {
    if (!isDev()) {
      bugsnagClient.leaveBreadcrumb(eventName);
    }
  },
};
