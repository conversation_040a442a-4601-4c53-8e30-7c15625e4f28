import { GOOGLE_ANALYTICS_ID } from 'src/configs/AppConfig';
import { loadExternalJS } from '../script';

export const GoogleAnalytics = {
  init() {
    try {
      loadExternalJS([
        'https://www.googletagmanager.com/gtag/js?id=' + GOOGLE_ANALYTICS_ID,
        // '//js.hsforms.net/forms/v2.js',
      ]);

      (window as any).dataLayer = (window as any).dataLayer || [];
      const gtag: any = (args: any) => {
        (window as any).dataLayer.push(args);
      };
      gtag('js', new Date());

      // gtag('config', GOOGLE_ANALYTICS_ID, { optimize_id: GOOGLE_OPTIMIZE_ID });
    } catch (e) {
      console.error(e);
    }
  },
};
