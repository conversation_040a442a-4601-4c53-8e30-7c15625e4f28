import { isBrowser, isDev } from 'src/utils/env';
import { DevAnalytics } from 'src/utils/analytics/dev';
import { AmplitudeAnalytics } from './amplitude';

const init: any[] = [];
const modules: any[] = [];

// export const onTouch = (onPress: Function, args: any, action?: string) => {
//   if (onPress) {
//     console.log('action', action);
//     try {
//       onPress();
//       AppAnalytics.track('click', {
//         action,
//       });
//     } catch (e) {
//       console.warn(e);
//     }
//   }
// };

if (isBrowser()) {
  const AmplitudeAnalytics = require('src/utils/analytics/amplitude');
  const BugsnagAnalytics = require('src/utils/analytics/bugsnag');
  // const SmartlookAnalytics = require('src/utils/analytics/smartlook');

  init.push(AmplitudeAnalytics);
  // init.push(SmartlookAnalytics);
  init.push(BugsnagAnalytics);
}
export const AppAnalytics = {
  async call(functionName: string, params = {}) {
    modules.map((client) => {
      try {
        if (typeof client[functionName] === 'function') {
          client[functionName](params);
        }
      } catch (e) {
        console.warn(e);
      }
    });
  },

  async init() {
    console.log('init', init);

    init.map((client) => {
      try {
        //
        if (client.init !== undefined) {
          client.init();
          modules.push(client);
          console.log('modules', modules);
        }
      } catch (e) {
        console.warn(e);
      }
    });

    setTimeout(async () => {
      try {
        if (isBrowser()) {
          // if (typeof modules[1].getData !== undefined) {
          //   const {playUrl} = await modules[1].getData();
          //   this.call('sessionUrl', {playUrl});
          // }
        }
      } catch (e) {
        console.log(e);
      }
    }, 5000);
  },

  identify(userId: any, userDetails = {}) {
    this.call('identify', { userId, userDetails });
  },

  sessionUrl(sessionUrl: string) {
    this.call('sessionUrl', { sessionUrl });
  },

  page(url: string, properties = {}) {
    this.call('screen', { eventName: `Display ${url}`, properties });
  },

  screen(screenName: string) {
    this.call('screen', { eventName: `Display ${screenName}` });
  },

  modal(modalName: string, properties = {}) {
    if (modalName) {
      this.call('track', {
        eventName: `Open modal ${modalName.toString().replace('Modal', ' Modal')}`,
        properties,
      });
    } else {
      this.call('track', { eventName: 'Close Modal', properties });
    }
  },

  completedRegistrationEvent(registrationMethod) {
    this.call('track', { eventName: `Registered with ${registrationMethod}` });
  },

  viewedContentEvent(product) {
    this.call('track', { eventName: 'View product', properties: { product } });
  },

  addedToCartEvent(product, quantity) {
    this.call('track', {
      eventName: 'Add product to cart',
      properties: {
        product,
        quantity,
      },
    });
  },

  removedToCartEvent(product, quantity) {
    this.call('track', {
      eventName: 'Remove product from cart',
      properties: {
        product,
        quantity,
      },
    });
  },

  initiatedCheckoutEvent(cart, items, nbItems, card, total) {
    this.call('track', {
      eventName: 'Initiate Checkout',
      properties: {
        cart,
        items,
        nbItems,
        card,
        total,
      },
    });
  },

  purchasedEvent(order, cart, place, items, nbItems, price) {
    this.call('purchasedEvent', {
      eventName: 'Order created',
      properties: {
        order,
        cart,
        place,
        items,
        nbItems,
        price,
      },
    });
  },
  addedPaymentInfoEvent(success) {
    this.call('track', {
      eventName: 'Payment Info added',
      properties: {
        success,
      },
    });
  },
  track(eventName: string, properties = {}) {
    this.call('track', {
      eventName,
      properties,
    });
  },
  error(error) {
    this.call('error', error);
  },
  reset() {
    this.call('reset');
  },
};

export default AppAnalytics;
