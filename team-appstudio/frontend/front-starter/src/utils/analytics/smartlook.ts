import { <PERSON>ARTLOOK_KEY } from 'src/configs/AppConfig';
import Smartlook from 'smartlook-client';

export const SmartlookAnalytics = {
  init() {
    Smartlook.init(SMARTLOOK_KEY);
  },

  // async getData() {
  //   return await new Promise(resolve => {
  //     Smartlook.getData(resolve);
  //   });
  // },

  identify({ userId, userDetails = {} }) {
    Smartlook.identify(userId, userDetails);
  },
  screen({ eventName }) {
    Smartlook.navigation(eventName);
  },
  track({ eventName, properties }) {
    Smartlook.track(eventName, properties);
  },
  purchasedEvent({ eventName, properties }) {
    Smartlook.track(eventName, properties);
  },
  reset() {
    Smartlook.restart();
  },
  error(error) {
    Smartlook.error(error);
  },
};
