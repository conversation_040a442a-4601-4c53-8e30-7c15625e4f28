import { isBrowser } from 'src/utils/env';

export function loadExternalJS(arr: string[]) {
  if (!isBrowser()) {
    return;
  }
  if (!(arr && arr.length > 0)) {
    return;
  }
  const scriptArr = Object.values(document.getElementsByTagName('script'));
  const ret: any[] = [];
  arr.forEach((url) => {
    const script = document.createElement('script');
    const html = document.getElementsByTagName('html')[0];
    script.setAttribute('src', url);
    script.setAttribute('async', 'true');
    script.setAttribute('charset', 'UTF-8');
    // 避免重复加载
    if (scriptArr.every((row) => !row.src.includes(url))) {
      html.appendChild(script);
      ret.push(script);
    }
  });
  return ret;
}
