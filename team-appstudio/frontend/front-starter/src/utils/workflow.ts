export const getStepIdByOrder = (action, requirementOrder, stepOrder) => {
  // Find the requirement with the given order
  const requirement = action?.requirements?.find((req) => req.order === requirementOrder);

  // Find the step within the requirement with the given order
  const step = requirement.steps.find((step) => step.order === stepOrder);

  if (!step) {
    console.error(`Step with order ${stepOrder} not found in requirement ${requirement.name}.`);
    return '';
  }
  // Return the step id
  return step.id;
};
