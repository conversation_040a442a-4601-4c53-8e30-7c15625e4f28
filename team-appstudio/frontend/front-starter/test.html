<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Accordion Component</title>
<link "https://unpkg.com/tailwindcss@^3.0/dist/tailwind.min.css" rel="stylesheet">
<script src="https://cdn.tailwindcss.com"></script>

</head>
<body class="antialiased bg-gray-100">
<div x-data="accordion" class="container mx-auto p-5">
  <div class="bg-white p-6 rounded shadow-md">
    <div class="mb-4 border-b border-gray-200">
      <div class="font-semibold text-xl text-gray-800 leading-tight">
        Accordion
      </div>
      <p class="text-sm text-gray-600">A collapsible component that displays content in a vertical stack.</p>
    </div>
    
    <div class="space-y-2">
      <!-- Accordion item 1 -->
      <div>
        <h2 @click="toggleTab(1)" class="flex justify-between items-center p-5 font-semibold text-left text-gray-800 bg-gray-100 rounded-t cursor-pointer">
          Section 1
          <span class="text-gray-600" x-show="openTab !== 1">+</span>
          <span class="text-gray-600" x-show="openTab === 1">-</span>
        </h2>
        <div x-show="openTab === 1" class="p-5 border border-t-0 border-gray-200 bg-white">
          <p class="text-gray-600">Content for section 1 goes here.</p>
        </div>
      </div>

      <!-- Accordion item 2 -->
      <div>
        <h2 @click="toggleTab(2)" class="flex justify-between items-center p-5 font-semibold text-left text-gray-800 bg-gray-100 cursor-pointer">
          Section 2
          <span class="text-gray-600" x-show="openTab !== 2">+</span>
          <span class="text-gray-600" x-show="openTab === 2">-</span>
        </h2>
        <div x-show="openTab === 2" class="p-5 border border-t-0 border-gray-200 bg-white">
          <p class="text-gray-600">Content for section 2 goes here.</p>
        </div>
      </div>

      <!-- Accordion item 3 -->
      <div>
        <h2 @click="toggleTab(3)" class="flex justify-between items-center p-5 font-semibold text-left text-gray-800 bg-gray-100 rounded-b cursor-pointer">
          Section 3
          <span class="text-gray-600" x-show="openTab !== 3">+</span>
          <span class="text-gray-600" x-show="openTab === 3">-</span>
        </h2>
        <div x-show="openTab === 3" class="p-5 border border-t-0 border-gray-200 bg-white rounded-b">
          <p class="text-gray-600">Content for section 3 goes here.</p>
        </div>
      </div>
    </div>
  </div>
</div>

<script src="//unpkg.com/alpinejs" defer></script>
</body>
</html>
