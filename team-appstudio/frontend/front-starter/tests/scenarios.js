const path = require('path');
const fs = require('fs');

global.isTestEnv = true;

// Function to recursively load all .test.js files in the tests folder (including subfolders)
function loadScenarios(dir) {
  const scenarios = [];

  try {
    // Check if directory exists
    if (!fs.existsSync(dir)) {
      console.error(`Directory does not exist: ${dir}`);
      return scenarios;
    }

    // Read all files and subdirectories in the current directory
    fs.readdirSync(dir).forEach((file) => {
      const filePath = path.join(dir, file);

      try {
        // If it's a directory, recursively search for more .test.js files
        if (fs.statSync(filePath).isDirectory()) {
          scenarios.push(...loadScenarios(filePath)); // Recursively add scenarios from subdirectories
        } else if (file.endsWith('.test.js')) {
          // If it's a .test.js file, require it and push it to the scenarios array
          console.log(`Loading test file: ${filePath}`);
          const scenario = require(filePath);
          scenarios.push(scenario);
        }
      } catch (fileError) {
        console.error(`Error processing file ${filePath}:`, fileError);
      }
    });
  } catch (dirError) {
    console.error(`Error reading directory ${dir}:`, dirError);
  }

  return scenarios;
}

// Start searching from the '__tests__' directory
const testDir = path.resolve(__dirname, '..', 'src', '__tests__');
console.log('Looking for test files in:', testDir);
const scenarios = loadScenarios(testDir);

console.log(`Found ${scenarios.length} test scenarios`);

describe('Test Scenarios Execution', () => {
  afterEach(async () => {
    // Wait for page redirects or action completion
    await browser.pause(4000);
  });

  if (scenarios.length === 0) {
    it('No test scenarios found', () => {
      console.warn('No test scenarios were found in the __tests__ directory');
    });
  } else {
    scenarios.forEach((scenario) => {
      it(scenario.story, async () => {
        if (scenario.useApi) {
          // First, fetch test data
          const testData = await scenario.steps[0]();
          console.log('Test data fetched:', testData);

          // Then, execute the test with the fetched data
          await scenario.steps[1](browser, testData);
        } else {
          // Execute all steps in sequence
          for (let i = 0; i < scenario.steps.length; i++) {
            console.log(`Executing step ${i + 1} of ${scenario.steps.length}`);

            // If it's the performTest step, pass the browser object
            if (scenario.steps[i].name === 'performTest') {
              await scenario.steps[i](browser);
            } else {
              // For other steps like cleanData, just execute them
              await scenario.steps[i]();
            }
          }
        }
      });
    });
  }
});
