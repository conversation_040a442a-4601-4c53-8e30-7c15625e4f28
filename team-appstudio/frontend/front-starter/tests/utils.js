// utils.js
const axios = require('axios');

async function fetchData(params = {}) {
  try {
    const response = await axios.get('http://localhost:3000/seed/get', { params });
    return response.data.data;
  } catch (error) {
    console.error('Error fetching data:', error);
    return null; // Return null or a default value in case of error
  }
}

/**
 * Fetches test user credentials from the seed API
 * @returns {Promise<{email: string, password: string}>} User credentials
 */
async function fetchTestCredentials() {
  try {
    const response = await axios.get('http://localhost:3000/seed/get', { params: { seed: 'test' } });
    const seed = response.data.data;

    const credentials = {
      email: seed?.params[0]?.email ?? '',
      password: seed?.params[0]?.password ?? '',
    };

    return credentials;
  } catch (error) {
    console.error('Error fetching test credentials:', error.message);
    throw new Error('Failed to fetch test credentials');
  }
}

/**
 * Authenticates with the API using provided credentials
 * @param {Object} credentials - User credentials with email and password
 * @returns {Promise<string>} Authentication token
 */
async function authenticate(credentials) {
  try {
    const authResponse = await axios.post('http://localhost:3000/auth/user/signIn', credentials);
    console.log('Auth response:', authResponse.data);

    if (!authResponse || !authResponse.data) {
      throw new Error('Authentication response is invalid');
    }

    // Extract token from response
    let token;
    if (authResponse.data.data && authResponse.data.data.token) {
      token = authResponse.data.data.token;
    } else if (authResponse.data.token) {
      token = authResponse.data.token;
    } else {
      console.error('Token not found in response:', authResponse.data);
      throw new Error('Token not found in response');
    }

    return token;
  } catch (error) {
    console.error('Authentication error:', error.response ? error.response.data : error.message);
    throw new Error('Authentication failed');
  }
}

/**
 * Deletes all access records using the provided token
 * @param {string} token - Authentication token
 * @returns {Promise<Object>} Response from the delete operation
 */
async function deleteAllAccess(token) {
  try {
    console.log('Sending request to delete access with token:', token);
    const headers = { Authorization: `Bearer ${token}` };
    const deleteResponse = await axios.delete('http://localhost:3000/access/delete', { headers });
    console.log('Delete response:', deleteResponse.data);
    return deleteResponse.data;
  } catch (error) {
    console.error('Error deleting access:', error.response ? error.response.data : error.message);
    throw new Error('Failed to delete access');
  }
}

/**
 * Main function to clean access control records
 */
async function cleanAccessControl() {
  try {
    // Step 1: Get test credentials
    const credentials = await fetchTestCredentials();

    // Step 2: Authenticate and get token
    const token = await authenticate(credentials);

    // Step 3: Delete all access records
    const result = await deleteAllAccess(token);

    return { success: true, result };
  } catch (error) {
    console.error('Error in cleanAccessControl:', error.message);
    return { error: error.message };
  }
}

async function performTestStep(step) {
  let element = await $(`#${step.id}`);

  if (step.class) {
    element = await $(`.${step.class}`);
  }

  console.log(`Performing action: ${step.action} on element with ID: ${step.id ?? step.class}`);

  // Helper function to wait for a specified time
  const wait = async (duration) => {
    console.log(`Waiting for ${duration || 1000} ms`);
    return new Promise((resolve) => setTimeout(resolve, duration || 1000));
  };

  switch (step.action) {
    case 'click':
      console.log(`Clicking element with ID: ${step?.id ?? step.class}`);
      await element.click();
      break;
    case 'wait':
      await wait(step.duration);
      break;
    case 'upload':
      console.log(`Uploading file to element with ID: ${step.id}`);
      await element.setValue(step.filePath); // This uploads the file by setting its path
      break;
    case 'text':
      console.log(`Setting text on element with ID: ${step.id} to: ${step.value}`);
      // Method 1: Using keyboard shortcuts
      await element.click(); // Focus the element
      // For Mac (Command+A then Delete)
      await browser.keys(['Command', 'a']);
      await browser.keys(['Delete']);

      // Method 2: Using JavaScript executor as fallback
      await browser.execute((id) => {
        const el = document.getElementById(id);
        if (el) {
          el.value = '';
        }
      }, step.id);

      // Finally set the new value
      await element.setValue(step.value);
      break;
    case 'dropdown':
      console.log(`Setting value dynamically for hidden or zero-sized <select> element`);
      await browser.execute((index) => {
        const dropdown = document.querySelector('ul[role="dropdown"]'); // Adjust the selector if needed
        if (dropdown) {
          const options = dropdown.querySelectorAll('li'); // Select all <li> options
          if (options.length > index) {
            options[index].click(); // Simulate clicking the option at the specified index
          } else {
            console.warn(`No option found at index ${index}, total options: ${options.length}`);
          }
        } else {
          console.warn('Dropdown not found');
        }
      }, step.index); // Pass the index dynamically

      break;

    case 'navigate':
      console.log(`Navigating to URL: ${step.url}`);
      await browser.url(step.url);
      break;

    default:
      console.warn(`Unknown action: ${step.action} for element with ID: ${step.id}`);
  }
}

// Helper function to generate a random string
function generateRandomString(length) {
  return Math.random()
    .toString(36)
    .substring(2, length + 2);
}

function generateRandomName() {
  return `user_${generateRandomString(7)}`; // Random name prefixed with 'user_'
}

function generateRandomNumber() {
  const randomDigits = () => Math.floor(Math.random() * 9) + 1; // Generate random number between 1 and 9
  return Array.from({ length: 10 }, randomDigits).join('');
}

module.exports = {
  fetchData,
  cleanAccessControl,
  performTestStep,
  generateRandomString,
  generateRandomName,
  generateRandomNumber,
};
