{
  "compilerOptions": {
    "module": "esnext",  // or "es6"
    "target": "es6", 
    "jsx": "react-jsx",
    "moduleResolution": "node",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": false,
    "isolatedModules": true,
    "noEmit": true,
    "noImplicitAny": false,
    "resolveJsonModule": true,
    "lib": [
      "dom", 
      "es6",
      "dom.iterable",
      "esnext"
    ],
    
    "sourceMap": true,
    "baseUrl": "./",
    "rootDir": "./",
    "paths": {
      "src/*": ["./src/*"], 
       "@/*": ["./*"]
    }
  },
  "include": [
    "./src/",
    ".eslintrc.js",
    ".prettierrc.js",
    "jest.config.js", "babel.config.js", "components",
  ],
  "exclude": [
    "node_modules",
    "build",
    "dist",
    "scripts",
    "webpack"
  ]
}