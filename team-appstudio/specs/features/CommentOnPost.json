{"feature": {"name": "CommentOnPost", "description": "Enables users to add comments to a specific blog post, fostering engagement and discussion.", "userExperience": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "findComments_request_1"}}, "where": "/posts/:id"}, {"who": "User", "action": {"type": "fill", "element": {"type": "form:field", "eventId": "comment_input"}}, "where": "/posts/:id"}, {"who": "User", "action": {"type": "click", "element": {"type": "button", "eventId": "submit_comment_button"}}, "where": "/posts/:id"}, {"who": "App", "action": {"type": "send", "request": {"requestId": "createComment_request_1", "notify": {"target": "owner", "title": "New Comment on Your Post", "message": "A new comment has been added to your post.", "link": "/posts/:id"}}}, "when": {"type": "click", "element": {"type": "button", "eventId": "submit_comment_button"}}, "where": "/posts/:id"}, {"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "findComments_request_1"}}, "when": {"type": "send", "request": {"requestId": "createComment_request_1"}}, "where": "/posts/:id"}], "screens": {"/posts/:id": "The screen displaying the full content of a blog post, including a section for viewing and adding comments."}, "conditions": ["User must be able to input text into a comment field.", "Comments must be associated with the specific post they are made on."], "dataSchemas": {"comment": {"canBeCommented": false, "usePayment": false, "description": "Entity representing a user's comment on a post.", "fields": {"id": {"type": "string", "required": true, "description": "Unique identifier for the comment."}, "content": {"type": "string", "required": true, "description": "The content of the comment."}, "userId": {"type": "string", "required": true, "isUser": true, "description": "The ID of the user who posted the comment."}, "objectId": {"type": "string", "required": true, "description": "The ID of the object (e.g., post) the comment is on."}, "objectType": {"type": "string", "required": true, "description": "The type of object the comment is on (e.g., 'post')."}}}}, "requests": [{"requestId": "findComments_request_1", "isArray": true, "dataSchema": "comment", "type": "Find", "body": {"objectId": "string", "objectType": "string"}}, {"requestId": "createComment_request_1", "isArray": false, "dataSchema": "comment", "type": "Create", "body": {"content": "string", "objectId": "string", "objectType": "string"}, "notifyLink": "/posts/:id"}]}}