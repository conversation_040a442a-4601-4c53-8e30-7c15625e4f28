{"feature": {"name": "Generate Post", "description": "Allows users to generate blog posts using AI based on a topic prompt.", "userExperience": [{"who": "User", "if": null, "action": {"type": "type", "element": {"type": "input", "eventId": "topic_input"}}, "where": "/generate-post"}, {"who": "User", "if": null, "action": {"type": "click", "element": {"type": "button", "eventId": "generate_button"}}, "where": "/generate-post"}, {"who": "App", "if": null, "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "click", "element": {"type": "button", "eventId": "generate_button"}}, "where": "/generate-post"}, {"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "generatePost_request_1"}}, "where": "/workflow/:id"}, {"who": "App", "if": null, "action": {"type": "navigate", "path": "/posts/:id"}, "when": {"type": "load", "request": {"requestId": "generatePost_request_1"}}, "where": "/workflow/:id"}], "screens": {"/generate-post": "The main screen where users input a topic to generate a blog post.", "/workflow/:id": "The screen that checks the generation of the post.", "/posts/:id": "The screen that displays the generated blog post."}, "conditions": ["User is able to input a topic for post generation.", "AI generates a blog post based on the provided topic.", "Generated post is displayed upon completion."], "dataSchemas": {"post": {"description": "Entity for a blog post.", "canBeCommented": true, "usePayment": false, "fields": {"id": {"type": "string", "required": true, "description": "Id of the blog post."}, "content": {"type": "string", "required": true, "description": "Full content of the blog post."}, "topic": {"type": "string", "required": true, "description": "Topic used to generate the post."}, "workflowId": {"type": "string", "required": false, "description": "Workflow identifier for the post."}}}}, "requests": [{"requestId": "generatePost_request_1", "isArray": false, "dataSchema": "post", "type": "Create", "useWorkflow": true, "tasks": [{"name": "postWorkflowService.generatePost", "uiDescription": "Generating your blog post...", "dependencies": null, "connect": null}, {"name": "postWorkflowService.storePost", "uiDescription": "Storing your post...", "dependencies": ["postWorkflowService.generatePost"], "connect": ["postWorkflowService.generatePost"]}], "useAi": {"generationType": ["text"], "role": "AI assistant", "objective": "Generate engaging blog posts based on user topics."}, "body": {"topic": "string"}, "notify": {"target": "me", "title": "Post Generated!", "message": "Your blog post has been successfully generated.", "link": "/posts/:id"}}], "paymentRestriction": null, "paymentEntity": null}}