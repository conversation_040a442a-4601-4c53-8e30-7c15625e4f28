{"feature": {"name": "ListPosts", "description": "Displays a list of all available blog posts, showing only the title, author, and a 'Read Post' button.", "userExperience": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "listPosts_request_1"}}, "where": "/posts"}, {"who": "User", "if": null, "action": {"type": "click", "element": {"type": "list:item", "eventId": "read_post_button"}}, "where": "/posts"}, {"who": "App", "if": null, "action": {"type": "navigate", "path": "/posts/:id"}, "when": {"type": "click", "element": {"type": "list:item", "eventId": "read_post_button"}}, "where": "/posts"}, {"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "listPosts_request_2"}}, "where": "/posts/:id"}], "screens": {"/posts": "The main screen displaying a scrollable list of blog post titles and authors, with a 'Read Post' button for each.", "/posts/:id": "The screen displaying the full content of a selected blog post."}, "conditions": ["All available blog posts are displayed on the listing page.", "Each listed post includes its title, author, and a 'Read Post' button.", "Clicking the 'Read Post' button redirects the user to the full content of the selected post."], "dataSchemas": {"post": {"canBeCommented": false, "usePayment": false, "description": "En<PERSON><PERSON> representing a blog post.", "fields": {"id": {"type": "string", "required": true, "isUser": false, "description": "Unique identifier for the blog post."}, "title": {"type": "string", "required": true, "isUser": false, "description": "The title of the blog post."}, "author": {"type": "string", "required": true, "isUser": true, "description": "The author of the blog post."}, "content": {"type": "string", "required": true, "isUser": false, "description": "The full content of the blog post."}}}}, "requests": [{"requestId": "listPosts_request_1", "isArray": true, "dataSchema": "post", "type": "Find"}, {"requestId": "listPosts_request_2", "isArray": false, "dataSchema": "post", "type": "Read", "params": {"id": "string"}}]}}