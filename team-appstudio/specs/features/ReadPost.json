{"feature": {"name": "ReadPost", "description": "Allows users to read the full content of a selected blog post after purchasing it with credits.", "userExperience": [{"who": "App", "if": null, "action": {"type": "load", "request": {"requestId": "readPosts_request_1"}}, "where": "/posts"}, {"who": "User", "if": null, "action": {"type": "click", "element": {"type": "list:item", "eventId": "post_entry"}}, "where": "/posts"}, {"who": "App", "if": null, "action": {"type": "navigate", "path": "/workflow/:id"}, "when": {"type": "click", "element": {"type": "list:item", "eventId": "post_entry"}}, "where": "/posts"}, {"who": "App", "action": {"type": "load", "request": {"requestId": "readWorkflow_request_2"}}, "where": "/workflow/:id"}, {"who": "App", "action": {"type": "navigate", "path": "/posts/:id"}, "where": "/workflow/:id"}, {"who": "App", "if": {"post": {"hasAccess": false}}, "action": {"type": "open", "modal": "/paywall"}, "where": "/posts/:id"}, {"who": "App", "if": {"post": {"hasAccess": true}}, "action": {"type": "load", "request": {"requestId": "readPosts_request_3"}}, "where": "/posts/:id"}, {"who": "User", "if": null, "action": {"type": "click", "element": {"type": "button", "eventId": "buy_post"}}, "where": "/paywall"}, {"who": "App", "if": null, "action": {"type": "send", "request": {"requestId": "buyPost_request_4", "notify": {"target": "me", "title": "Purchase Successful", "message": "You have successfully purchased this post. Enjoy reading!", "link": "/posts/:id"}}}, "when": {"type": "click", "element": {"type": "button", "eventId": "buy_post"}}, "where": "/paywall"}, {"who": "App", "if": null, "action": {"type": "close", "modal": "/paywall"}, "when": {"type": "send", "request": {"requestId": "buyPost_request_4"}}, "where": "/paywall"}, {"who": "App", "if": null, "action": {"type": "redirect", "path": "/posts/:id"}, "when": {"type": "close", "modal": "/paywall"}, "where": "/paywall"}], "screens": {"/posts": "The main screen displaying a list of blog posts. Presents summaries and indicates if payment is required to read the full content.", "/workflow/:id": "The screen that checks the generation of the post.", "/posts/:id": "The screen that displays the full content of a selected blog post, with an option to purchase if not already owned.", "/paywall": "A modal screen prompting the user to purchase access to the selected blog post using credits."}, "conditions": ["User has sufficient credits to purchase the post.", "Post content is displayed only after successful purchase or if already owned.", "Posts on the list screen show a summary or title and indicate if payment is required."], "dataSchemas": {"post": {"canBeCommented": false, "usePayment": true, "description": "Entity for detailed information about a blog post.", "fields": {"id": {"type": "string", "required": true, "description": "Id of the post."}, "title": {"type": "string", "required": true, "description": "Title of the post."}, "content": {"type": "string", "required": false, "description": "Full content of the post."}, "workflowId": {"type": "string", "required": false, "description": "Workflow Id of the post"}}}}, "requests": [{"requestId": "readPosts_request_1", "isArray": true, "dataSchema": "post", "type": "Find"}, {"requestId": "readWorkflow_request_2", "dataSchema": "workflow", "type": "Read", "params": {"id": "string"}}, {"requestId": "readPosts_request_3", "dataSchema": "post", "type": "Read", "params": {"id": "string"}}, {"requestId": "buyPost_request_4", "dataSchema": "post", "type": "Create", "body": {"id": "string", "objectId": "string", "objectType": "string"}, "notifyLink": "/posts/:id"}]}}