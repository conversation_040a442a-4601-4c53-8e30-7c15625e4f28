"""
Feature Analyzer Sub-Agent
==========================

Specialized agent for analyzing and extracting feature requirements from project specifications.

Responsibilities:
- Read and parse project specifications
- Extract features array and configuration
- Identify payment features and credit usage
- Build configuration objects for downstream agents
- Validate feature requirements and dependencies
"""
from .agent import ai_model_selector

__all__ = ['ai_model_selector']
