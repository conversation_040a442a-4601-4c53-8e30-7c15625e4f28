"""
AI Model Selector Agent - AI Model and Provider Selection
=========================================================
"""

from google.adk.agents import LlmAgent
from .prompt import get_instruction
from .tools import get_ai_model_selector_tools

# Create AI Model Selector Agent as sub-agent

ai_model_selector = LlmAgent(
    name="ai_model_selector",
    model="gemini-2.5-flash",
    description="Selects optimal AI models and providers for workflow tasks",
    instruction=get_instruction(),
    tools=get_ai_model_selector_tools()
)
