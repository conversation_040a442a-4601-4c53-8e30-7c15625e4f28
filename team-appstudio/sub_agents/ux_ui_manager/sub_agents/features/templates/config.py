feature_config = {
    "fields": {
        
    },
    "audio": {
        "dataSchemas": {
            "id": "A unique identifier for the resource",
            "userId": "A required unique user identifier associated with the resource",
            "publicId": "A required public identifier for the resource in cloud storage. Generated by the server.",
            "url": "A required URL where the resource is hosted. Generated by the server.",
            "json": "Optional metadata in JSON format associated with the resource",
            "type": "The media type of the resource, defaults to audio",
            "workflowId": "An optional workflow identifier for the resource",
            "topic": "Optional, a topic or prompt related to the resource.",
            "name": "The name of the audio. Generated by the server.",
            "transcription": "The transcribed text of the audio. Generated by the server.",
            "createdAt": "Timestamp indicating when the resource was created",
            "updatedAt": "Timestamp automatically updated when the resource is modified",
        },
    },
    "chat": {
        "dataSchemas": {
            "chatConversation": {
                "description": "An entity representing a chat conversation between a user and an assistant",
                "fields": {
                    "id": "Unique identifier for the conversation",
                    "userId": "Required user identifier associated with the conversation",
                    "title": "Optional title for the conversation",
                    "systemPrompt": "Required system message that sets the assistant's behavior",
                    "status": "Optional status of the conversation: active, archived, or deleted",
                    "messages": "Optional list of messages in the conversation",
                },
            },
            "chatMessage": {
                "description": "An entity representing a message within a chat conversation",
                "fields": {
                    "id": "Unique identifier for the message",
                    "userId": "Required user identifier for the sender",
                    "role": "Required sender role: user, assistant, system, or model. Will be set on the server side.",
                    "content": "Required message content",
                    "metadata": "Optional metadata (e.g., tokens, model)",
                    "conversationId": "Required conversation ID the message belongs to",
                },
            },
        },
    },
    "media": {
        "dataSchemas": {
            "id": "A unique identifier for the resource",
            "userId": "A required unique user identifier associated with the resource",
            "publicId": "A required public identifier for the resource in cloud storage",
            "url": "A required URL where the resource is hosted",
            "thumbnailUrl": "A URL where the thumbnail resource is hosted",
            "json": "Optional metadata in JSON format associated with the resource",
            "type": "The media type of the resource, defaults to image or video",
            "workflowId": "An optional workflow identifier for the resource",
            "topic": "A topic or prompt related to the resource",
            "createdAt": "Timestamp indicating when the resource was created",
            "updatedAt": "Timestamp automatically updated when the resource is modified",
        },
    },
}
