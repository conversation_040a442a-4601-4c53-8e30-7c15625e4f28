import json

def get_required_fields_for_payment_entity(payment):
    if payment.get('paymentType') == "credit" or not payment.get('paymentEntity'):
        return ""
    return f"""
    For the "{payment['paymentEntity']}" entity, the following fields are required:
    - `id`: Identifier of the resource.
    - `name`: Descriptive name for the resource to be displayed.
    - `price`: The amount the resource can be purchased for.
  """

def get_access_field_warning(payment):
    payment_entity = payment.get('paymentEntity', 'none')
    return f'Do not include any access-related fields within the "{payment_entity}" entity to manage access control.'

def get_media_schemas(feature_config, config):
    if not config.get('isMedia'):
        return ""
    return f"""
  - **Specific Fields for "image" or "video" Entities**:
    If the entity is either "image" or "video", use only the following predefined fields:
    ```json
    {json.dumps(feature_config['media']['dataSchemas'], indent=2)}
    ```
  """

def get_audio_schemas(feature_config):
    return f"""
  - **Specific Fields for "audio" Entity**:
    If the entity is "audio", use only the following predefined fields:
    ```json
    {json.dumps(feature_config['audio']['dataSchemas'], indent=2)}
    ```
"""

def get_chat_schemas(feature_config, config):
    if not config.get('isChat'):
        return ""
    return f"""
  - **Additional Fields for Chat-Related Entities**:
    Include the following fields for entities involved in chat functionality:
    ```json
    {json.dumps(feature_config['chat']['dataSchemas'], indent=2)}
    ```
  """

def get_specific_requirements_for_payment_entity(payment):
    if payment.get('paymentType') == "credit" or not payment.get('paymentEntity'):
        return ""
    return f"""
    - **Specific Requirements for "{payment['paymentEntity']}" Entity**:
      Ensure that the "{payment['paymentEntity']}" entity includes the following required fields: `id`, `name`, and `price`. The `name` field should hold the descriptive name of the item. Remove any other field that serves the same purpose as `name`.
  """

def data_schema_prompt(feature_config, payment, config):
    return f"""
Define the structure and purpose of data entities used within the feature.

Each schema should include the following top-level properties:

- **Entity Name**:
  A single, descriptive, lowercase noun that uniquely identifies a distinct object or concept within the feature. This ensures clarity and consistency across the data schema.

- **Description**:
  A concise explanation of what the entity represents.

- **canBeCommented**:
  Set to `true` if the entity can be commented on by a user.

- **usePayment**:
  Set to `true` if the entity involves a payment-related condition. This may include access control, purchase verification, subscription status, or credit usage.

- **Fields**:
  Defines the structure of the entity. This is an object where each key is a `fieldName`.

  - **Requirements for Fields**:
    {get_required_fields_for_payment_entity(payment)}

  - **Details for each `fieldName`**:
    For each field, specify the following properties:

    - `type`: The data type of the field (e.g., "string", "number", "boolean").
    - `required`: Set to `true` **only if** the field is mandatory for the creation request.
    - `isUser`: Set to `true` **only if** the field represents user information.
    - `description`: A clear description of the field's purpose.

    - **Restrictions for Field Names**:
      - **Dependencies between Entities**:
        When an entity's creation depends on another entity, use the following standard field names for the identifier:
        - `objectId`: The identifier of the entity that is needed to create the current object.
        - `objectType`: The entity name of the entity that is needed to create the current object.
        - **Note**: `objectId` and `objectType` clarify the dependency.
      - Avoid using specific identifiers like "postId" or "userId". Always use `objectId` and `objectType` for inter-entity dependencies.
      - For storing an image within an entity, the field name must be exactly `image`.
      - {get_access_field_warning(payment)}

  - **General Requirements for Fields**:
    - Reuse existing `dataSchema` definitions whenever possible to maintain consistency and avoid redundancy.
    - Each field within an entity should have a unique identifier (e.g., as a key in the `Fields` object).

  {get_media_schemas(feature_config, config)}
  {get_audio_schemas(feature_config)}
  {get_chat_schemas(feature_config, config)}

    - **Workflow Identifier for Generated Resources**:
      When a resource or entity needs to be generated, include the following field:
      ```json
      "workflowId": {{
        "type": "string",
        "required": false,
        "description": "Workflow identifier for the [resource]."
      }}
      ```
    {get_specific_requirements_for_payment_entity(payment)}
    - **Redundancy Check**:
      Remove any redundant fields that have the same purpose as the `name` field (e.g., if both "title" and "name" exist and contain the same information, keep only `name`). Include only the minimally necessary fields to avoid data duplication.
"""
