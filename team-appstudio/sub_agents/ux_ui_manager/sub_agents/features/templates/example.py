example_output = {
    "feature": {
        "name": "ReadArticles",
        "description": "Immediate and unrestricted access to all published articles without the need for user registration or login.",
        "userExperience": [
            {
                "who": "App",
                "if": None,  # Explicitly set to None for list view
                "action": {
                    "type": "load",
                    "request": {
                        "requestId": "readArticles_request_1",
                    },
                },
                "where": "/articles",
            },
            {
                "who": "User",
                "if": None,  # Explicitly set to None for list view
                "action": {
                    "type": "select",
                    "element": {
                        "type": "list:item",
                        "eventId": "article_entry",
                    },
                },
                "where": "/articles",
            },
            {
                "who": "App",
                "if": None,  # Explicitly set to None for list view
                "action": {
                    "type": "navigate",
                    "path": "/workflow/:id",
                },
                "when": {
                    "type": "click",
                    "element": {
                        "type": "list:item",
                        "eventId": "article_entry",
                    },
                },
                "where": "/articles",
            },
            {
                "who": "App",
                "action": {
                    "type": "load",
                    "request": {
                        "requestId": "readWorkflow_request_2",
                    },
                },
                "where": "/workflow/:id",
            },
            {
                "who": "App",
                "action": {
                    "type": "navigate",
                    "path": "/articles/:id",
                },
                "where": "/workflow/:id",
            },
            {
                "who": "App",
                "if": {
                    "article": {
                        "hasAccess": False,
                    },
                },
                "action": {
                    "type": "open",
                    "modal": "/paywall",
                },
                "where": "/articles/:id",
            },
            {
                "who": "App",
                "if": {
                    "article": {
                        "hasAccess": True,
                    },
                },
                "action": {
                    "type": "load",
                    "request": {
                        "requestId": "readArticles_request_3",
                    },
                },
                "where": "/articles/:id",
            },
        ],
        "screens": {
            "/articles": "The main screen with a list of articles. Presents the content in a minimalistic design with easy navigation to aid instant content access.",
            "/workflow/:id": "The screen that check the generation of the article.",
            "/articles/:id": "The screen that display the article",
        },
        "conditions": [
            "Content is available without the need of user sign-in.",
            "Articles are displayed with a summary on the list screen.",
            "Full articles can be viewed by selecting their summaries.",
        ],
        "dataSchemas": {
            "article": {
                "description": "Entity for detailed information about an article.",
                "fields": {
                    "id": {
                        "type": "string",
                        "required": True,
                        "description": "Id of the article.",
                    },
                    "content": {
                        "type": "string",
                        "required": True,
                        "description": "Full content of the article.",
                    },
                    "author": {
                        "type": "string",
                        "required": True,
                        "isUser": True,
                        "description": "Name of the article's author.",
                    },
                    "workflowId": {
                        "type": "string",
                        "required": False,
                        "description": "Workflow Id of the article",
                    },
                },
            },
        },
        "requests": [
            {
                "requestId": "readArticles_request_1",
                "isArray": True,
                "dataSchema": "article",
                "type": "Find",
            },
            {
                "requestId": "readWorkflow_request_2",
                "dataSchema": "workflow",
                "type": "Read",
                "params": {"id": "string"},
            },
            {
                "requestId": "readArticles_request_3",
                "dataSchema": "article",
                "type": "Read",
                "params": {"id": "string"},
            },
        ],
    },
}
