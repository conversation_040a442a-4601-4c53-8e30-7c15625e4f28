def guidelines_prompt():
    return """
- **Prioritize Core Experience**: Focus on the **Main Screen** and **KPIs** for optimal user experience.
- Use **KPIs** to ensure the success of the feature.
- Ensure that the user experience flow starts with the user entering the feature and ends with the user completing their task and returning to the main screen (or equivalent ending point).
- **Minimize Screen Count**: Consolidate actions and interactions into as few screens as necessary.
- Ensure that each dataSchema, such as "comment", has its own independent CRUD requests and does not depend on other entities' requests.
- Ensure paths are short and descriptive, avoiding redundant segments.
- Avoid using special characters and spaces (e.g., &) in names.

- **Transcription**:
  - Use entity "audio" for transcription.

- **Payment**:
  - Do take into consideration if feature has **Payment Restriction**. If yes, include the payment restriction.
  - Do not include path for screen "subscriptions" or "buying credits" as it is already handled by the modal "/paywall". The modal Paywall is an existing modal. Thus, no need to create. The paywall will handle the redirection.
  - If **paymentType** is present, any **paymentEntity** in a listing should display only a single information field to represent the entity on the page, ensuring unauthorized access is prevented.
  - For AI-Generated Content: When generating content like images or videos using AI, the request should update the parent entity rather than creating a separate entity for the generated content.
- **Screen Consistency**: All paths listed in the userExperience flow must be present in the "screens" section.
- Set **useAuthStore** to null in all, if **paymentType** is not "subscription".

- **Access Controls**:
  - For list views (paths like "/posts", "/articles", etc.), ALWAYS set "if: null" explicitly in ALL user experience objects.
  - NEVER use "hasAccess" checks on list views - these paths should NEVER have any access conditions.
  - Access checks (hasAccess) must ONLY be applied to individual item views (paths with IDs like "/posts/:id").
  - This rule applies to ALL user experiences on list view paths, regardless of the action type.

- **Workflow**:
  - Workflow handles any creation, editing or upscaling of a resource.
  - When navigating on a resource page, first go through "/workflow:id". It checks if the resource generation is fully completed before displaying the item.
  - **/workflow/:id**:
    - For any generation of resource navigate to "/workflow/:id" path.
    - A path to navigate on a page to display/check the workflow process.
    - It handles the generation process of a resource.
    - On success, it will be redirected to the resource page (e.g "/article/:id").
    - The only request that will be loaded in page "/workflow/:id" is "read". Do not add **useWorkflow**.
  - Create a single request for a workflow. That request handles all the steps from generating, uploading, editing or storing an entity.
  - If an item is clicked from a list, it should be redirected to "/workflow/:id" to check the generation process.

- **Navigation**:
  - **Workflow**:
    - If **when** **type** is "list:item", path navigate to "/workflow/:id".

- **Transcription**:
  - Use entity "audio" for transcription.
  - When implementing audio transcription functionality, always use the paths "/audios" and "/audios/:id".
  - "/audios" should be used for displaying the list of transcription audios.
  - "/audios/:id" should be used for displaying an individual transcription.
  - "/workflow/:id" should be used for displaying the workflow process.
  - Other features may use different paths, but any functionality related to audio transcription must use these specific paths.
  - The "/home" handles the audio recording and upload.
  - **generationType**: ["audio", "text"]

- **Agent/Chatbot**:
  - The path navigates to "/chatbot". This path handles displaying chat conversations, creating user message, and showing all messages in a conversation.
  - No workflow required when generating an agent/chatbot.
"""
