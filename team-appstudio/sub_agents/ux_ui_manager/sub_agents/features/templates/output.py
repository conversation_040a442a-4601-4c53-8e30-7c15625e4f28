def output_format():
    return f"""
{{
  "feature": {{
    "name": "[Feature Name]",
    "description": "[feature['description']]",
    "userExperience": [
      {{
        "who": "App",
        "if": {{
          "[identifierName]": {{
            "[identifierFunction]": false
          }}
        }},
        "action": {{
          "type": "[Action Type]",
          "request": {{
            "requestId": "[unique identifier]",
            "notify": {{
              "target": "[me or owner]",
              "title": "[title on action]",
              "message": "[message on action]",
              "link": "[redirection path]"
            }}
          }}
        }},
        "where": "[Screen Path]"
      }},
      {{
        "who": "[User/App]",
        "action": {{
          "type": "[Action Type]",
          "path": "[Path]",
          "modal": "[modal]"
        }},
        "when": {{
          "type": "[Action Type]",
          "request": {{
            "requestId": "[unique identifier]",
            "notify": {{
              "target": "[me or owner]",
              "title": "[title on action]",
              "message": "[message on action]",
              "link": "[redirection path]"
            }}
          }}
        }},
        "where": "[Screen Path]"
      }},
      {{
        "who": "App",
        "action": {{
          "type": "[Action Type]",
        }},
        "when": {{
          "type": "[Action Type]",
          "path": "[Path]",
          "modal": "[modal]"
        }},
        "where": "[Screen Path]"
      }}
    ],
    "screens": {{
      "[The URL path associated with the screen (key)]": "[Describe the purpose and contribution to the user experience (value)]"
    }},
    "conditions": [
      "[Condition 1]",
      "[Condition 2]"
    ],
    "dataSchemas": {{
      "[entity Name]": {{
        "canBeCommented": "[true/false]",
        "usePayment": "[true/false]",
        "description": "[Description of the schema and its purpose]",
        "fields": {{
          "[Field Name]": {{
            "type": "[Field Type]",
            "required": "[true/false]",
            "isUser": "[true/false]",
            "description": "[Description of the field purpose]"
          }}
        }}
      }}
    }},
    "requests": [
      {{
        "requestId": "[unique identifier]",
        "useWorkflow": {{
          "tasks": [
            {{
              "name": "[function name]",
              "uiDescription": "[A text description]",
              "dependencies": ["list of dependencies"],
              "next": [
                {{
                  "name": "[function name]",
                  "uiDescription": "[A text description]",
                  "dependencies": ["list of dependencies"],
                  "connect": ["List of function name"]
                }}
              ]
            }}
          ]
        }},
        "useAi": {{
          "generationType": ["[type of the output: text, image, video, audio]"],
          "role": "[role of the ai]",
          "objective": "[objective of the ai]"
        }},
        "dataSchema": "[Entity Name from dataSchemas]",
        "type": "[Read/Find/Create/Update/Delete]",
        "params": {{}},
        "body": {{}},
        "notifyLink": "[path to notify]"
      }}
    ]
  }}
}}
"""
