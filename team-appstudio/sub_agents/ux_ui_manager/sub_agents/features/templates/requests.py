def requests_prompt(payment):
    payment_requirement = ""
    if payment.get('paymentType') != "credit":
        payment_requirement = "For **payment**, you must add fields: id, name, and price, as they will be required for resource generation."
    
    return f"""
A list of CRUD operations required for the feature, focusing on "load" and "send" actions. Each request includes:
  - **requestId**: Unique identifier for the request.
  - **isArray**: <PERSON><PERSON><PERSON> indicating if the response returns a list of items.
  - **useWorkflow**: Specifies if a task follows a sequential workflow for completion.
    - **tasks**: A list of tasks to achieve the main objective.
      - **name**: The function name initiating the task.
        - Following formats for **name** if:
          - Uploading and Editing an entity: "[entity]WorkflowService.edit[entity]" (e.g., imageWorkflowService.editImage).
          - Upscaling an entity: "[entity]WorkflowService.upscale[entity]" (e.g., imageWorkflowService.upscaleImage).
          - Generation: "[entity]WorkflowService.generate[entity]".
          - Transcribe: "[entity]WorkflowService.transcribe[entity]".
          - Store: "[entity]WorkflowService.store[entity]".
      - **uiDescription**: A user-facing message describing the task's purpose or status in the UI.
      - **dependencies**: A list of function names that must be completed before this task can execute. If within a 'next' object, it should reference the parent task's function name.
      - **connect**: A list of function names whose data is necessary to complete this task. If within a 'next' object, it should reference the parent function name.
      - **next**: The subsequent task(s) to be executed after the current one.

    - **Requirements**:
      - Create a single "create" request for resource generation.
      - Ensure **entity** matches an entity defined in **dataSchemas**.
      - No upload step is needed, as it will be handled automatically.

  - **Steps for Scenarios Involving:
   - **"Image" or "Video" or "audio"**:
    - **To Generate and Upscale**: Workflow includes tasks to generate the entity using AI, then upscale and store it.
      1. Generate
      2. Upscale
      3. Store
    - **To Upload and Upscale**: Workflow manages entity upload, including tasks to upscale and store.
      1. Upscale
      2. Store
    - **To Upload and Edit**: Workflow manages entity upload, including tasks to edit and store.
      1. Edit
      2. Store
    - **To Transcribe Audio**: Workflow manages entity upload, including tasks to transcribe and store.
      1. Transcribe
      2. Store

  -**Others**:
    - **To Generate**: Workflow includes tasks to generate the entity using AI and store.
      1. generate
      2. Store

  -**Note**: Each step depends on the successful completion of the previous one. (e.g. Upscale depends on Generate, Store depends on Upscale, etc.)

  - **useAi**:
    - **generationType**: The type of AI-generated or AI-enhanced/upscaling output: ["text"], ["image"], ["video"], ["audio"], "["text", "image"]", "["text", "video"]", or "["text", "audio"]".
    - Include the following only if **generationType** includes "text":
      - **role**: Describes the AI's role in generating the response.
      - **objective**: The specific goal the AI fulfills in this request.
    - **Requirements**:
      - Ensure **generationType** includes all applicable types if the resource generates multiple types (e.g., text and image).
      - Ensure **generationType** is included even for upscaling operations.

  - **dataSchema**: Reference to an entity defined in dataSchemas.
    - References existing schema names only.
    - No field redefinition allowed here.

  - **type**: The type of operation: Find, Read, Create, Update, Delete.
    - **Criteria**:
      - Use "Find" for:
        - Searching or filtering resources.
        - Retrieving a list of items.
      - Use "Read" only for:
        - Retrieving a single resource by its "Id".
      - Use "Create" for:
        - Creating a resource.
        - Upscaling a resource.
        - Editing an uploaded resource.
      - Use "Update" for:
        - Modifying existing resources.
        - Removing resources by "Id".

  - **params**: (Optional) Input parameters for the request.
    - **fieldName**: Field name from **dataSchemas fields**.
    - **value**: Data type.
      - **Criteria**:
        - Only use params for:
          - "Read" operations: Use the exact field name from the dataSchema for the identifier (e.g., "id" or "conversationId").
          - "Delete" operations: Use the exact field name from the dataSchema for the identifier.
          - "Update" operations: Use the exact field name from the dataSchema for the identifier.
      - **Never use params for**:
        - "Find" operations (use body instead).
        - "Create" operations.
      - **Requirements**:
        - ALL body fields MUST exactly match fields defined in the referenced **dataSchemas**.

  - **body**: (Optional) Data payload for the request.
    - **fieldName**: Field name from **dataSchemas fields**, except for entity identifiers needed for object creation.
      - **Requirements**:
        - For "Create" operations where another entity identifier is needed:
          - Replace the existing identifier with:
            - **objectId**: The identifier of the entity needed to create the object.
            - **objectType**: The entity name needed to create the object.
        - If **useAi** is true and useWorkflow is true, provide a single field for resource creation: "topic".
        - If a file (image or video) is being "uploaded", include the fieldName: "file", even if not in **dataSchema**.

    - **value**: Data type.
      - **Criteria**:
        - Use body for:
          - "Create" operations (resource data). Include ALL fields marked as "required": true in **dataSchemas** to create a resource.
          - If a file (image or video) is being "uploaded", include the fieldName: "file", even if not in **dataSchema**.
          - "Update" operations (modified fields).
          - "Find" operations (search/filter criteria).

  - **notifyLink**: (Optional) If the request in **userExperience** has a notify, this will be the same **link** path in **notify**.
    - **Criteria**: Include only if an action in **userExperience** has a request that has a **notify**.
    - **Requirements**: The **notifyLink** should match an existing **path**.

    - **Requirements**:
      - ALL body fields MUST exactly match fields defined in the referenced **dataSchemas** and include all fields with "isRequired": true, except for fields with "isUser": true.
      - For "Create" operations, body MUST include ALL fields marked as "required": true in **dataSchemas**, except for fields with "isUser": true, unless a field identifier is needed for object creation. In such cases:
        - Replace the existing identifier with:
          - **objectId**: The identifier of the entity needed to create the object.
          - **objectType**: The entity name needed to create the object.
      - Do not include fields with "isUser": true.
      - **workflow**:
        - If **generationType** is "image", the body should only include the "image" field.
        - If **generationType** is "audio", the body should only include the "file" field.
        - If **generationType** is "text", the body should only include the "topic" field.
        - If **generationType** is "["text", "image"]", the body should only include the "topic" field.
        - If **generationType** is "["text", "audio"]", the body should only include the "file" field.

    - Example: If dataSchema "post" has fields ["id", "title", "content", "author"]
      - Valid body: {{"title": "string", "content": "string"}}
      - Invalid body: {{"search": "string"}} (search is not in dataSchema)

**Requirements**:
- Ensure each dataSchema (e.g., "comment") has its own set of independent CRUD requests. Avoid creating dependent requests.
- If **useAi**, create a single request to handle entity creation.
- Each request in the requests section must correspond to an explicit action in the userExperience flow. No extraneous requests are allowed.
- If **useWorkflow** is true, create only one request for entity generation. Do not create a separate request for "image"; generation will be handled within the workflow. Include **useAi**.
  - {payment_requirement}
  - Do not display the content of an entity without verifying access.
  - Access should only be verified when the user attempts to view a single resource page.
  - Ensure **body** includes the fieldName "file" if an entity is being uploaded.
"""
