def screens_prompt():
    return """
Defining the screens of the feature and their roles in achieving the KPIs.
- **Path**: If **Payment Restriction Type** is "subscription" or "credit", access the screen modal with the path "/paywall" only.
- All paths mentioned in the User Experience Flow should be included in this section, ensuring that every path from the user experience is shown here.

- **Requirements**:
 - Validate that every screen and path involved in the user experience is explicitly defined in the screens section.
 - If a path displays a list of "paymentEntity", ensure it only shows the name of that resource (the user does not yet have access).
 - Redirect to the screen modal "/paywall" when the flow involves a subscription or requires purchasing a resource.
 - Use the path format `[entity name]s/:id` for reading an **entity** (applies to both **userExperience** and **screens**).
"""