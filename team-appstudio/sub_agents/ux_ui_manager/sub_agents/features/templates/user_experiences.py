def user_experience_prompt():
    return """
  For each user experience:
   - **Who**: The actor performing the action (User/App).
   - **If Conditions**:
     - Use **if** only when:
       1. **who** is "App"
       2. The action depends on a specific condition for buying a subscription, making a one-time payment to access a resource or buying credits.

      Only include, if **paymentType** is "subscription" only:
       - **identifierName**: Refers to an existing store in the application that provides access to stored data.
         - Use "useAuthStore" to retrieve user account details, including subscription information.
       - **identifierFunction**: A function within the specified **identifierName**.
         - Use the "hasSubscription" function to check if a user has an active subscription. It returns a boolean value.

     - if **Payment Restriction Type** === "credit" OR **Payment Restriction Type** === "one-time-payment" OR **Payment Restriction Type** === "cart":
       - **identifierName**: Refers to resource/entity that needs to be bought to have access too.
       - **identifierFunction**:
         - Use the "hasAccess" function to verify whether a user has access to a resource, and apply **hasAccess** false, only when interacting with a single entity based on its ID—specifically, on pages where the **where** path includes an identifier (e.g., "/posts/:id").
         - IMPORTANT: NEVER use "hasAccess" on list views or paths that display multiple items (e.g., "/posts", "/articles", etc.). Access checks must only be applied to individual items, not to list views.
         The function returns a boolean value indicating whether the user has access, default should be false.

   - **Action**: The type of action and the element involved.
     - **type**: Type of action depends on **who** is performing it:
       👤 If **who** is "User":
       - For forms, use only:
         - "fill"
         - "select"
         - "click"
         - "upload" (to upload file)

       - For other components, use only:
         - "click"
         - "type"

       ⚙️ If **who** is "App":
       - For modals, use only:
         - "open"
         - "close"

       - For pages, use only:
         - "navigate"
         - "redirect"

       - For forms, use only:
         - "validate"
         - "send"

       - For requests, use only:
         - "load"
         - "send"

     Each action must include exactly one of the following: **path**, **modal**, **request**, or **element**.
     - **path** or **modal**: An **action** can have either "path" or "modal" which indicates which screen page or screen modal to display.
       When to use:
       - **path**: Use "path" to specify the navigation route to a screen page. Do not use for "/paywall".
         - If a screen displays a list of an entity, use "/[entity]s" as path (e.g "/articles") with an "s".
         - IMPORTANT: For list paths (e.g., "/posts", "/articles"), ALWAYS set "if: null" explicitly in the user experience object. NEVER use "hasAccess" checks on list views.

       - **modal**: Use "modal" to display a pop-up interface for user interactions, screen modal.
         - When to use "modal":
           - When prompting a user to subscribe or buy a resource, navigate to the "/paywall" modal.
         - **Requirements**:
           - Use **type** open or close when using "modal" for pop-up screens.
           - **path** format for reading an **entity** should be "`[entity name]s/:id`". (e.g. "/posts/:id") in **userExperience** and **screens**.
         - **Requirements**:
           - If the type of action is load or send, the object will have a request property. (Load indicates when the page is loaded and send for on user action)
           **Subscription Validation**:
           - If the app should make a request to check the user's subscription status using the Account entity (e.g., "request_subscription_check").
         - **Conditional Navigation**:
           - If the subscription check indicates that the user is not subscribed, the app should navigate to the "/plan" screen before allowing access to any premium content or features.
           - If **create** request, the app should navigate to the "/workflow/:id".
           - If **action** **type** "click" on **element** **type** "list:item", set the next **who** is App to "navigate" to path "/workflow/:id".

     Include the **request** field only when **type** is "load" or "send."
     - **Request**:
       - **requestId**: Unique identifier for the request. The format name of the identifier should start with the name of the feature (e.g "readArticles_request_1").
       - **notify**: Notify every action fired by the app or other users that needs to be sent to inform the **target**. If no notification is needed, set this field to null.
         - **target**: The target of the notification either "me" or "owner".
           - **me**: The current logged-in user who receives notifications about their personal actions and account activities.
           - **owner**: The individual or entity that created or manages a resource, receiving notifications about interactions or changes related to that resource.
         - **title**: A concise headline summarizing the notification when action is successfully completed.
         - **message**: A detailed description providing context or additional information about the notification
         - **link**: A **url** that directs the user to a specific resource or page when they interact with the notification.
           - **Note**: **url** should match existing **path**.

     If the type of action is not load or send, we will have an element property.
     - **Element**:
       - **Type**: The type of the element being interacted with.
       - **eventId**: unique ID for the element being interacted with.

   - **Where**: The screen where the action happens.

   - **When**:
     - **User**: Describe the trigger (e.g., "User clicks the publish button after reviewing the post").
       1. For every "User" action, there should be a corresponding "App" action that mirrors it, unless it involves navigation.
     - **App**:
       1. Mirror the triggering User action (type, element type, ID, request, notify etc..).
       - **Requirements**: The **when** should be the exact reflection of the **action**.

   #### **Criteria**:
   1. If the task involves:
     - **Generating and Upscaling**:
       - In **userExperience**: Include single button to generate_upscale entity.
       - In **requests**: use a single request, as both tasks should be handled within the same workflow.
   2. If an item is clicked from a list, it should be redirected to "/workflow/:id" to check the generation process.
   3. For list views (paths ending with plural entity names like "/posts", "/articles"):
     - NEVER include any access conditions or "hasAccess" checks in the "if" field.
     - ALL user experiences on list views must have "if" set to null.
     - Access checks are ONLY for individual item views (paths with IDs like "/posts/:id").

   #### **Requirements**:
   1. Every user action that involves a change (e.g., saving edits) must include the application's response (e.g., success message, navigation) as part of the flow.
   2. Ensure the flow includes a step where the user navigates back to the main screen or completes the cycle.
   3. Start with the user entering the feature and end with one of the following:
      - Returning to the starting screen (or dashboard).
      - Completing their task and receiving confirmation.
   4. Add more detailed steps to cover:
      - All user interactions.
      - App responses.

   5. Ensure that actions related to each dataSchema, such as loading comments, have their own dedicated requests and are not dependent on other entities' requests.

   6. **Subscription Validation**:
      - If a feature requires subscription access, the system must first check the user's subscription status using the **Account** entity.
      - The app should make a request to retrieve the subscription field from the **Account** entity before granting access.
   7. If **action** that requires data to be fetched , that action should be paired with a corresponding request under **requests**.
   8. Ensure for **user experience** having **type** is navigate, add a **when** information.
   9. **action** has only two fields, that is, **type** and **element or path or modal or request**.
   10. When a resource is created and displayed on the same screen, no additional request should be made to retrieve or find it. The creation request must return the full entity, and the UI should use this response directly without a separate fetch request.
   11. Minimize the number of paths—if all features can be managed within a single path, keep it that way. Refer to the **existing Routes** to reuse available paths and avoid creating redundant ones.
   12. When navigating on a resource page, first go through "/workflow:id". It checks if the resource generation is fully completed before displaying the item.
   13. Do not add an upload button. The upload will be triggered by an event with eventId: "upload_media", using the action type "click" on a component element. No separate request is required for this.
"""