"""
Features Manager Tools
=====================
"""
import os
import json

from typing import Dict, Optional
from google.adk.tools.function_tool import FunctionTool
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset
from google.adk.tools.mcp_tool.mcp_session_manager import StdioServerParameters

def generate_features_config(specs_file_path: str, output_file_path: str):
    # Read the specifications JSON file
    with open(specs_file_path, 'r') as file:
        project_specs = json.load(file)

    # Extract the features from the projectSpecs
    features = project_specs.get("features", [])

    # Return early if no features are found
    if not features:
        raise ValueError("No features found in the specifications.")

    # Find the payment feature
    payment_feature = next(
        (feature for feature in features if 'paymentEntity' in feature),
        {"paymentEntity": "", "paymentType": ""}
    )

    # Check if any feature has creditType set to "usage"
    is_credit_usage = any(feature.get("creditType") == "usage" for feature in features)

    # Prepare the configuration
    config = {
        "isChat": "chatbot" in project_specs.get("interactionType", []),
        "isMedia": "media" in project_specs.get("interactionType", []),
        "isCreditUsage": is_credit_usage,
    }

    # Create the features config as a Python dictionary
    features_config = f"""
features_config = {{
  "status": "success",
  "payment": {{
    "paymentEntity": "{payment_feature['paymentEntity'] or ''}",
    "paymentType": "{payment_feature['paymentType'] or ''}"
  }},
  "config": {{
    "isChat": {str(config['isChat']).capitalize()},
    "isMedia": {str(config['isMedia']).capitalize()},
    "isCreditUsage": {str(config['isCreditUsage']).capitalize()}
  }}
}}
"""

    # Ensure the output directory exists
    os.makedirs(os.path.dirname(output_file_path), exist_ok=True)

    # Write the features_config to the output file as a string
    with open(output_file_path, 'w') as output_file:
        output_file.write(features_config)

    return features_config

def remove_white_space(text):
    return text.strip()

def get_paths() -> Dict[str, str]:
    """Return absolute paths for various output and config files related to a feature."""
    base_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..", "..", ".."))
    return {
        "root": base_dir,
        "output_path": os.path.join(base_dir, "specs"),
        "specifications_path": os.path.join(base_dir, "specs", "specifications.json"),
        "features_config_path": os.path.join(
            base_dir, "sub_agents", "ux_ui_manager", "sub_agents", "features", "templates", "features_config.py"
        ),
        "routing_app_path": os.path.join(base_dir, "specs", "routingApp.json"),
        "feature_output_folder": os.path.join(base_dir, "specs", "features"),
        "modification_report_path": os.path.join(base_dir, "specs", "modification_report.json")
    }

def file_system_toolset() -> MCPToolset:
    """File system toolset for reading and writing grant sections"""
    # Get the team grant directory dynamically
    current_dir = os.path.dirname(os.path.abspath(__file__))  # Absolute path to current file
    team_grant_dir = os.path.abspath(os.path.join(current_dir, "..", "..", "..", "..", ".."))  # Resolve ".." to absolute path

    return MCPToolset(
        connection_params=StdioServerParameters(
            command='npx',
            args=[
                "-y",
                "@modelcontextprotocol/server-filesystem",
                team_grant_dir,
            ],

        ),
        tool_filter=["read_text_file", "write_file", "create_directory", "list_directory"]
    )

def get_feature_tools() -> list:
    return [
        file_system_toolset(),
        FunctionTool(func=get_paths)
        ]

__all__ = [
    "generate_features_config",
    "get_feature_tools"
]
