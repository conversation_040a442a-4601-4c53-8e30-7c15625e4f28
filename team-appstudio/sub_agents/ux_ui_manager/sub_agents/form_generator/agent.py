"""
Feature Analyzer Agent - Feature Requirements Extraction
=================================================================
"""

from google.adk.agents import LlmAgent
from .prompt import get_instruction
from .tools import get_form_generator_tools

form_generator = LlmAgent(
    name="form_generator",
    model="gemini-2.5-flash",
    description="Expert in generating form specifications for all screens that contain input elements by reading existing screen spec files (pages and modals) and writing one JSON per form to the forms output folder.",
    instruction=get_instruction(),
    tools=get_form_generator_tools()
)
root_agent = form_generator
