"""
Form Generator Tools
====================
"""
import os
import json
import shutil
from typing import List, Union, Dict, Any
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters
from google.adk.tools.base_tool import BaseTool
from google.adk.tools.function_tool import FunctionTool

# ============================================================================
# CORE UTILITIES - File Operations
# ============================================================================

def list_files(directory: str) -> List[str]:
    """
    List all files in a directory (non-recursive).

    Args:
        directory: Path to directory to list

    Returns:
        List of filenames (not full paths) in the directory
    """
    if not os.path.isdir(directory):
        return []
    return [f for f in os.listdir(directory) if os.path.isfile(os.path.join(directory, f))]


def read_json(file_path: str) -> Any:
    """
    Read and parse a JSON file.

    Args:
        file_path: Path to JSON file

    Returns:
        Parsed JSON content (dict, list, etc.)
    """
    with open(file_path, "r", encoding="utf-8") as f:
        return json.load(f)


def write_json(file_path: str, data: Any) -> None:
    """
    Write data to a JSON file with proper formatting.

    Creates parent directories if they don't exist.

    Args:
        file_path: Path where JSON file should be written
        data: Data to serialize to JSON
    """
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(data, f, indent=2, ensure_ascii=False)


def read_text(file_path: str) -> str:
    """
    Read text file content.

    Args:
        file_path: Path to text file

    Returns:
        File content as string
    """
    with open(file_path, "r", encoding="utf-8") as f:
        return f.read()


def write_text(file_path: str, content: str) -> None:
    """
    Write text content to file.

    Creates parent directories if they don't exist.

    Args:
        file_path: Path where file should be written
        content: Text content to write
    """
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(content)


def move_file(src: str, dst: str) -> None:
    """
    Move file from source to destination.

    Creates destination directory if it doesn't exist.

    Args:
        src: Source file path
        dst: Destination file path
    """
    os.makedirs(os.path.dirname(dst), exist_ok=True)
    shutil.move(src, dst)


def delete_folder(directory: str) -> None:
    """
    Delete folder and recreate it empty.

    Args:
        directory: Directory path to delete and recreate
    """
    if os.path.isdir(directory):
        shutil.rmtree(directory)
    os.makedirs(directory, exist_ok=True)

# ===========================================================================
# CORE UTILITIES - Form Generation
# ===========================================================================

# =====================================================================
# Step 1: Gather every element from all screen spec files
# =====================================================================
def gather_elements_from_spec_files(pages_dir: str, modals_dir: str, forms_dir: str) -> List[Dict[str, Any]]:
    # Clear forms directory
    delete_folder(forms_dir)

    # List files
    page_file_names = list_files(pages_dir)
    modal_file_names = list_files(modals_dir)

    # Merge all spec files
    spec_file_names = page_file_names + modal_file_names
    element_processing_queue = []

    for spec_file_name in spec_file_names:
        folder_path = pages_dir if spec_file_name in page_file_names else modals_dir
        spec_file_path = os.path.join(folder_path, spec_file_name)
        screen_data = read_json(spec_file_path)

        # These functions need to be implemented or imported
        input_elements = find_elements_with_inputs(screen_data["screenSpecs"])
        form_request_definitions = find_elements_with_inputs_and_data_request(screen_data["screenSpecs"])
        # Map form request by name for instant lookup
        form_request_lookup = {req["formName"]: req for req in form_request_definitions}

        for item in input_elements:
            element_processing_queue.append({
                "elementDefinition": item["element"],
                "formRequestLookup": form_request_lookup,
                "allRequests": screen_data["screenSpecs"].get("requests", [])
            })

    return element_processing_queue

def find_elements_with_inputs(screen_specs: dict) -> list:
    """
    Find all elements inside layouts/groups that have input fields.
    Returns a list of dicts with:
    {
        'screen': <screen_name>,
        'groupName': <group_name>,
        'element': <element_dict>
    }
    """
    results = []
    for layout in screen_specs.get("layouts", []):
        for group in layout.get("groups", []):
            group_name = group.get("groupName")
            for element in group.get("elements", []):
                if element.get("inputs") and len(element["inputs"]) > 0:
                    results.append({
                        "screen": screen_specs.get("name"),
                        "groupName": group_name,
                        "element": element
                    })
    return results

def find_elements_with_inputs_and_data_request(screen_specs: dict) -> list:
    """
    Find all elements inside layouts/groups that have both 'inputs' and 'dataRequest'.
    Returns a list of dicts:
      {
        'formName': <element_name>,
        'dataRequest': <data_request_object>
      }
    """
    results = []
    for layout in screen_specs.get("layouts", []):
        for group in layout.get("groups", []):
            for element in group.get("elements", []):
                if element.get("inputs") and element.get("dataRequest"):
                    results.append({
                        "formName": element.get("name"),
                        "dataRequest": element.get("dataRequest"),
                    })
    return results

def process_element_for_form_generation(element_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process a single element from the element_processing_queue for form generation.

    Extracts element definition, attaches matching request data if found,
    and returns the processed element ready for AI form generation.

    Args:
        element_data: Dictionary containing elementDefinition, formRequestLookup, and allRequests

    Returns:
        Processed element_definition with attached request data, or None if element should be skipped
    """
    element_definition = element_data["elementDefinition"]
    form_request_lookup = element_data["formRequestLookup"]
    all_requests = element_data["allRequests"]

    # Skip empty elements
    if not element_definition:
        return None

    # Attach matching request data if found
    matching_form_request = form_request_lookup.get(element_definition["name"])
    if matching_form_request:
        update_element_data_request(element_definition, matching_form_request, all_requests)

    return element_definition

def update_element_data_request(element: Dict[str, Any], matching_request: Dict[str, Any], requests: Dict[str, Any]) -> None:
    """
    Updates an element's dataRequest with the corresponding request details.

    Args:
        element (dict): The element to update.
        matching_request (dict): The matching data request object.
        requests (dict): The full requests dictionary.
    """
    for _, req in requests.items():
        if (
            req.get("type") == matching_request["dataRequest"].get("type") and
            req.get("path") == matching_request["dataRequest"].get("path")
        ):
            element["dataRequest"]["params"] = req.get("params")
            element["dataRequest"]["body"] = req.get("body")
            break

def get_paths() -> Dict[str, str]:
    """
    Get standardized absolute paths for the application structure.

    Returns absolute paths for all directories and files used across agents.
    Base directory is calculated relative to this file's location.

    Returns:
        Dictionary mapping logical path names to absolute file paths
    """
    base_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..", "..", ".."))
    return {
        "root": base_dir,
        "specifications_path": os.path.join(base_dir, "specs", "specifications.json"),
        "routing_app_path": os.path.join(base_dir, "specs", "routingApp.json"),
        "pages_dir": os.path.join(base_dir, "specs", "pages"),
        "modals_dir": os.path.join(base_dir, "specs", "modals"),
        "forms_dir": os.path.join(base_dir, "specs", "forms"),
        "screens_dir": os.path.join(base_dir, "specs", "screens"),
        "project_models_file_path": os.path.join(base_dir, "specs", "models.json"),
        "features_dir": os.path.join(base_dir, "specs", "features"),
        "requests_file_path": os.path.join(base_dir, "specs", "requests.json"),
        "schema_prisma_file_path": os.path.join(base_dir, "backend", "api-starter", "prisma", "schema.prisma"),
        "consolidated_data_schemas_file_path": os.path.join(base_dir, "specs", "consolidated_data_schemas.json"),
        "collected_data_schemas_file_path": os.path.join(base_dir, "specs", "collected_data_schemas.json"),
    }

def file_system_toolset() -> List[MCPToolset]:
    """
    Create file system toolset for MCP operations.

    Sets up the Model Context Protocol toolset for file system operations
    including reading, writing, and directory management within the
    team app studio directory structure.

    Returns:
        List containing configured MCPToolset for file operations
    """
    current_dir = os.path.dirname(os.path.abspath(__file__))
    team_app_studio_dir = os.path.abspath(os.path.join(current_dir, "..", "..", "..", ".."))

    return [MCPToolset(
        connection_params=StdioServerParameters(
            command='npx',
            args=[
                "-y",
                "@modelcontextprotocol/server-filesystem",
                team_app_studio_dir,
            ],
        ),
        tool_filter=["read_text_file", "write_file", "create_directory", "list_directory"]
    )]


def get_form_generator_tools() -> List[Union[BaseTool, MCPToolset, FunctionTool]]:
    """
    Get tools for the form generator agent.

    Returns:
        List of tools including helper function tools and MCP filesystem toolset.
    """
    return [
        FunctionTool(func=gather_elements_from_spec_files),
        FunctionTool(func=get_paths),
        *file_system_toolset(),
    ]

# Export tools
__all__ = [
    "get_form_generator_tools"
]