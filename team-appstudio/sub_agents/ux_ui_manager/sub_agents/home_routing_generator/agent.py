"""
Home Routing Generator Agent - Home Page Routing Configuration
==============================================================
"""

from google.adk.agents import LlmAgent
from .prompt import get_instruction
from .tools import get_home_routing_generator_tools

# Create Home Routing Generator Agent as sub-agent

home_routing_generator = LlmAgent(
    name="home_routing_generator",
    model="gemini-2.5-flash",
    description="Generates home page routing configuration for the application",
    instruction=get_instruction(),
    tools=get_home_routing_generator_tools()
)

root_agent = home_routing_generator
