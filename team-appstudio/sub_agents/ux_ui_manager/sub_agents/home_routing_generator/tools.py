"""
Home Routing Generator Tools
============================
"""
import json
import os
import re
import shutil
from typing import Any, List, Union, Dict
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters
from google.adk.tools.base_tool import BaseTool
from google.adk.tools.function_tool import FunctionTool
from typing import List


# ============================================================================
# CORE UTILITIES - File Operations
# ============================================================================

def list_files(directory: str) -> List[str]:
    """
    List all files in a directory (non-recursive).

    Args:
        directory: Path to directory to list

    Returns:
        List of filenames (not full paths) in the directory
    """
    if not os.path.isdir(directory):
        return []
    return [f for f in os.listdir(directory) if os.path.isfile(os.path.join(directory, f))]


def read_json(file_path: str) -> Any:
    """
    Read and parse a JSON file.

    Args:
        file_path: Path to JSON file

    Returns:
        Parsed JSON content (dict, list, etc.)
    """
    with open(file_path, "r", encoding="utf-8") as f:
        return json.load(f)


def write_json(file_path: str, data: Any) -> None:
    """
    Write data to a JSON file with proper formatting.

    Creates parent directories if they don't exist.

    Args:
        file_path: Path where JSON file should be written
        data: Data to serialize to JSON
    """
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(data, f, indent=2, ensure_ascii=False)


def read_text(file_path: str) -> str:
    """
    Read text file content.

    Args:
        file_path: Path to text file

    Returns:
        File content as string
    """
    with open(file_path, "r", encoding="utf-8") as f:
        return f.read()


def write_text(file_path: str, content: str) -> None:
    """
    Write text content to file.

    Creates parent directories if they don't exist.

    Args:
        file_path: Path where file should be written
        content: Text content to write
    """
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(content)


def move_file(src: str, dst: str) -> None:
    """
    Move file from source to destination.

    Creates destination directory if it doesn't exist.

    Args:
        src: Source file path
        dst: Destination file path
    """
    os.makedirs(os.path.dirname(dst), exist_ok=True)
    shutil.move(src, dst)


def delete_folder(directory: str) -> None:
    """
    Delete folder and recreate it empty.

    Args:
        directory: Directory path to delete and recreate
    """
    if os.path.isdir(directory):
        shutil.rmtree(directory)
    os.makedirs(directory, exist_ok=True)


def to_camel_case(text: str) -> str:
    """
    Converts text to camelCase.
    Handles spaces, underscores, and other separators.

    Examples:
        "Create New Post" -> "createNewPost"
        "view_all_posts" -> "viewAllPosts"
    """
    # Replace spaces and other separators with underscores, then split
    normalized = re.sub(r'[^a-zA-Z0-9]', '_', text.strip())
    parts = [part for part in normalized.split('_') if part]

    if not parts:
        return ""

    # First word lowercase, rest capitalized
    return parts[0].lower() + ''.join(word.capitalize() for word in parts[1:])


# ============================================================================
# AGENT: HOME ROUTING GENERATOR
# ============================================================================

def generate_home_config(home_routes_path: str, home_config_path: str) -> None:
    """
    Generate frontend home configuration file from home routes JSON.

    Args:
        home_routes_path: Path to the home routes JSON file
        home_config_path: Path where the home config TypeScript file should be written
    """
    config = read_json(home_routes_path)
    routes = config["homeRoutes"]

    # Use a Set to remove duplicates from icons
    unique_icons = list(set(route["icon"] for route in routes))

    # Map routes with proper i18n formatting
    routes_with_i18n = []
    for route in routes:
        route_copy = route.copy()
        route_copy["title"] = f"i18n.t('homeRoutes.{to_camel_case(route['title'])}')"
        routes_with_i18n.append(route_copy)

    # Convert to JSON with pretty formatting
    icon_name_to_component = json.dumps(routes_with_i18n, indent=2)

    # Replace icon strings with actual component names
    for icon in unique_icons:
        icon_name_to_component = icon_name_to_component.replace(f'"{icon}"', icon)

    # Ensure i18n.t() stays as a function call
    import re
    icon_name_to_component = re.sub(r'"i18n\.t\((.*?)\)"', r'i18n.t(\1)', icon_name_to_component)

    template = f"""import i18n from 'src/utils/locale';
import {{ {", ".join(unique_icons)} }} from '@app-studio/web';

export const HOME_CONFIG = {{
  interactionMode: "{config["interactionMode"]}",
  cards: {icon_name_to_component},
}};
"""

    write_text(home_config_path, template)


def build_home_routes_locale(home_routes_path: str) -> Dict[str, Any]:
    """
    Build home routes locale configuration from home routes JSON.

    Args:
        home_routes_path: Path to the home routes JSON file

    Returns:
        Dictionary with home routes locale configuration
    """
    config = read_json(home_routes_path)
    transformed_routes = {}

    for route in config["homeRoutes"]:
        # Convert title to camelCase for the key
        key = to_camel_case(route["title"])
        transformed_routes[key] = route["title"]

    return {
        "homeRoutes": transformed_routes
    }


def update_locale_with_configs(home_routes_path: str, locales_dir: str, locale_file_name: str = "en.ts") -> None:
    """
    Update locale files with home routes configuration.
    """
    locale_file_path = os.path.join(locales_dir, locale_file_name)

    # Ensure locales directory exists
    os.makedirs(locales_dir, exist_ok=True)
    if not os.path.exists(locales_dir):
        write_text(os.path.join(locales_dir, ".gitkeep"), "")

    existing_locale = {}

    # Read and parse existing locale file if it exists
    if os.path.exists(locale_file_path):
        content = read_text(locale_file_path)
        match = re.search(r'export default ({[\s\S]*});', content)
        if match and match.group(1):
            try:
                existing_locale = eval(f"({match.group(1)})")
            except:
                existing_locale = {}

    # Generate new locale data for home routes (avec camelCase)
    new_locale = build_home_routes_locale(home_routes_path)

    # Merge with existing locale
    merged_locale = {**existing_locale, **new_locale}

    # Format and write the updated locale file
    formatted_locale = json.dumps(merged_locale, indent=2, ensure_ascii=False)
    file_content = f"export default {formatted_locale};\n"

    write_text(locale_file_path, file_content)

def get_paths() -> Dict[str, str]:
    """
    Get standardized absolute paths for the application structure.

    Returns absolute paths for all directories and files used across agents.
    Base directory is calculated relative to this file's location.

    Returns:
        Dictionary mapping logical path names to absolute file paths
    """
    base_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..", "..", ".."))
    return {
        "root": base_dir,
        "specifications_path": os.path.join(base_dir, "specs", "specifications.json"),
        "routing_app_path": os.path.join(base_dir, "specs", "routingApp.json"),
        "pages_dir": os.path.join(base_dir, "specs", "pages"),
        "modals_dir": os.path.join(base_dir, "specs", "modals"),
        "forms_dir": os.path.join(base_dir, "specs", "forms"),
        "screens_dir": os.path.join(base_dir, "specs", "screens"),
        "project_models_file_path": os.path.join(base_dir, "specs", "models.json"),
        "features_dir": os.path.join(base_dir, "specs", "features"),
        "requests_file_path": os.path.join(base_dir, "specs", "requests.json"),
        "schema_prisma_file_path": os.path.join(base_dir, "backend", "api-starter", "prisma", "schema.prisma"),
        "consolidated_data_schemas_file_path": os.path.join(base_dir, "specs", "consolidated_data_schemas.json"),
        "collected_data_schemas_file_path": os.path.join(base_dir, "specs", "collected_data_schemas.json"),
        "home_routes_path": os.path.join(base_dir, "specs", "homeRoutes.json"),
        "home_config_path": os.path.join(base_dir, "frontend", "front-starter", "src", "configs", "HomeConfig.ts"),
        "locales_dir": os.path.join(base_dir, "frontend", "front-starter", "src", "locales")
    }

def file_system_toolset() -> List[MCPToolset]:
    """
    Create file system toolset for MCP operations.

    Sets up the Model Context Protocol toolset for file system operations
    including reading, writing, and directory management within the
    team app studio directory structure.

    Returns:
        List containing configured MCPToolset for file operations
    """
    current_dir = os.path.dirname(os.path.abspath(__file__))
    team_app_studio_dir = os.path.abspath(os.path.join(current_dir, "..", "..", "..", ".."))

    return [MCPToolset(
        connection_params=StdioServerParameters(
            command='npx',
            args=[
                "-y",
                "@modelcontextprotocol/server-filesystem",
                team_app_studio_dir,
            ],
        ),
        tool_filter=["read_text_file", "write_file", "create_directory", "list_directory"]
    )]


def get_home_routing_generator_tools() -> List[Union[BaseTool, MCPToolset, FunctionTool]]:
    """
    Get tools for home routing generator agent.

    Returns:
        List of tools including function tools, agent tools, and MCP toolset
    """
    return [
        FunctionTool(func=get_paths),
        *file_system_toolset(),
        FunctionTool(func=generate_home_config),
        FunctionTool(func=update_locale_with_configs)
    ]


# Export tools
__all__ = [
    "get_home_routing_generator_tools"
]
