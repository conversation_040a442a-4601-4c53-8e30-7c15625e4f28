"""
Navigation Generator Tools
==========================
"""
import json
import os
import shutil
from typing import Any, List, Union, Dict
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters
from google.adk.tools.base_tool import BaseTool
from google.adk.tools.function_tool import FunctionTool
from typing import List


# ============================================================================
# CORE UTILITIES - File Operations
# ============================================================================

def list_files(directory: str) -> List[str]:
    """
    List all files in a directory (non-recursive).

    Args:
        directory: Path to directory to list

    Returns:
        List of filenames (not full paths) in the directory
    """
    if not os.path.isdir(directory):
        return []
    return [f for f in os.listdir(directory) if os.path.isfile(os.path.join(directory, f))]


def read_json(file_path: str) -> Any:
    """
    Read and parse a JSON file.

    Args:
        file_path: Path to JSON file

    Returns:
        Parsed JSON content (dict, list, etc.)
    """
    with open(file_path, "r", encoding="utf-8") as f:
        return json.load(f)


def write_json(file_path: str, data: Any) -> None:
    """
    Write data to a JSON file with proper formatting.

    Creates parent directories if they don't exist.

    Args:
        file_path: Path where JSON file should be written
        data: Data to serialize to JSON
    """
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(data, f, indent=2, ensure_ascii=False)


def read_text(file_path: str) -> str:
    """
    Read text file content.

    Args:
        file_path: Path to text file

    Returns:
        File content as string
    """
    with open(file_path, "r", encoding="utf-8") as f:
        return f.read()


def write_text(file_path: str, content: str) -> None:
    """
    Write text content to file.

    Creates parent directories if they don't exist.

    Args:
        file_path: Path where file should be written
        content: Text content to write
    """
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(content)


def move_file(src: str, dst: str) -> None:
    """
    Move file from source to destination.

    Creates destination directory if it doesn't exist.

    Args:
        src: Source file path
        dst: Destination file path
    """
    os.makedirs(os.path.dirname(dst), exist_ok=True)
    shutil.move(src, dst)


def delete_folder(directory: str) -> None:
    """
    Delete folder and recreate it empty.

    Args:
        directory: Directory path to delete and recreate
    """
    if os.path.isdir(directory):
        shutil.rmtree(directory)
    os.makedirs(directory, exist_ok=True)

# ============================================================================
# AGENT: NAVIGATION GENERATOR
# ============================================================================

def generate_frontend_navigation_config(navigation_file_path: str,front_end_navigation_file_path: str) -> None:
    """
    Generate frontend navigation configuration file.
    """
    nav_config = read_json(navigation_file_path)

    # Generate TypeScript content with proper formatting
    nav_config_json = json.dumps(nav_config["navigationConfig"], indent=2)
    content = f"""export interface MenuItem {{
  label: string;
  destination: string;
}}

export const NAV_CONFIG: Record<string, any> = {nav_config_json};
"""
    # Write to a separate TypeScript file, not overwriting the JSON
    write_text(front_end_navigation_file_path, content)

def get_paths() -> Dict[str, str]:
    """
    Get standardized absolute paths for the application structure.

    Returns absolute paths for all directories and files used across agents.
    Base directory is calculated relative to this file's location.

    Returns:
        Dictionary mapping logical path names to absolute file paths
    """
    base_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..", "..", ".."))
    return {
        "root": base_dir,
        "specifications_path": os.path.join(base_dir, "specs", "specifications.json"),
        "routing_app_path": os.path.join(base_dir, "specs", "routingApp.json"),
        "pages_dir": os.path.join(base_dir, "specs", "pages"),
        "modals_dir": os.path.join(base_dir, "specs", "modals"),
        "forms_dir": os.path.join(base_dir, "specs", "forms"),
        "screens_dir": os.path.join(base_dir, "specs", "screens"),
        "project_models_file_path": os.path.join(base_dir, "specs", "models.json"),
        "features_dir": os.path.join(base_dir, "specs", "features"),
        "requests_file_path": os.path.join(base_dir, "specs", "requests.json"),
        "schema_prisma_file_path": os.path.join(base_dir, "backend", "api-starter", "prisma", "schema.prisma"),
        "consolidated_data_schemas_file_path": os.path.join(base_dir, "specs", "consolidated_data_schemas.json"),
        "collected_data_schemas_file_path": os.path.join(base_dir, "specs", "collected_data_schemas.json"),
        "navigation_file_path": os.path.join(base_dir, "specs", "navigation.json"),
        "front_end_navigation_file_path": os.path.join(base_dir, "frontend", "front-starter", "src", "configs", "NavConfig.ts")
    }

def file_system_toolset() -> List[MCPToolset]:
    """
    Create file system toolset for MCP operations.

    Sets up the Model Context Protocol toolset for file system operations
    including reading, writing, and directory management within the
    team app studio directory structure.

    Returns:
        List containing configured MCPToolset for file operations
    """
    current_dir = os.path.dirname(os.path.abspath(__file__))
    team_app_studio_dir = os.path.abspath(os.path.join(current_dir, "..", "..", "..", ".."))

    return [MCPToolset(
        connection_params=StdioServerParameters(
            command='npx',
            args=[
                "-y",
                "@modelcontextprotocol/server-filesystem",
                team_app_studio_dir,
            ],
        ),
        tool_filter=["read_text_file", "write_file", "create_directory", "list_directory"]
    )]


def get_navigation_generator_tools() -> List[Union[BaseTool, MCPToolset, FunctionTool]]:
    """
    Get tools for navigation generator agent.

    Returns:
        List of tools including function tools, agent tools, and MCP toolset
    """
    return [
        FunctionTool(func=get_paths),
        *file_system_toolset(),
        FunctionTool(func=generate_frontend_navigation_config)
    ]


# Export tools
__all__ = [
    "get_navigation_generator_tools"
]
