"""
Notification Generator Tools
============================
"""
import json
import os
import shutil
import re
from typing import Any, List, Union, Dict
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters
from google.adk.tools.base_tool import BaseTool
from google.adk.tools.function_tool import FunctionTool


# ============================================================================
# CORE UTILITIES - File Operations
# ============================================================================

def list_files(directory: str) -> List[str]:
    """
    List all files in a directory (non-recursive).

    Args:
        directory: Path to directory to list

    Returns:
        List of filenames (not full paths) in the directory
    """
    if not os.path.isdir(directory):
        return []
    return [f for f in os.listdir(directory) if os.path.isfile(os.path.join(directory, f))]


def read_json(file_path: str) -> Any:
    """
    Read and parse a JSON file.

    Args:
        file_path: Path to JSON file

    Returns:
        Parsed JSON content (dict, list, etc.)
    """
    with open(file_path, "r", encoding="utf-8") as f:
        return json.load(f)


def write_json(file_path: str, data: Any) -> None:
    """
    Write data to a JSON file with proper formatting.

    Creates parent directories if they don't exist.

    Args:
        file_path: Path where JSON file should be written
        data: Data to serialize to JSON
    """
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(data, f, indent=2, ensure_ascii=False)


def read_text(file_path: str) -> str:
    """
    Read text file content.

    Args:
        file_path: Path to text file

    Returns:
        File content as string
    """
    with open(file_path, "r", encoding="utf-8") as f:
        return f.read()


def write_text(file_path: str, content: str) -> None:
    """
    Write text content to file.

    Creates parent directories if they don't exist.

    Args:
        file_path: Path where file should be written
        content: Text content to write
    """
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(content)


def move_file(src: str, dst: str) -> None:
    """
    Move file from source to destination.

    Creates destination directory if it doesn't exist.

    Args:
        src: Source file path
        dst: Destination file path
    """
    os.makedirs(os.path.dirname(dst), exist_ok=True)
    shutil.move(src, dst)


def delete_folder(directory: str) -> None:
    """
    Delete folder and recreate it empty.

    Args:
        directory: Directory path to delete and recreate
    """
    if os.path.isdir(directory):
        shutil.rmtree(directory)
    os.makedirs(directory, exist_ok=True)


# ============================================================================
# NOTIFICATION UTILITIES
# ============================================================================

def to_camel_case(snake_str: str) -> str:
    """Converts a snake_case string to camelCase."""
    parts = snake_str.split('_')
    return parts[0].lower() + ''.join(word.capitalize() for word in parts[1:])


def derive_function_name(key: str) -> str:
    """
    Derives a function name from a notification key.
    E.g., '/post/:id_GET' becomes 'postIdGET'

    Equivalent to JavaScript:
    export function deriveFunctionName(key) {
      const parts = key.replace(/^\\//, "").split("/");
      const cleaned = parts.map((part) => part.replace(/:/g, ""));
      return toCamelCase(cleaned.join("_"));
    }
    """
    # Remove leading slash and split by '/'
    parts = key.lstrip('/').split('/')
    # Remove colon characters and join parts with an underscore
    cleaned = [part.replace(':', '') for part in parts]
    # Join and convert to camelCase
    return to_camel_case('_'.join(cleaned))


def generate_notification_key(path: str, type_: str, is_workflow: bool, has_dynamic_data: bool) -> str:
    """
    Generates a notification key based on path, type, workflow, and dynamic data flags.
    """
    suffix = '_params' if has_dynamic_data else ''
    if is_workflow:
        return f"{path}_{type_}_workflow{suffix}"
    else:
        return f"{path}_{type_}{suffix}"


def extract_dynamic_params(path: str) -> List[str]:
    """
    Extracts dynamic parameters (e.g., ':id') from a route path.
    """
    return re.findall(r':([a-zA-Z0-9_]+)', path)

def extract_object_body(config_content: str) -> str:
    """
    Extracts the inner properties of NOTIFICATION_CONFIG = { ... }.
    """
    # Debug: Print what we're looking for
    print(f"Looking for NOTIFICATION_CONFIG in content of length: {len(config_content)}")
    print(f"Content preview: {config_content[:300]}")

    # Find the start of the object - make the pattern more flexible
    start_match = re.search(r"NOTIFICATION_CONFIG\s*=\s*\{", config_content)
    if not start_match:
        # Try alternative patterns
        start_match = re.search(r"const\s+NOTIFICATION_CONFIG\s*=\s*\{", config_content)
        if not start_match:
            start_match = re.search(r"export\s+const\s+NOTIFICATION_CONFIG\s*=\s*\{", config_content)
            if not start_match:
                raise ValueError(f"Could not find NOTIFICATION_CONFIG object. Content starts with: {config_content[:200]}")

    print(f"Found NOTIFICATION_CONFIG at position: {start_match.start()}")

    # Find the position after the opening brace
    start_pos = start_match.end() - 1  # Position of the opening brace

    # Count braces to find the matching closing brace
    brace_count = 0
    pos = start_pos

    while pos < len(config_content):
        char = config_content[pos]
        if char == '{':
            brace_count += 1
        elif char == '}':
            brace_count -= 1
            if brace_count == 0:
                # Found the matching closing brace
                break
        pos += 1

    if brace_count != 0:
        raise ValueError("Could not find matching closing brace for NOTIFICATION_CONFIG")

    # Extract content between the braces
    content = config_content[start_pos + 1:pos].strip()

    # Remove trailing comma if present
    if content.endswith(','):
        content = content[:-1].strip()

    print(f"Extracted content length: {len(content)}")
    print(f"Extracted content preview: {content[:200]}")

    return content

def add_content_to_object(file_content: str, new_content: str) -> str:
    """
    Inserts new properties into the Notification = { ... } object
    right before the closing brace.
    Strips outermost braces of new_content if present.
    """
    last_brace_index = file_content.rfind("}")
    if last_brace_index == -1:
        print("Notification object braces not found")
        return file_content

    # Remove the outermost braces of new_content
    trimmed_content = re.sub(r"^\{|\}$", "", new_content.strip())

    # Check if there's already a trailing comma before the last brace
    before_closing = file_content[:last_brace_index].rstrip()

    if before_closing.endswith(","):
        # Already has a comma → just append new content
        return (
            before_closing +
            "\n  " + trimmed_content + "\n" +
            file_content[last_brace_index:]
        )
    else:
        # No comma → insert with a comma
        return (
            before_closing +
            ",\n  " + trimmed_content + "\n" +
            file_content[last_brace_index:]
        )

# ============================================================================
# AGENT: NOTIFICATION GENERATOR
# ============================================================================

def generate_notification_config(pages_dir: str, modals_dir: str, generated_notification_config_path: str) -> Dict[str, Any]:
    """
    Generate notification configurations from page and modal specs.

    Args:
        pages_dir: Path to pages directory
        modals_dir: Path to modals directory
        generated_notification_config_path: Path where notification config should be written

    Returns:
        Generated notification configuration dictionary
    """
    notification_config = {}

    # Read pages and modals
    page_files = list_files(pages_dir) if os.path.exists(pages_dir) else []
    modal_files = list_files(modals_dir) if os.path.exists(modals_dir) else []

    def process_experience(exp, spec):
        """Process a single user experience."""
        if not exp or not spec or not isinstance(spec, dict):
            return

        screen_specs = spec.get("screenSpecs", {})
        requests = screen_specs.get("requests", {})

        if (exp.get("action", {}).get("request", {}).get("notify") and
            exp.get("action", {}).get("request", {}).get("requestId") and
            requests and isinstance(requests, dict)):

            req_id = exp["action"]["request"]["requestId"]
            request = requests.get(req_id)

            if request and request.get("path") and request.get("type"):
                request_path = request["path"]
                request_type = request["type"]
                use_workflow = request.get("useWorkflow", False)
                notify_data = exp["action"]["request"]["notify"]
                link = notify_data.get("link", "")
                has_dynamic_data = len(extract_dynamic_params(link)) > 0

                key = generate_notification_key(
                    request_path,
                    request_type,
                    bool(use_workflow),
                    has_dynamic_data
                )

                # Extract link and rest of notify_data (equivalent to JavaScript destructuring)
                rest = {k: v for k, v in notify_data.items() if k != "link"}

                notification_config[derive_function_name(key)] = {
                    "requestId": req_id,
                    "requestPath": request_path,
                    "requestType": request_type,
                    "useWorkflow": bool(use_workflow),
                    "dynamicRedirectionParams": extract_dynamic_params(link),
                    "link": link,
                    **rest
                }

    # Process all pages
    for file in page_files:
        if not file:
            continue
        file_path = os.path.join(pages_dir, file)
        try:
            page_spec = read_json(file_path)
            user_experience = page_spec.get("screenSpecs", {}).get("userExperience", [])
            for exp in user_experience:
                process_experience(exp, page_spec)
        except Exception as e:
            print(f"Error processing page file {file}: {e}")

    # Process all modals
    for file in modal_files:
        if not file:
            continue
        file_path = os.path.join(modals_dir, file)
        try:
            modal_spec = read_json(file_path)
            user_experience = modal_spec.get("screenSpecs", {}).get("userExperience", [])
            for exp in user_experience:
                process_experience(exp, modal_spec)
        except Exception as e:
            print(f"Error processing modal file {file}: {e}")

    # Generate and write the config template
    template = f"export const NOTIFICATION_CONFIG = {json.dumps(notification_config, indent=2)}"
    write_text(generated_notification_config_path, template)

    return notification_config

def format_dynamic_links(content: str) -> str:
    """
    Format dynamic links from :param format to ${param} template literal format.

    Args:
        content: Content containing links with :param format
    Returns:
        Content with links formatted as template literals
    """
    # Replace :param with ${param} in link values
    # Pattern matches: link: "/posts/:id" -> link: `/posts/${id}`
    pattern = r'link:\s*["\']([^"\']*):([a-zA-Z0-9_]+)([^"\']*)["\']'

    def replace_link(match):
        prefix = match.group(1)  # "/posts/"
        param = match.group(2)   # "id"
        suffix = match.group(3)  # ""
        return f'link: `{prefix}${{{param}}}{suffix}`'

    return re.sub(pattern, replace_link, content)

def update_notification_templates(notification_methods_path: str, ai_generated_notification_config_path: str) -> dict:
    """
    Update notification templates file with AI-generated content.
    Reads the AI-generated config as text (since it contains JS functions, not valid JSON),
    extracts the object content, and adds it to the notification.ts file.

    Args:
        notification_methods_path: Path to notification templates file
        generated_notification_config_path: Path to notification config file (not used in this implementation)
        ai_generated_notification_config_path: Path to AI-generated config file
    Returns:
        dict: status JSON for ADK
    """
    try:
        # Read existing notification file
        notification_file = read_text(notification_methods_path)

        # Read AI-generated config as text (not JSON since it contains functions)
        ai_config_content = read_text(ai_generated_notification_config_path)

        # The markdown file already contains just the object content
        extracted_content = ai_config_content.strip()

        # Format dynamic links from :param to ${param} template literal format
        formatted_content = format_dynamic_links(extracted_content)

        # Add the extracted content to the Notification object
        updated_notification_file = add_content_to_object(notification_file, formatted_content)

        # Write updated file
        write_text(notification_methods_path, updated_notification_file)

        return {"status": "success", "updated": notification_methods_path}
    except Exception as e:
        return {"status": "error", "message": str(e)}

def get_paths() -> Dict[str, str]:
    """
    Get standardized absolute paths for the application structure.

    Returns absolute paths for all directories and files used across agents.
    Base directory is calculated relative to this file's location.

    Returns:
        Dictionary mapping logical path names to absolute file paths
    """
    base_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..", "..", ".."))
    return {
        "root": base_dir,
        "specifications_path": os.path.join(base_dir, "specs", "specifications.json"),
        "routing_app_path": os.path.join(base_dir, "specs", "routingApp.json"),
        "pages_dir": os.path.join(base_dir, "specs", "pages"),
        "modals_dir": os.path.join(base_dir, "specs", "modals"),
        "forms_dir": os.path.join(base_dir, "specs", "forms"),
        "screens_dir": os.path.join(base_dir, "specs", "screens"),
        "project_models_file_path": os.path.join(base_dir, "specs", "models.json"),
        "features_dir": os.path.join(base_dir, "specs", "features"),
        "requests_file_path": os.path.join(base_dir, "specs", "requests.json"),
        "ai_generated_notification_config_path": os.path.join(base_dir, "specs", "ai_generated_notification_config.md"),
        "generated_notification_config_path": os.path.join(base_dir, "backend", "api-starter", "src", "shared", "config", "notification.config.ts"),
        "notification_methods_path": os.path.join(base_dir, "backend", "api-starter", "src", "shared", "notification", "notifications.ts")
    }

def file_system_toolset() -> List[MCPToolset]:
    """
    Create file system toolset for MCP operations.

    Sets up the Model Context Protocol toolset for file system operations
    including reading, writing, and directory management within the
    team app studio directory structure.

    Returns:
        List containing configured MCPToolset for file operations
    """
    current_dir = os.path.dirname(os.path.abspath(__file__))
    team_app_studio_dir = os.path.abspath(os.path.join(current_dir, "..", "..", "..", ".."))

    return [MCPToolset(
        connection_params=StdioServerParameters(
            command='npx',
            args=[
                "-y",
                "@modelcontextprotocol/server-filesystem",
                team_app_studio_dir,
            ],
        ),
        tool_filter=["read_text_file", "write_file", "create_directory", "list_directory"]
    )]

def get_notification_generator_tools() -> List[Union[BaseTool, MCPToolset, FunctionTool]]:
    """
    Get tools for notification generator agent.

    Returns:
        List of tools including function tools, agent tools, and MCP toolset
    """
    return [
        FunctionTool(func=get_paths),
        *file_system_toolset(),
        FunctionTool(func=generate_notification_config),
        FunctionTool(func=update_notification_templates)
    ]

# Export tools
__all__ = [
    "get_notification_generator_tools"
]
