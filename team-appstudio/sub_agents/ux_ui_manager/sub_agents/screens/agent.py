"""
Screen Specification Management Agents
=================================================================
Multi-agent system for generating screen specifications and managing data models
"""

from google.adk.agents import LlmAgent
from .prompt import  get_models_consolidator_instruction, get_patches_generator_instruction, get_model_updater_instruction, get_screens_specs_generator_instruction
from .tools import get_screens_specs_generator_tools, get_patches_generator_tools, models_consolidator_tools, get_model_updater_tools

# 🔄 MODELS CONSOLIDATOR
# Synchronizes existing entities in models.json with Prisma schema as source of truth
# - Updates ONLY existing entities (never adds new ones from Prisma)
# - Preserves custom fields not defined in Prisma
# - Respects deny list: ["image", "video", "comment", "account", "chatMessage", "chatConversation"]
models_consolidator = LlmAgent(
    name="models_consolidator",
    model="gemini-2.5-flash",
    description="Expert in consolidating data schemas for a single feature based on user experience flow and conditions.",
    instruction=get_models_consolidator_instruction(),
    tools=models_consolidator_tools()
)

# 🔧 PATCHES GENERATOR
# Generates and applies Prisma schema patches from models.json
# - Reads current Prisma schema and models.json
# - Generates diff patches in <<<ORIGINAL/UPDATED>>> format
# - Applies patches to update Prisma schema
# - Handles payment-specific updates when paymentType is "credit"
patches_generator = LlmAgent(
    name="patches_generator",
    model="gemini-2.5-flash",
    description="Expert in generating Prisma schema for a single feature based on user experience flow and conditions.",
    instruction=get_patches_generator_instruction(),
    tools=get_patches_generator_tools()
)

# 📱 SCREEN SPECS GENERATOR
# Generates comprehensive screen specifications from routing configuration
# - Reads routing app and extracts formatted model paths
# - Creates detailed screen specs with UX flows, layouts, and API requests
# - Includes error handling and retry logic (max 2 retries per operation)
# - Updates routing app with screen names and types
screens_specs_generator = LlmAgent(
    name="screens_specs_generator",
    model="gemini-2.5-flash",
    description="Expert in generating comprehensive screen specifications for a single feature based on user experience flow and conditions.",
    instruction=get_screens_specs_generator_instruction(),
    tools=get_screens_specs_generator_tools()
)

# 🗃️ MODEL UPDATER
# Consolidates and cleans data models from screen specifications
# - Collects all dataSchemas from screen specification files
# - Merges schemas into unified models definition
# - Removes duplicates and resolves type conflicts
# - Normalizes user-related fields and produces clean models.json
model_updater = LlmAgent(
    name="model_updater",
    model="gemini-2.0-flash",
    description="Expert in generating Prisma schema for a single feature based on user experience flow and conditions.",
    instruction=get_model_updater_instruction(),
    tools=get_model_updater_tools()
)

# Export all agents
__all__ = [models_consolidator, patches_generator, screens_specs_generator, model_updater]

