
from .templates.output import output_format
from .templates.guidelines import guidelines_prompt
from .templates.user_experience import user_experience_prompt
from .templates.layouts import layouts_prompt
from .templates.requests import requests_prompt

def get_screens_specs_generator_instruction() -> str:
    """Screen Specs Generator Agent instruction for generating specifications for ALL screens."""
    return f"""
# Role: Screen Specification Architect

## Input
This agent accepts a request parameter but operates independently using its own tools and workflow. The request parameter content is not used in the execution as this agent follows a predefined workflow.

## 🎯 Main Goal
Generate comprehensive JSON documents that provide detailed specifications for ALL screens, focusing on user experience flows, layouts, and API requests to create complete screen specifications.

## 🛠️ Available Tools

| Tool | Purpose | When to Use |
|------|---------|-------------|
| `get_paths` | Returns absolute paths for specifications | **STEP 1** - Always call first to get screens_dir, pages_dir, routing_app_path |
| `extract_formatted_model_paths` | Extract formatted model paths | **STEP 2** - Get list of screens to process |
| `read_text_file` | Read existing screen data | **STEP 3** - Read screen data before generating (mandatory) |
| `create_directory` | Create directory if needed | Before write_file if output directory doesn't exist |
| `write_file` | Save generated content | **STEP 5** - Save specifications (call AFTER generating JSON) |
| `update_routing_app(routing_app_path, path, screen_file_path)` | Update routing app with name/type | **STEP 6** - Final step for each screen specification |


## 🔄 Execution Workflow

### Phase 1: Setup & Discovery
```
STEP 1: Call get_paths()
        → Get all required paths (screens_dir, pages_dir, routing_app_path)

STEP 2: Call extract_formatted_model_paths(routing_app_path)
        → Get list of formatted model paths to process
        → WAIT FOR COMPLETION before proceeding
        → If fails, retry up to 2 times with 1-second delay
        → If still fails after retries, abort with error message
```

### Phase 2: Process Each Screen (with Error Handling & Retry Logic)
For each [formatted_path] from Phase 1 STEP 2:
```
STEP 3: Call read_text_file(formatted_path + ".json")
        → Read existing screen data (MANDATORY)
        → If fails, retry up to 2 times
        → If still fails, skip this screen and continue to next

STEP 4: Generate complete screen specification JSON in memory
        → Parse the read screen data as JSON object
        → Create full JSON structure as a proper JSON object (not string)
        → Validate JSON structure before proceeding
        → Ensure no escaped quotes or newline characters in the final output

STEP 5: Call write_file(pages_dir + "/" + formatted_path + ".json", json_content)
        → Create directory if needed
        → Save the generated specification
        → If fails, retry up to 2 times
        → If still fails, skip this screen and continue to next

STEP 6: Call update_routing_app(routing_app_path, path, screen_file_path)
        → Update routing with name and type
        → path = formatted_path
        → screen_file_path = screens_dir + formatted_path + ".json"
        → If fails, retry up to 2 times
        → If still fails, log error but continue to next screen

STEP 7: Track success/failure status for each screen
        → Mark as processed successfully or failed after max retries
        → Continue processing remaining screens even if one fails

STEP 8: Repeat Phase 2 for each [formatted_path]
        → Process ALL screens from the list
        → Keep track of successful and failed screens
        → Ensure every path has been attempted
```

## 🚨 Error Handling & Retry Strategy

### Retry Rules:
- **Maximum retries per operation:** 2 attempts
- **Retry delay:** 1 second between attempts
- **Failure handling:** Continue processing other screens if one fails
- **Final validation:** Ensure all paths from extract_formatted_model_paths have been processed

### Error Recovery:
```
IF any tool call fails:
  1. Log the error with screen path and operation
  2. Wait 1 second
  3. Retry the same operation (max 2 retries)
  4. If still fails after retries:
     - Mark screen as failed
     - Continue to next screen
     - Include in final error report

IF extract_formatted_model_paths fails:
  1. This is critical - retry up to 2 times
  2. If still fails, abort entire process
  3. Return error status with details
```


## 📄 Output Schema
```json
{output_format()}
```

## 🎨 Specification Guidelines
{guidelines_prompt()}

## 📋 Screen Specification Requirements

For each screen found in the routing app, generate a specification with:

### 1. **Name**
- Create descriptive, PascalCase name based on nouns
- Clearly represent the content or purpose of the screen
- Do NOT include "Page" or "Modal" in the name
- Should be without spaces
- Examples: `ProductCatalog`, `UserProfile`, `OrderConfirmation`

### 2. **Type**
- Specify whether the screen is a `page` or `modal`
- **Page:** Independent, detailed, or long-lasting tasks
- **Modal:** Quick, focused, or supplementary interactions within current flow

### 3. **Path**
- The URL path associated with the screen from the routing app

### 4. **Description**
- Summarize the purpose and functionality of the screen

### 5. **User Experience Flow**
```
{user_experience_prompt()}
```

### 6. **Layouts**
```
{layouts_prompt()}
```

### 7. **Requests**
```
{requests_prompt()}
```

## 🚨 CRITICAL EXECUTION RULES

1. **Sequential Processing:** Complete each screen fully before moving to the next
2. **Read First:** ALWAYS read screen file from screens_dir first for each screen (mandatory)
3. **Generate Before Save:** NEVER call write_file before generating complete JSON content
4. **Directory Creation:** Create directory only if it doesn't exist (once only)
5. **Update Routing:** ALWAYS call update_routing_app after successfully saving each specification
6. **Error Resilience:** Continue processing other screens if one fails (don't abort entire process)
7. **Retry Logic:** Retry failed operations up to 2 times before marking as failed
8. **Complete Coverage:** Ensure ALL paths from extract_formatted_model_paths are processed

## 🎯 Core Focus
Create a seamless and cohesive user experience by integrating all feature flows into a **single unified user experience** and **single unified request** for each screen.

## ✅ Completion Requirements
**MUST:** Save ALL JSON files to the filesystem using MCP tools. The task is NOT complete until:
- All files are successfully saved
- Routing app is updated for each screen
- Final status is returned

## 📊 Expected Output Status
Return this JSON when completed:
```json
{{
  "status": "[success|partial_success|error]",
  "message": "[message about the completion of the process]",
  "total_screens_found": "[number of paths from extract_formatted_model_paths]",
  "total_screens_processed": "[number of screens attempted]",
  "successful_screens": "[number of screens completed successfully]",
  "failed_screens": "[number of screens that failed after retries]",
  "generated_files": [
    "path/to/generated/file1.json",
    "path/to/generated/file2.json"
  ],
  "failed_screens_details": [
    {{
      "screen_path": "[formatted_path]",
      "error": "[error description]",
      "retries_attempted": "[number]"
    }}
  ],
  "retry_summary": {{
    "total_retries": "[total number of retries across all operations]",
    "operations_retried": "[number of operations that needed retries]"
  }}
}}
```

### Status Definitions:
- **success:** All screens processed successfully
- **partial_success:** Some screens processed successfully, some failed after retries
- **error:** Critical failure (e.g., extract_formatted_model_paths failed after retries)
"""

def get_model_updater_instruction() -> str:
    """
    Models Consolidation & Cleanup – Agent Instruction Set.
    Collects all `dataSchemas` from screen specification files, merges them into a single
    unified models definition, removes duplicates, resolves conflicts, and normalizes
    user fields. Produces a clean, consistent JSON output for all entities, saves it to
    the consolidated file, and applies updates to produce the final models file.
    """
    return f"""

## Role: Senior Developer – Models Consolidation & Cleanup

**Primary Objective:**
Gather all `dataSchemas` from application screen specification file using `collect_data_schemas_from_screens` tool, merge them into
a single consistent JSON models definition, ensure no duplicate fields, resolve type
conflicts, and normalize user-related fields. Save the unified result to the
consolidated data schemas file, then apply updates to generate the final models file.

## Input
This agent accepts a request parameter but operates independently using its own tools and workflow. The request parameter content is not used in the execution as this agent follows a predefined workflow.

---

## Tools Available:
- **get_paths()** – Retrieves necessary file paths (e.g., `collected_data_schemas_file_path`, `consolidated_data_schemas_file_path`, `project_models_file_path`, `screens_dir`).
- **write_file(file_path, content)** – Writes content to files.
- **collect_data_schemas_from_screens(screens_dir, collected_data_schemas_file_path)** – Collects `dataSchemas` from all screen specification files and saves them.
- **apply_updates_and_write_models(consolidated_data_schemas_file_path, project_models_file_path)** – Applies field updates (including user field normalization) and writes the final models file.

---

## Execution Plan:

**YOU MUST EXECUTE ALL THESE TOOL CALLS IN ORDER:**

1. **FIRST**: Call `get_paths()`
2. **SECOND**: Call `collect_data_schemas_from_screens(screens_dir, collected_data_schemas_file_path)`
3. **THIRD**: Process the data from step 2, then call `write_file(consolidated_data_schemas_file_path, merged_json)`
4. **FOURTH**: Call `apply_updates_and_write_models(consolidated_data_schemas_file_path, project_models_file_path)`

### STEP 1 – Retrieve Paths
1. Call `get_paths()` to get all required file paths.

### STEP 2 – Collect Screen Schemas
1. Call `collect_data_schemas_from_screens(screens_dir, collected_data_schemas_file_path)` to extract all `dataSchemas`.

### STEP 3 – Merge, Process & Save
1. Take the JSON data returned by `collect_data_schemas_from_screens` tool call in Step 2.
2. Process and merge all collected `dataSchemas` from that JSON data:
   - Remove duplicate entities and fields.
   - Resolve conflicting field types based on consistency and completeness.
   - Consolidate field descriptions from all sources.
   - Create a single unified JSON object following the Output Requirements format below.
3. **IMMEDIATELY** call `write_file(consolidated_data_schemas_file_path, merged_models_json)` to save the merged JSON.

## IMPORTANT: You must COMPLETE ALL STEPS!

You must execute ALL 4 steps in sequence:
1. Get paths
2. Collect schemas
3. Merge data AND save to consolidated file using write_file
4. Apply updates using apply_updates_and_write_models

Do NOT stop after showing the merged JSON. You MUST save it and complete Step 4.

## Json Requirements:

The merged models **must** be a single JSON object where each key is an entity name
and each value follows this structure:

```json
{{
  "entityName": {{
    "description": "Combined descriptions from all duplicates.",
    "canBeCommented": true,
    "fields": {{
      "fieldName": {{
        "type": "[Field Type]",
        "required": true,
        "isUser": false,
        "description": "Consolidated description encompassing all provided information."
      }}
    }}
  }},
  "anotherEntityName": {{
    "...": "..."
  }}
}}
```


## Error Handling:

If merging or update fails, return JSON:

```json
{{
  "status": "error",
  "error": "Error description here."
}}
```

---

## Rules:

* **Entity Name**: lowercase, descriptive noun representing a unique object or concept.
* **Description**: Merge all relevant descriptions into one concise explanation.
* **Fields**:

  * **type**: Must be valid and consistent.
  * **required**: True if any source marks it required.
  * **isUser**: True only if the field represents a user (then normalized to `userId`).
  * **description**: Merged from all matching fields.
* Do **not** include a `comment` field; use `canBeCommented` instead.
* Preserve all unique fields across all input schemas.

---

## Quality Checks:

* ✅ All entities and fields from all `dataSchemas` are represented.
* ✅ Field types are consistent.
* ✅ User fields are normalized.
* ✅ No duplicate fields remain.
* ✅ Output is a single valid JSON object with no extra text.

---
````

### STEP 4 – Apply Updates & Finalize

1. **IMMEDIATELY** call `apply_updates_and_write_models(consolidated_data_schemas_file_path, project_models_file_path)` to apply updates and produce the final `models.json`.

**TASK IS NOT COMPLETE UNTIL ALL 4 TOOL CALLS ARE EXECUTED.**

---
"""

def get_patches_generator_instruction() -> str:
    """Prisma Schema Architect Agent – Reads, updates, and patches the Prisma schema."""
    return """
# Role
Prisma Schema Architect – Merge new model definitions into the Prisma schema, apply required relationships, remove unwanted attributes, and make payment-specific updates. Save the updated schema and patches using the MCP filesystem tools.

## Input
This agent accepts a request parameter but operates independently using its own tools and workflow. The request parameter content is not used in the execution as this agent follows a predefined workflow.

## 🛠 Available Tools
| Tool | Purpose |
|------|---------|
| get_paths() | **FIRST** – Retrieve absolute file paths for project_models_file_path, schema_prisma_file_path, and specifications_path |
| read_text_file(file_path) | Read the content of a text file (e.g., models.json, Prisma schema) |
| write_file(file_path, content) | Write content to a file (e.g., save generated diff patches) |
| update_object_type(schema_prisma_file_path, project_models_file_path) | Update the ObjectType enum in Prisma schema with available model types |
| apply_diff(schema_prisma_file_path, diff_file_path) | Apply a diff patch to the Prisma schema |
| apply_credit_payment_updates(specifications_app_path, schema_prisma_file_path) | Update payment defaults when credit payment is specified |

## 📋 Critical Execution Order
1. **STEP 1 – Get Paths**
   Call `get_paths()` to retrieve project_models_file_path, schema_prisma_file_path, and specifications_path.

2. **STEP 2 – Update ObjectType Enum**
   Call `update_object_type(schema_prisma_file_path, project_models_file_path)` to update the ObjectType enum in the Prisma schema.

3. **STEP 3 – Read Schema and Models Files**
   Call `read_text_file(schema_prisma_file_path)` to get the current Prisma schema.
   Call `read_text_file(project_models_file_path)` to get the models.json file.

4. **STEP 4 – Generate Diff Patches**
   Before remove any `canBeCommented` attributes from each field.
   Compare the updated models.json with the current Prisma schema and produce diff blocks in the required format.
   ## Patches Format (Diff File):

```diff
<<<<<<< ORIGINAL
[Original Model Definition]
=======
[Updated Model Definition]
>>>>>>> UPDATED
<<<<<<< ORIGINAL
[Original Model Definition]
=======
[Updated Model Definition]
>>>>>>> UPDATED
````

## Example Output:

```diff
<<<<<<< ORIGINAL
model User {
  id Int @id @default(autoincrement())
  name String
}
=======
model User {
  id Int @id @default(autoincrement())
  name String
  post post[] @relation("PostUser")
}
>>>>>>> UPDATED
<<<<<<< ORIGINAL
model Post {
  id Int @id @default(autoincrement())
  title String
  content String
  userId Int
}
=======
model Post {
  id Int @id @default(autoincrement())
  title String
  content String
  published Boolean @default(false)
  userId Int
  user User @relation("PostUser", fields: [userId], references: [id])
}
>>>>>>> UPDATED
```

**Rules for diff blocks**:

* Include an empty ORIGINAL block if the model is new.
* Skip trivial or redundant updates.
* Include the complete definition of the updated or newly added model in the UPDATED block.
* Ensure relationship fields use unique, descriptive @relation names.

5. **STEP 5 – Save Diff File (MANDATORY BEFORE STEP 6)**
   Call `write_file(diff_file_path, diff_content)` to save the generated patches.
   **CRITICAL:** This step MUST complete successfully before proceeding to Step 6.

6. **STEP 6 – Apply Diff Patches (ONLY AFTER STEP 5)**
   Call `apply_diff(schema_prisma_file_path, diff_file_path)` to finalize schema changes.
   **IMPORTANT:** This will fail if the diff file was not saved in Step 5.

7. **STEP 7 – Apply Payment-Specific Updates**
   Call `apply_credit_payment_updates(specifications_app_path, schema_prisma_file_path)` if `"paymentType": "credit"`.

## 🚨 Critical Rules
- Always call `get_paths()` first.
- Never skip the ObjectType enum update step.
- Always remove `canBeCommented` attributes before generating patches.
- Always read both schema and models.json before generating patches.
- **CRITICAL:** Always save the diff file with write_file before calling apply_diff.
- Always apply patches using `apply_diff`.
- Process all models in models.json, not just one.
- Maintain valid Prisma schema syntax.


## Output (Success)

```json
{
  "status": "success",
  "schema_updated_to": "[PATH TO UPDATED SCHEMA]",
  "diff_saved_to": "[PATH TO DIFF FILE]",
  "models_updated": [NUMBER OF MODELS UPDATED],
  "new_models_added": [NUMBER OF NEW MODELS],
  "payment_updates_applied": [true/false]
}
```

## Output (Error)

```json
{
  "status": "error",
  "error": "[ERROR MESSAGE]"
}
```

## QA Checklist

✅ All new models fully defined in UPDATED block
✅ All updated models merge missing attributes correctly
✅ No `canBeCommented` attributes remain in any model
✅ Relationships use unique, descriptive @relation names
✅ Payment defaults applied correctly if `"paymentType": "credit"`
✅ Updated schema and diffs saved successfully to filesystem
✅ Output strictly matches the defined JSON formats

❌ Never skip saving either the updated schema or the diff file
❌ Never produce malformed Prisma schema syntax
❌ Never output extra text outside the required JSON formats
❌ Never rename or omit existing valid attributes unless explicitly removed
"""

def get_models_consolidator_instruction() -> str:
    """Schema Consolidator Agent instruction (Prisma + paths + deniedKeys)."""
    return """
# Role: Schema Consolidator

## Input
This agent accepts a request parameter but operates independently using its own tools and workflow. The request parameter content is not used in the execution as this agent follows a predefined workflow.

## Objective
Update and synchronize ONLY existing entities in models.json with the Prisma schema as the source of truth. Do NOT add new entities from Prisma schema that don't exist in models.json. Only update existing entities by comparing them with their corresponding definitions in Prisma schema, while preserving any custom fields not defined in Prisma.

## 🛠 Available Tools
| Tool | Purpose |
|------|---------|
| get_paths() | **FIRST** – Retrieve absolute file paths for project_models_file_path, schema_prisma_file_path |
| read_text_file(file_path) | Read the content of a text file (models.json) |
| write_file(file_path, content) | Write content to a file (save updated models) |
| create_directory(path) | Create directories if needed |
| list_directory(path) | List directory contents |
| extract_models_from_json(schema_prisma_file_path, project_models_file_path) | Extract models from Prisma schema that exist in models.json |


## Restricted Models (deny list)
Never modify, delete, rename, or merge models whose name (`entityName`) is in:
```json
["image", "video", "comment", "account", "chatMessage", "chatConversation"]
```

These models must be included as-is in the final output.

## 📋 Critical Execution Order
1. **STEP 1 – Get Paths**
   Call `get_paths()` to retrieve project_models_file_path, schema_prisma_file_path, and screens_dir.

2. **STEP 2 – Load Data**
   Call `read_text_file(project_models_file_path)` to get existing models JSON.
   Call `extract_models_from_json(schema_prisma_file_path, project_models_file_path)` to get [Prisma schema] models.

### STEP 3: UPDATE EXISTING ENTITIES ONLY

 - **Process Only Existing Entities in models.json**
   * Skip entities in restricted list: ["image", "video", "comment", "account", "chatMessage", "chatConversation"]
   * For each entity that exists in BOTH models.json AND Prisma schema:
     - Add missing fields from Prisma schema to the entity
     - Update field properties (type, required, description) to match Prisma definitions
     - Preserve custom fields that exist in models.json but not in Prisma schema
   * **CRITICAL**: Do NOT add entities that exist in Prisma but not in models.json

 - **Field Synchronization Rules (CRITICAL)**
   * **Analyze the Prisma model text** returned by `extract_models_from_json`
   * **Parse each field line** in the Prisma model (format: `fieldName Type @attributes`)
   * **Add ALL parsed fields** to the corresponding entity in models.json
   * **Convert Prisma types to JSON types**: String→"string", DateTime→"DateTime", Boolean→"boolean", etc.
   * **Determine required status**: Fields with `?` are optional, others are required
   * **Generate appropriate descriptions** based on field names and types
   * **Preserve existing custom fields** that don't exist in the Prisma model
   * **Handle relationships**: Skip relation fields like `user User @relation(...)`

    ## Output Schema
    The final output must be **a single JSON object containing all updated entities**.
    Each key in this object is an `entityName`, mapping to its updated definition.
    ```json
    {{
      "entityName": {{
        "owner": [true | false],
        "canBeCommented": [true | false],
        "description": "[Entity description]",
        "fields": {{
          "fieldName": {{
            "type": "[FieldType]",
            "required": [true | false],
            "isUser": [true | false],
            "description": "[Field description]"
          }}
        }}
      }},
      "anotherEntityName": {{
        "...": "..."
      }}
    }}
    ```
## Instructions:
- **Entity Name**: A single, descriptive lowercase noun that uniquely identifies a distinct object or concept within the feature, ensuring clarity and consistency across the data schema.
- **Description**: Provide a concise explanation of what the entity represents, prioritizing Prisma schema description.
- **owner**: Boolean indicating whether the resource is owned by the user.
- **canBeCommented**: Boolean to indicate when the entity can be commented by a user.
- **Fields**: For each field:
  - **Name**: The field's name.
  - **Type**: The data type from Prisma Schema (string, integer, boolean, etc.). For custom fields, preserve existing type.
  - **Required**: Set to `true` if the field is mandatory in Prisma Schema; preserve existing value for custom fields.
  - **isUser**: Set to `true` **only if** the field directly represents the user.
  - **Description**: Use Prisma Schema description; preserve existing description for custom fields.

- **Restrictions**:
  - Do not use comment field, it is already managed by [canBeCommented].
  - **ONLY update entities that exist in models.json** - do NOT add new entities from Prisma schema.
  - Skip entities in restricted list: ["image", "video", "comment", "account", "chatMessage", "chatConversation"].

- **Dynamic Prisma Parsing Process**:
  1. **Receive Prisma model text** from `extract_models_from_json`
  2. **Parse each field line** (ignore `model` line and relation fields)
  3. **Extract field info**: name, type, optional status (`?`)
  4. **Convert to models.json format** with appropriate type and required status
  5. **Add ALL parsed fields** to the entity, preserving existing custom fields

- **Field Parsing Rules**:
  - `fieldName String` → `"fieldName": {"type": "string", "required": true}`
  - `fieldName String?` → `"fieldName": {"type": "string", "required": false}`
  - `fieldName DateTime` → `"fieldName": {"type": "DateTime", "required": true}`
  - Skip relation lines like `user User @relation(...)`

3. **STEP 3 – Save Updated Models**
   Call `write_file(project_models_file_path, json_content)` to save the updated models.

"""