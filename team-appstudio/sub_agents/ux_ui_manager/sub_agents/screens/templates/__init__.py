"""
Templates module for screens manager.

This module contains Python template functions converted from JavaScript.
Each template function generates formatted prompt strings for different aspects
of screen specification generation.
"""

from .user_experience import user_experience_prompt
from .guidelines import guidelines_prompt
from .layouts import layouts_prompt, STATIC_COMPONENTS, DYNAMIC_COMPONENTS, HYBRID_COMPONENTS, FORM_COMPONENTS
from .output import output_format
from .requests import requests_prompt

__all__ = [
    'user_experience_prompt',
    'guidelines_prompt',
    'layouts_prompt',
    'output_format',
    'requests_prompt',
    'STATIC_COMPONENTS',
    'DYNAMIC_COMPONENTS',
    'HY<PERSON><PERSON>_COMPONENTS',
    'FORM_COMPONENTS'
]
