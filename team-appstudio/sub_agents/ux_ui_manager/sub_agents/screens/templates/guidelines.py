def guidelines_prompt():
    """
    Generate guidelines prompt template.
    
    Returns:
        str: Guidelines prompt content
    """
    return """- Merge redundant or overlapping actions into a single logical sequence, ensuring clarity and cohesion.
- Organize and describe user actions and application events in sequential order.
- Ensure that all features are harmonized to present a consistent and efficient interaction flow for the user.
- Ensure consistency between the user experience flow and the screen layout.
- Ensure that each dataSchema, such as "comment", has its own independent CRUD requests and does not depend on other entities' requests.
- **Note on AI-Generated Content**: When generating content like images using AI, the request should update the parent entity rather than creating a separate entity for the generated content.
- Only use "AddProductButton" when [payment type] is "Cart" only.
- Use "MediaUploader" component to upload an image or video, the component already contains an "upload button."
- Consolidate all feature user experience data into a single unified flow.
- Use logical and sequential ordering for actions, avoiding duplication.
- **Ensure that all related input fields for a form are grouped within the same element's `inputs` array in the layout. Do not create separate elements for each input field.**
- `[action]` has only two fields: `[type]` and `[element or path or modal or request]`.

- If [items] is true, indicating that a list is being displayed for an entity of type:
  - "video" or "image", display the only following attributes for each item in [component]: thumbnailUrl (as an image), objectName (as text), and createdAt (as the creation date).
  - "audio", display the only following attributes for each item in [component]: objectName (as text), and createdAt (as the creation date).

- **Workflow**:
  - Workflow handles any creation or upscaling of a resource.
  - When navigating on a resource page, first go through "/workflow:id". It checks if the resource generation is fully completed before displaying the item.
  - **/workflow/:id**:
    - For any generation of resource navigate to "/workflow/:id" path.
    - A path to navigate on a page to display/check the workflow process.
    - It handles the generation process of a resource.
    - On success, it will be redirected to the resource page (e.g "/article/:id").
    - If [when][type] is "list:item", path navigate to "/workflow/:id".

- **Agent/Chatbot**
  - No workflow required when generating an agent/chatbot.
  - For chat interfaces, use the "ChatBot" component which handles all chat functionality including message display, sending, typing indicators, settings, and theme management. No additional components needed for chat functionality.
  - Use the "ChatConversation" entity to retrieve the messages.

- **Transcription**:
  - "/workflow/:id" should be used for displaying the workflow process.
  - Use "ReactMarkdown" component to display transcription text.
  - Use [Workflow/AI endpoints] to access data.
  
"""
