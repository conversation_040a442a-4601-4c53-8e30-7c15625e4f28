# Component constants
STATIC_COMPONENTS = [
    "Button",
    "Switch",
    "Avatar",
    "Badge",
    "Alert",
    "Toggle",
    "ToggleGroup",
    "AspectRatio",
    "Text",
    "AddProductButton",
    "Video",
    "MediaUploader",
]

DYNAMIC_COMPONENTS = [
    "Button",
    "Switch",
    "Avatar",
    "Badge",
    "Alert",
    "Toggle",
    "ToggleGroup",
    "AspectRatio",
    "Text",
    "Image",
    "AddProductButton",
    "Video",
    "MediaUploader",
    "Chatbot",
    "AudioRecorderUploader",
    "AudioPlayer",
    "ReactMarkdown",
]

HYBRID_COMPONENTS = ["Button"]

FORM_COMPONENTS = [
    "TextField",
    "TextArea",
    "Select",
    "ComboBox",
    "Checkbox",
    "CountryPicker",
    "DatePicker",
    "Password",
    "Button",
]


def layouts_prompt():
    """
    Generate layouts prompt template.

    Args:
        payment: Payment object containing payment configuration

    Returns:
        str: Formatted layouts prompt
    """
    static_components_str = ", ".join(STATIC_COMPONENTS)
    dynamic_components_str = ", ".join(DYNAMIC_COMPONENTS)
    hybrid_components_str = ", ".join(HYBRID_COMPONENTS)
    form_components_str = ", ".join(FORM_COMPONENTS)

    return f"""Define each layout on the screen and include the following:

- **Layout Type**: Specify Vertical, Horizontal, or Center to organize groups of elements.

- **Groups**: Organize elements into cohesive groups with the following details:
  - **Group Name**: Provide a PascalCase name (e.g., "Header").
  - **Description**: Describe the purpose or functionality of the group.

  - **Requirements**:
    - Each group must have at least 2-3 elements.

  - **Elements**:
    - **Name**: Provide the component name in PascalCase.
    - **Description**: Specify the role or purpose of the element.
    - **dataSource**: The `requestId` that provides the data for this element.
    - **Items**: Is true if the element displays multiple similar items in a list or group.
      - This excludes dropdowns, search, forms, or interactive filters.
      - Do not include when the element has inputs.

    - **dataRequest**:
      - **type**: Specifies the HTTP method used for the request (e.g., GET, POST).
      - **path**: The API endpoint path where the request is sent.
      - **fields**:
        - An object mapping component names to the fields from the request result that should be used to populate the component.

      - **Requirements**:
        - Only include `dataRequest` if it corresponds to an actual request defined in the `Requests config`. If no request exists, omit the dataRequest field entirely.
        - Set to "objectName" even if not referenced in `dataResult` when `element` is a list.
        - Name should not be identical to ComponentName.

    - **Components**:
      - **Requirements**:
        - Only if `paymentType` is "cart", add the "AddProductButton" component in `components` only for a **list of items** of entity [paymentEntity]. The "AddProductButton" is responsible for adding items to the cart.
      - If items is true, indicating that a list is being displayed for an entity, set all component with eventId.

      - There's three types of components: static component, dynamic component and hybrid component:

        - **Static Component**:
          - **eventId**: A unique identifier for components tied to user interactions. If none, create a unique identifier.
          - **component**: From the list only: {static_components_str}.
          - **texts**: List in an object the texts that are not included in `dataResult` from the request. Do not create placeholders.
            - **textId**: Unique identifier of the text
            - **text**: text to display

          - **Requirements**:
            - Use texts property for static text, use 'texts' property.
            - Use "MediaUploader" component to upload an image or video or when clicked on a button to upload, the component already contains an "upload button."

          - **Constraints**:
            - Do not use 'data' property.

        - **Dynamic Component**:
          - **eventId**: A unique identifier for components tied to user interactions. If none, create a unique identifier.
          - **component**:  From the list: {dynamic_components_str}.
          - **data**: Used to directly reference a field from the data result.
            - **Criteria**:
              - If `items` is true and `element` gets data from a workflow, set `data` to "objectName" even if not referenced in dataResult.
              - When a component is intended to display the user's name, use the "data" field as "user.name" instead of generic terms.

          - **Requirements**:
            - Use the 'data' property to display the data from the request.
            - Use request `dataResult` for direct field mapping.
            - Use "MediaUploader" component to upload an image or video or when clicked on a button to upload, the component already contains an "upload button."
            - Use exiting "Chatbot" component to display chat messages. The component already handles the input and display of messages.

          - **Constraints**:
            - Do not use 'texts' property.

        - **Hybrid Component**:
          - **eventId**: A unique identifier for components tied to user interactions. If none, create a unique identifier.
          - **component**: From the list: {hybrid_components_str}.
          - **data**: Used to directly reference a field from the data result. If component is "image" or "Video", use "url".
            - **Criteria**: If `items` is true and `element` gets data from a workflow, set `data` to "objectName" even if not referenced in dataResult.

          - **texts**: List in an object the texts that are not included in `dataResult` from the request. Do not create placeholders.
            - **textId**: Unique identifier of the text
            - **text**: text to display

          - **Requirements**:
            - Use the 'data' property to display the data from the request.
            - Use request `dataResult` for direct field mapping.
            - Use texts property for static text, use 'texts' property.

    - **Inputs**:
      - Create an array input field to create a form component:
        - **eventId**: A unique identifier for components tied to user interactions. If none, create a unique identifier.
        - **componentName**: From the list: {form_components_str}.
        - **fieldName**: The name of the input field. Ensure is the same as the field name in `dataSchemas`.

      - **Requirements**:
        - A Button component must be included for form submission.
        - If a valid form is not present, move any standalone buttons to components instead.
        - **If an element represents a form, all its inputs must be grouped together under the same element in the inputs array. Do not create separate elements for each input field.**

    - **IMPORTANT**:
      1. If the element represents a form, all its inputs (e.g., TextField, TextArea, Button) must be grouped within the same element under the inputs array.
      2. Every element must include either a "components" array or an "inputs" array. At least one of these is required.
      3. An Element cannot have both components and inputs at the same time.
      4. GroupName cannot be the same as element name.
      5. If `user experience` has a when event that has an element, it must be included in the layouts.
      6. Ensure that buttons not associated with forms are placed within the 'components' array instead of the 'inputs' array.
      7. Ensure that "requestId" values used in `dataSource` fields exactly match those defined in the requests section.
      8. Ensure unique name for each group name and element name.
      9. Display a single field for the [paymentEntity] for description.
      10. Only if `paymentType` is "cart", add the "AddProductButton" component in `components` only for a **list of items** of entity [paymentEntity]. The "AddProductButton" is responsible for adding items to the cart.
      11. If `item` is true, indicating that a list is being displayed for an entity of type video or image, display the following only the attributes for each item: thumbnailUrl (as an image), objectName (as text), and createdAt (as the creation date). Even if objectName is not referenced in dataResult.
      12. Use "MediaUploader" component to upload an image or video, the component already contains an "upload button."
      13. For chat interfaces, use the "ChatBot" component which handles all chat functionality including message display, sending, typing indicators, settings, and theme management. No additional components needed for chat functionality.
      14. Components description:
        - "AudioRecorderUploader": Buttons to record or upload audio.
        - "AudioPlayer": Displays audio.
        - "ChatBot": Handles all chat functionality including message display, sending, typing indicators, settings, and theme management.
        - "ReactMarkdown": To display markdown text.
      15. Do not use "eventId" on "Image" component.
"""
