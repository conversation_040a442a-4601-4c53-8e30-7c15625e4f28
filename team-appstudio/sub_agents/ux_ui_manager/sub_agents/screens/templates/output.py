def output_format():
    """
    Generate output format template.

    Returns:
        str: Formatted output template
    """
    return """{{
    "screenSpecs": {{
      "name": "[Unique Name for the Screen, (max 2-3 words)]",
      "type": "[Type of Screen: 'page' or 'modal']",
      "path": "[feature path]",
      "description": "[description]",
      "userExperience": [
        {{
          "who": "[User/App]",
          "if": {{
            "[identifierName]": {{
              "[identifierFunction]": false
            }}
          }},
          "action": {{
            "type": "[Action Type]",
            "element": ["[List of elements]"],
            "request": {{
              "requestId": "[unique identifier]",
              "notify": {{
                "target": "[me or owner]",
                "title": "[title on action]",
                "message": "[message on action]",
                "link": "[redirection path]"
              }}
            }}
          }},
          "where": "[Screen Path]"
        }},
        {{
          "who": "[User/App]",
          "action": {{
            "type": "[Action Type]",
            "path": "[Path]",
            "modal": "[modal]"
          }},
          "when": {{
            "type": "[Action Type]",
            "[element/request]": ["[List of elements/requests]"]
          }},
          "where": "[Screen Path of the Triggering User Action]"
        }}
      ],
      "dataSchemas": "{{[data_schemas_json]}}",
      "requests": {{
        "[requestId]": {{
          "useAi": {{
            "generationType": ["[type of the output: text, image, video, audio]"],
            "role": "[role of the ai]",
            "objective": "[objective of the ai]",
            "context": ["field name"],
            "output": [
              {{
                "[field name]": "[field name]",
                "outputDescription": "[description of the field]"
              }}
            ]
          }},
          "useWorkflow": {{
            "tasks": [
              {{
                "name": "[function name]",
                "uiDescription": "[ui description]",
                "dependencies": ["list of dependencies"],
                "next": [
                  {{
                    "name": "[function name]",
                    "uiDescription": "[ui description]",
                    "dependencies": ["list of dependencies"],
                    "connect": ["List of function name"]
                  }}
                ]
              }}
            ]
          }},
          "dataResult": {{
            "[fieldName]": "[value]"
          }},
          "type": "[POST/GET/PATCH/DELETE]",
          "params": {{
            "[fieldName]": "[field value]"
          }},
          "body": {{
            "[fieldName]": "[field value]"
          }},
          "notifyLink": "[path to notify]",
          "accessCredit": {{
            "consumes": {{
              "[resource]": "[Quantity of resource]"
            }}
          }},
          "path": "[request path]",
          "onSuccess": {{
            "actionType": "[load/navigate]",
            "[requestId/path]": "[request id for load/relative path for navigate]"
          }}
        }}
      }},
      "layouts": [
        {{
          "layoutType": "[Vertical | Horizontal | Center]",
          "groups": [
            {{
              "groupName": "[Name of the group]",
              "layoutType": "[Vertical | Horizontal | Center]",
              "description": "[Description of the group]",
              "elements": [
                {{
                  "name": "[Element Name]",
                  "description": "[Role or function of the element]",
                  "dataSource": "[request id]",
                  "inputs": [
                    {{
                      "eventId": "[unique identifier]",
                      "componentName": "[Form Component]",
                      "fieldName": "[Field Name]"
                    }},
                    {{
                      "eventId": "submit_button",
                      "componentName": "Button",
                      "type": "submit"
                    }}
                  ],
                  "dataRequest": {{
                    "type": "[GET/POST/PATCH/DELETE]",
                    "path": "[request path]",
                    "fields": {{
                      "[ComponentName]": "[field from request result]"
                    }}
                  }}
                }},
                {{
                  "name": "[Element Name]",
                  "description": "[Role or function of the element]",
                  "dataSource": "[request id]",
                  "items": "[boolean value]",
                  "dataRequest": {{
                    "type": "[GET/POST/PATCH/DELETE]",
                    "path": "[request path]",
                    "fields": {{
                      "[ComponentName]": "[field from request result]"
                    }}
                  }},
                  "components": [
                    {{
                      "eventId": "[unique identifier]",
                      "component": "[component Name]",
                      "texts": {{
                        "[textId]": "[static text]"
                      }}
                    }},
                    {{
                      "component": "[component Name]",
                      "data": "[dynamic data key]"
                    }}
                  ]
                }}
              ]
            }}
          ]
        }}
      ]
    }}
  }}"""
