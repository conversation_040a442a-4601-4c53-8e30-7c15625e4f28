def requests_prompt():
    """
    Generate requests prompt template.
    """
    return """Based on the [requests information] in [additional information], create a unified **requests** array containing dynamic data for each request.

Each request includes:

- **requestId**: Unique identifier.
- **isArray**: Boolean indicating if the response returns a list.
- **useAi**:
  - **generationType**: Type of AI output — "text", "image", "video", "audio" — or combos ["text","image"], ["text","video"], ["text","audio"].
  - **context**: One entity field from [body] (not "name") to guide image/video/audio generation.
  **Text** (only if generationType is ["text"] or ["text","image"], ["text","video"], ["text","audio"]):
    - **role**: Role the AI plays.
    - **objective**: Goal AI is fulfilling.
    - **output**: Required fields of [paymentEntity]:
      - **field**: Name of AI-generated field.
      - **outputDescription**: Brief description of what the field represents.
    **Requirements**:
      - Include all fields for "text" and "image".
      - If resource needs a price, include "price" in [output].

- **useWorkflow**: <PERSON><PERSON>an — whether task follows sequential steps.

- **dataResult**: Data to return based on [dataSchemas].
  - Required field.
  - If array: `"items":[{"id":"itemId1","value":"Value1"},{"id":"itemId2","value":"Value2"}]`.
  - For **tables only**, use `{cols, rows}` structure:
    ```json
    "[fieldName]":{"cols":[{"title":"[Column Title]","field":"[Field Name]"}],
                   "rows":[{"[Field Name]":"[Value]"}]}
    ```

- **type**: HTTP method:
  - **POST**: For Find, Read, Create.
  - **GET**: Only for Read by "Id".
  - **PATCH**: For updates.
  - **DELETE**: For deletes by "Id".
  - If generationType = "image", use PATCH (to update parent entity).

- **params** (optional): Input parameters.
  - **fieldName**: From [dataSchemas fields].
  - **value**: From [dataResult].
  **Criteria**:
    - Use only for:
      - Read by "Id" (`fieldName="id"`)
      - Delete by "Id" (`fieldName="id"`)
    - Never use for Find, Create, Update.

- **path**: API endpoint ([entityName] always singular).
  1. Chat/Agent endpoints:
     ```
     GET    /chat/chatConversation/:id
     POST   /chat/chatConversation/create
     POST   /chat/chatMessage/send
     POST   /chat/chatMessage/find
     POST   /chat/chatConversation/find
     PATCH  /chat/chatConversation/update/:id
     DELETE /chat/chatConversation/delete/:id
     DELETE /chat/chatConversation/clear/:id
     ```
  2. Workflow/AI endpoints:
     ```
     GET    /workflow/[entityName]/:id
     POST   /workflow/[entityName]/create
     POST   /workflow/[entityName]/find
     POST   /workflow/[entityName]/transcribe
     POST   /workflow/[entityName]/upscale
     POST   /workflow/[entityName]/edit
     PATCH  /workflow/[entityName]/update/:id
     DELETE /workflow/[entityName]/delete/:id
     ```
  **Requirements**:
    - Use singular entityName (e.g., "post").
    - Set useAi: true for upscale.

- **body**: Only for POST/PATCH.
  - **IMPORTANT for AI requests**:
    - If generationType = "text", body has only one trigger field (e.g., "topic").
  - **fieldName**: From [dataSchemas fields].
  - **value**: From [dataResult]; generate non-AI fields as needed.
  **Requirements**:
    - Update + AI:
      - If image → include only image field.
      - If audio → include only audio field.
      - If text or combos → include only one trigger field.
    - Create (non-AI):
      - Include all `"required": true` fields in [dataSchemas] except "userId", "user", "createdAt".
      - If dependent on another entity:
        - **objectId**: Id of required entity.
        - **objectType**: Name of required entity.
      - Exclude "userId", "user", "createdAt".

- **notifyLink** (optional): Include if [userExperience] action has [notify]. Match the [link] path.

- **accessCredit**: Tracks AI credit use.
  - **resource**: "text" | "image" | "video" | "audio".
  - **quantity**: Default 1.
  - Reference table:
    ```
    const accessCreditNeeded = {
      generate: { video:{text:1,video:1}, image:{text:1,image:1}, audio:{text:1,audio:1}, text:{text:1} },
      upscale:  { video:{video:1}, image:{image:1} },
      transcribe:{ audio:{audio:1} },
      edit:     { image:{image:1}, video:{video:1} },
    };
    ```
  - Include only if using AI.
  - Sum for chained ops (e.g., generate+upscale → {"text":1,"video":2}).

- **onSuccess** (optional): For explicit follow-up actions.
  - **actionType**: "load" | "navigate".
  - If "load": include requestId (must match another request).
  - If "navigate": include path.
  - A request cannot call itself.

**Global requirements**:
- Create requests only if [requests config] is provided.
- Only include requests referenced in at least one [userExperience] action via requestId.
- Remove unused requests to avoid redundancy.
- Avoid redundant API calls.
"""
