def user_experience_prompt():
    """
    Generate user experience prompt template.
    """
    return """### User Experience

- **Who**: Actor performing the action ("User" or "App").
---
- **If Conditions**:
  - Use [if] only when:
    1. **who** = "App"
    2. Action depends on a condition for:
       - Buying a subscription
       - Making a one-time payment to access a resource
       - Buying credits
  - Only include if [paymentType] is "subscription":
    - **identifierName**: Store providing access to stored data.
      - Use "useAuthStore" to get user account details (including subscription info).
    - **identifierFunction**: Function in **identifierName**.
      - Use "hasSubscription" to check if the user has an active subscription (returns boolean).
  - If [isCreditUsage] = false and [Payment Restriction Type] is "credit" OR "one-time-payment" OR "cart":
    - **identifierName**: Resource/entity that must be purchased for access.
    - **identifierFunction**:
      - Use "hasAccess" to verify access to a single [paymentEntity] by its ID.
      - Apply only when [where] path includes an identifier (single-item view).
      - Return boolean, default = false.
      - **IMPORTANT**: Never use "hasAccess" on list views or multi-item paths (e.g., "/posts", "/articles").
---
- **Action**: Type of action and the element involved.

  **Type**:
  - **User**:
    1. Forms → only: fill, select, submit (onSubmit button), type (onChange function)
    2. Other components → only: click, type
  - **App**:
    1. Modals → only: open, close
    2. Pages → only: navigate, redirect
    3. Forms → only: validate, send
    4. Requests → only: load, send
       - **load**: when page loads
       - **send**: on user action

  - Include the **[request]** field only when **type** is "load" or "send":
    - **Request**:
      - **requestId**: Unique request identifier.
      - **notify**: Notification triggered by app/user actions; set null if none.
        - **target**: "me" or "owner"
          - **me**: Current logged-in user (personal/account updates)
          - **owner**: Entity creator/manager (resource updates)
        - **title**: Short headline when action completes.
        - **message**: Detailed context for notification.
        - **link**: URL to resource/page.
          - Must match an existing [path].

  - If action type is not "load" or "send", include **element** instead:
    - **Element**:
      - **Type**: Element type being interacted with.
      - **eventId**: Unique ID matching the corresponding Layouts → Elements component.
---
- **Where**: Screen where the action happens.
---
- **When**:
  - **User**: Describe the trigger.
  - **App**: Mirror triggering User action (type, element type, eventId), except for navigation actions ("navigate", "redirect").
"""
