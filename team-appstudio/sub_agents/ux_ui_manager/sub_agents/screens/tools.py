"""
Multi-Agent Screen Specification Management System

This module provides utilities and tools for managing screen specifications,
data models, and feature generation across multiple specialized agents.

Agents:
- Screen Specs Generator: Creates screen specifications from feature definitions
- Schema Prisma Generator: Applies code diffs and field transformations
- Schema Consolidator: Consolidates and merges schema models
- Model Updater: Updates models from screen specifications
"""

import os
import re
import json
import shutil
from functools import wraps
from typing import Any, Dict, List, Union
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters
from google.adk.tools.base_tool import BaseTool
from google.adk.tools.function_tool import FunctionTool
from google.adk.tools.agent_tool import AgentTool


# ============================================================================
# CORE UTILITIES - Path Management and Validation
# ============================================================================

def validate_paths(required_keys: List[str], paths: Dict[str, str]) -> None:
    """
    Validates that required path keys exist and creates necessary directories.

    Ensures required path keys exist in the dict and that directories/files exist,
    creating output directories if needed. Raises clear errors early instead of
    failing deep inside operations.

    Args:
        required_keys: List of path keys that must exist in paths dict
        paths: Dictionary mapping path keys to actual file/directory paths

    Raises:
        ValueError: If any required keys are missing from paths dict
    """
    missing = [k for k in required_keys if k not in paths]
    if missing:
        raise ValueError(f"Missing required path keys: {', '.join(missing)}")

    # Safety: prevent accidental writes to filesystem root or outside project
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..", "..", ".."))
    for key in required_keys:
        p = paths[key]
        if not isinstance(p, str) or p.strip() == "":
            continue
        # Normalize potential route-like inputs (e.g., "/create-post")
        pn = p.lstrip("/") if os.path.isabs(p) and not p.startswith(project_root) else p

        # If it looks like a file, ensure parent dir; else ensure directory
        target_path = pn
        parent = os.path.dirname(target_path) or "."
        # Rebase to project root if path is not absolute
        if not os.path.isabs(target_path):
            target_path = os.path.join(project_root, target_path)
            parent = os.path.dirname(target_path) or "."

        # Final guard: ensure we're staying under project_root
        target_path = os.path.abspath(target_path)
        parent = os.path.abspath(parent)
        if not target_path.startswith(project_root):
            # Skip creating directories outside project boundary
            continue

        if target_path.endswith((".json", ".prisma")):
            os.makedirs(parent, exist_ok=True)
        else:
            os.makedirs(target_path, exist_ok=True)

def require_paths(*required_keys: str):
    """
    Decorator that validates required paths when function is called
    with positional string arguments matching required_keys order or keyword arguments.
    """
    def deco(fn):
        @wraps(fn)
        def wrapper(*args, **kwargs):
            # Map positional args to the required_keys
            paths = dict(zip(required_keys, args))

            # Also check for keyword arguments that match required_keys
            for key in required_keys:
                if key in kwargs:
                    paths[key] = kwargs[key]

            validate_paths(list(required_keys), paths)
            return fn(*args, **kwargs)
        return wrapper
    return deco

def get_paths() -> Dict[str, str]:
    """
    Get standardized absolute paths for the application structure.

    Returns absolute paths for all directories and files used across agents.
    Base directory is calculated relative to this file's location.

    Returns:
        Dictionary mapping logical path names to absolute file paths
    """
    base_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..", "..", ".."))
    return {
        "root": base_dir,
        "specifications_path": os.path.join(base_dir, "specs", "specifications.json"),
        "routing_app_path": os.path.join(base_dir, "specs", "routingApp.json"),
        "pages_dir": os.path.join(base_dir, "specs", "pages"),
        "modals_dir": os.path.join(base_dir, "specs", "modals"),
        "screens_dir": os.path.join(base_dir, "specs", "screens"),
        "project_models_file_path": os.path.join(base_dir, "specs", "models.json"),
        "features_dir": os.path.join(base_dir, "specs", "features"),
        "requests_file_path": os.path.join(base_dir, "specs", "requests.json"),
        "schema_prisma_file_path": os.path.join(base_dir, "backend", "api-starter", "prisma", "schema.prisma"),
        "consolidated_data_schemas_file_path": os.path.join(base_dir, "specs", "consolidated_data_schemas.json"),
        "collected_data_schemas_file_path": os.path.join(base_dir, "specs", "collected_data_schemas.json"),
        "features_config_path": os.path.join(
            base_dir, "sub_agents", "ux_ui_manager", "sub_agents", "screens_manager", "templates", "features_config.py"
        ),
        "routing_app_path": os.path.join(base_dir, "specs", "routingApp.json"),
        "feature_output_folder": os.path.join(base_dir, "specs", "features"),
        "modification_report_path": os.path.join(base_dir, "specs", "modification_report.json"),
        "navigation_file_path": os.path.join(base_dir, "specs", "navigation.json"),
        "front_end_navigation_file_path": os.path.join(base_dir, "frontend", "front-starter", "src", "configs", "NavConfig.ts"),
        "diff_file_path": os.path.join(base_dir, "specs", "diffs.diff"),
        "ai_generated_notification_config_path": os.path.join(base_dir, "specs", "ai_generated_notification_config.md"),
        "generated_notification_config_path": os.path.join(base_dir, "backend", "api-starter", "src", "shared", "config", "notification.config.ts"),
        "notification_methods_path": os.path.join(base_dir, "backend", "api-starter", "src", "shared", "notification", "notifications.ts"),
        "home_routes_path": os.path.join(base_dir, "specs", "homeRoutes.json"),
        "home_config_path": os.path.join(base_dir, "frontend", "front-starter", "src", "configs", "HomeConfig.ts"),
        "locales_dir": os.path.join(base_dir, "frontend", "front-starter", "src", "locales"),
        "ai_models_path": os.path.join(base_dir, "backend", "api-starter", "src", "shared", "ai", "ai.model.ts")
    }

# ============================================================================
# CORE UTILITIES - File Operations
# ============================================================================

def list_files(directory: str) -> List[str]:
    """
    List all files in a directory (non-recursive).

    Args:
        directory: Path to directory to list

    Returns:
        List of filenames (not full paths) in the directory
    """
    if not os.path.isdir(directory):
        return []
    return [f for f in os.listdir(directory) if os.path.isfile(os.path.join(directory, f))]

def read_json(file_path: str) -> Any:
    """
    Read and parse a JSON file.

    Args:
        file_path: Path to JSON file

    Returns:
        Parsed JSON content (dict, list, etc.)
    """
    with open(file_path, "r", encoding="utf-8") as f:
        return json.load(f)

def write_json(file_path: str, data: Any) -> None:
    """
    Write data to a JSON file with proper formatting.

    Creates parent directories if they don't exist.

    Args:
        file_path: Path where JSON file should be written
        data: Data to serialize to JSON
    """
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(data, f, indent=2, ensure_ascii=False)

def read_text(file_path: str) -> str:
    """
    Read text file content.

    Args:
        file_path: Path to text file

    Returns:
        File content as string
    """
    with open(file_path, "r", encoding="utf-8") as f:
        return f.read()

def write_text(file_path: str, content: str) -> None:
    """
    Write text content to file.

    Creates parent directories if they don't exist.

    Args:
        file_path: Path where file should be written
        content: Text content to write
    """
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(content)

def move_file(src: str, dst: str) -> None:
    """
    Move file from source to destination.

    Creates destination directory if it doesn't exist.

    Args:
        src: Source file path
        dst: Destination file path
    """
    os.makedirs(os.path.dirname(dst), exist_ok=True)
    shutil.move(src, dst)

def delete_folder(directory: str) -> None:
    """
    Delete folder and recreate it empty.

    Args:
        directory: Directory path to delete and recreate
    """
    if os.path.isdir(directory):
        shutil.rmtree(directory)
    os.makedirs(directory, exist_ok=True)


# ============================================================================
# CORE UTILITIES - String and Data Processing
# ============================================================================

def format_path(value: str, frm: str = "/", to: str = "_") -> str:
    """
    Format path string by replacing characters.

    Used for converting paths to safe filenames.

    Args:
        value: String to format
        frm: Character to replace (default: "/")
        to: Replacement character (default: "_")

    Returns:
        Formatted string
    """
    return value.replace(frm, to)

# ============================================================================
# AGENT: SCREEN SPECS GENERATOR
# Tools for generating screen specifications from features
# ============================================================================

@require_paths("routing_app_path")
def extract_formatted_model_paths(routing_app_path: str) -> List[str]:
    """
    Extract keys (paths) from models JSON and format them into safe strings.

    Args:
        models_json_path: Path to the JSON file containing models with paths as keys.

    Returns:
        List of formatted path strings.
    """
    data = read_json(routing_app_path)  # Use existing helper
    return [format_path(path, frm="/", to="_") for path in data.keys()]

@require_paths("routing_app_path")
def update_routing_app(routing_app_path: str, path: str, screen_file_path: str) -> None:
    """
    Update routing application configuration with screen metadata.

    Reads screen specification and updates the routing app JSON with
    the screen's name and type information.

    Args:
        routing_app_path: Path to routingApp.json file
        screen_key: Key identifying the screen in routing
        screen_file_path: Path to the screen specification file

    Raises:
        RuntimeError: If updating routing app fails
    """
    try:
        screen_key = format_path(path, frm="_", to="/")
        screen_spec = read_json(screen_file_path)
        name = screen_spec.get("screenSpecs", {}).get("name")
        t = screen_spec.get("screenSpecs", {}).get("type")
        routing_app = read_json(routing_app_path)
        routing_app[screen_key] = {**routing_app.get(screen_key, {}), "name": name, "type": t}
        write_json(routing_app_path, routing_app)
        print(f'Updated routing app for screen "{screen_key}" with name and type.')
    except Exception as e:
        raise RuntimeError(f"Error updating routing app for screenKey={screen_key}, filePath={screen_file_path}: {e}") from e


def get_screens_specs_generator_tools() -> List[Union[BaseTool, MCPToolset, FunctionTool]]:
    """
    Get tools required for the Screen Specs Generator agent.

    Returns:
        List of tools including file system access, path utilities, and screen operations
    """
    return [
        FunctionTool(func=get_paths),
        *file_system_toolset(),
        FunctionTool(func=extract_formatted_model_paths),
        FunctionTool(func=format_path),
        FunctionTool(func=update_routing_app)
    ]


# ============================================================================
# AGENT: SCHEMA PRISMA GENERATOR
# Tools for applying code diffs and field transformations
# ============================================================================
def update_object_type(schema_prisma_file_path: str, project_models_file_path: str) -> None:
    """
    Update ObjectType enum in schema with available model types.

    Finds the ObjectType enum in the schema and updates it with
    predefined types plus all model keys, sorted alphabetically.

    Args:
        schema: Schema string containing ObjectType enum
        models: Dictionary of available models

    Returns:
        Updated schema string with new ObjectType enum values
    """
    schema = read_text(schema_prisma_file_path)
    models = read_json(project_models_file_path)
    object_types = set(["news", "comment", "profile", "audio", *models.keys()])
    parts = schema.split("enum ObjectType")
    if len(parts) < 2:
        return schema
    relevant = parts[1]
    start = relevant.find("{")
    end = relevant.find("}")
    if start != -1 and end != -1:
        updated = "\n ".join(sorted(object_types))
        return parts[0] + f"enum ObjectType {{ \n {updated} \n}}" + relevant[end+1:]

    write_text(schema_prisma_file_path, schema)


def apply_diff(schema_prisma_file_path: str, diff_file_path: str) -> None:
    """
    Apply unified diff patches to code.

    Processes diff text with ORIGINAL/UPDATED sections and applies
    the changes to the provided code string.

    Args:
        schema_prisma_file_path: Path to the Prisma schema file
        diff_file_path: Path to the diff file with patches

    Returns:
        None - modifies the schema file in place
    """
    # Check if diff file exists
    if not os.path.exists(diff_file_path):
        raise FileNotFoundError(f"Diff file not found: {diff_file_path}. Make sure to call write_file to save the diff content before applying patches.")

    diff = read_text(diff_file_path)
    code = read_text(schema_prisma_file_path)
    # Define the regex to match the diff format
    regex = re.compile(r"<<<<<<< ORIGINAL\n(.*?)=======\n(.*?)>>>>>>> UPDATED", re.S)

    def escape_regexp(string: str) -> str:
        """Escape regex special characters for literal matching."""
        return re.sub(r"([.*+?^${}()|[\]\\])", r"\\\1", string)

    # Iterate over matches and apply changes
    for match in regex.finditer(diff):
        before, after = match.groups()
        if before.strip() == "":
            # Append the after string to the code
            code += f"\n{after}"
        else:
            # Replace the before string with the after string
            replacement_regex = escape_regexp(before)
            replacement_regex = re.sub(r"\r?\n", r"\\s+", replacement_regex)
            replacement_regex = replacement_regex.replace("\t", "")
            replace_regex = re.compile(replacement_regex)
            code = replace_regex.sub(after, code, count=1)  # replace once to mimic JS behavior

    write_text(schema_prisma_file_path, code)

def apply_credit_payment_updates(specifications_app_path: str, schema_prisma_file_path: str) -> str:
    """
    Update autoChargePlan and balance defaults in the Prisma schema
    if the specifications app contains a credit payment configuration.

    Args:
        updated_prisma: Current Prisma schema content as a string
        specifications_app_path: Path to specificationsApp.json

    Returns:
        Updated Prisma schema content as a string
    """
    project_config = read_json(specifications_app_path)

    # Find the first feature with a paymentType
    payment = next((f for f in project_config.get("features", []) if f.get("paymentType")), None)

    if (
        payment
        and payment.get("paymentType") == "credit"
        and "options" in payment
        and len(payment["options"]) > 0
    ):
        keys = list(payment["options"].keys())
        updated_prisma = read_text(schema_prisma_file_path)
        # Replace autoChargePlan default value
        updated_prisma = re.sub(
            r'autoChargePlan\s+String\s+@default\(".*?"\)',
            f'autoChargePlan      String  @default("{keys[0]}")',
            updated_prisma
        )

        # Replace balance default value
        credits_value = payment["options"][keys[0]].get("credits", 0)
        updated_prisma = re.sub(
            r'balance\s+Int\s+@default\(\d+\)',
            f'balance      Int           @default({credits_value})',
            updated_prisma
        )

    write_text(schema_prisma_file_path, updated_prisma)
def get_patches_generator_tools() -> List[Union[BaseTool, MCPToolset, FunctionTool]]:
    """
    Get tools required for the Patches Generator agent.

    Returns:
        List of tools for applying diffs, updating fields, and file operations
    """
    return [
        FunctionTool(func=apply_diff),
        FunctionTool(func=apply_credit_payment_updates),
        FunctionTool(func=update_object_type),
        FunctionTool(func=get_paths),
        *file_system_toolset(),
    ]


# ============================================================================
# AGENT: SCHEMA CONSOLIDATOR
# Tools for consolidating and merging schema models
# ============================================================================

def extract_models_from_json(schema_path: str, models_json_path: str) -> str:
    """
    Extracts only the models present in the models.json keys from a Prisma schema.

    Args:
        schema_path: Path to the Prisma schema file.
        models_json_path: Path to the models.json file.

    Returns:
        Filtered schema containing only the requested models as a string.
    """
    # Load models.json using existing helper
    models_data = read_json(models_json_path)

    # Convert keys to Prisma model names (capitalize first letter)
    models_to_extract = [key[0].upper() + key[1:] for key in models_data.keys()]

    # Read schema text using helper
    schema = read_text(schema_path)

    # Regex to match models
    pattern = re.compile(r"model\s+(\w+)\s*{[^}]*}", re.DOTALL)

    # Extract only required models
    extracted_models = [
        match.group(0).strip()
        for match in pattern.finditer(schema)
        if match.group(1) in models_to_extract
    ]

    return "\n\n".join(extracted_models)

def models_consolidator_tools() -> List[Union[BaseTool, MCPToolset, FunctionTool]]:
    """
    Get tools required for the Schema Consolidator agent.

    Returns:
        List of tools for file system operations and path management
    """
    return [
        FunctionTool(func=get_paths),
        *file_system_toolset(),
        FunctionTool(func=extract_models_from_json),
    ]


# ============================================================================
# AGENT: MODEL UPDATER
# Tools for updating models from screen specifications
# ============================================================================
def update_fields(entity: Dict[str, Any]) -> Dict[str, Any]:
    """
    Normalize entity fields with standardized user field handling and add standard timestamps.

    Transforms fields with isUser flag into standardized userId fields,
    adds standard createdAt/updatedAt timestamp fields,
    while preserving all other field definitions unchanged.

    Args:
        entity: Entity definition dictionary with fields

    Returns:
        Entity dictionary with normalized fields including timestamps
    """
    new_fields: Dict[str, Any] = {}
    for field, value in entity.get("fields", {}).items():
        if isinstance(value, dict) and value.get("isUser"):
            new_fields["userId"] = {
                "type": "String",
                "required": True,
                "isUser": True,
                "description": "Identifier of the user."
            }
        else:
            new_fields[field] = value

    # Add standard timestamp fields if not already present
    if "createdAt" not in new_fields:
        new_fields["createdAt"] = {
            "type": "DateTime",
            "required": True,
            "isUser": False,
            "description": "Timestamp when the record was created."
        }

    if "updatedAt" not in new_fields:
        new_fields["updatedAt"] = {
            "type": "DateTime",
            "required": True,
            "isUser": False,
            "description": "Timestamp when the record was last updated."
        }

    entity["fields"] = new_fields
    return entity


def collect_data_schemas_from_screens(
    screens_dir: str,
    collected_data_schemas_file_path: str
) -> List[Any]:
    """
    Collect data schemas from all screen specification files.

    Reads all JSON files in the screens directory, extracts dataSchemas
    from each screenSpecs object, and saves the collected schemas to a file.

    Args:
        screens_dir: Directory containing screen spec JSON files
        collected_data_schemas_file_path: Output file path for collected schemas

    Returns:
        List of dataSchemas objects found in screen specifications
    """
    data_schemas_list: List[Any] = []

    files = [f for f in list_files(screens_dir) if f.endswith(".json")]
    for filename in files:
        file_path = os.path.join(screens_dir, filename)
        try:
            screen_spec = read_json(file_path)
            data_schemas = (
                screen_spec.get("screenSpecs", {}).get("dataSchemas")
                if isinstance(screen_spec, dict) else None
            )
            if data_schemas is not None:
                data_schemas_list.append(data_schemas)
        except Exception:
            # Skip files that can't be parsed
            continue

    # Save results to output file
    write_json(collected_data_schemas_file_path, data_schemas_list)

    return data_schemas_list


def apply_updates_and_write_models(
    consolidated_data_schemas_file_path: str,
    project_models_file_path: str
) -> Dict[str, Any]:
    """
    Apply field updates to consolidated models and write final models file.

    Reads merged models from consolidated file, applies the update_fields
    transformation to each entity, removes comment fields, and writes
    the result to the final models file.

    Args:
        consolidated_data_schemas_file_path: Path to consolidated schema models JSON
        project_models_file_path: Destination path for final models JSON file

    Returns:
        Updated models dictionary
    """
    # Simple error handling - continue workflow even if JSON is corrupted
    try:
        merged_models: Dict[str, Any] = read_json(consolidated_data_schemas_file_path)
    except Exception as e:
        print(f"Warning: Could not read {consolidated_data_schemas_file_path}: {e}")
        print("Continuing with empty models...")
        merged_models = {}

    updated: Dict[str, Any] = {}
    for entity_key, entity_def in merged_models.items():
        if entity_key == "comment":
            continue
        updated[entity_key] = update_fields(entity_def)

    write_json(project_models_file_path, updated)
    return updated


def get_model_updater_tools() -> List[Union[BaseTool, MCPToolset, FunctionTool]]:
    """
    Get tools required for the Model Updater agent.

    Returns:
        List of tools for collecting schemas, applying updates, and file operations
    """
    return [
        FunctionTool(func=get_paths),
        *file_system_toolset(),
        FunctionTool(func=collect_data_schemas_from_screens),
        FunctionTool(func=apply_updates_and_write_models),
    ]


# ============================================================================
# AGENT: MANAGER
# Tools for orchestrating all agents and managing file operations
# ============================================================================
@require_paths("screens_dir", "project_models_file_path")
def update_screen_specs_from_models(screens_dir: str, project_models_file_path: str) -> None:
    """
    Synchronize screen specifications with updated models.

    Replaces dataSchemas in screen specifications with corresponding
    schemas from models.json when keys match (case-insensitive).

    Args:
        screens_dir: Directory containing screen specification files
        project_models_file_path: Path to models.json file

    Raises:
        RuntimeError: If updating screen specs fails
    """
    try:
        files = list_files(screens_dir)
        models = read_json(project_models_file_path)
        normalized = {k.lower(): v for k, v in models.items()}

        for file in files:
            file_path = os.path.join(screens_dir, file)
            screen_spec = read_json(file_path)
            schemas = screen_spec.get("screenSpecs", {}).get("dataSchemas")

            if not schemas:
                print(f'dataSchemas not found in "{file_path}". Skipping update for this screen.')
                continue

            updated = False
            for schema_key in list(schemas.keys()):
                nk = schema_key.lower()
                if nk in normalized:
                    screen_spec["screenSpecs"]["dataSchemas"][schema_key] = normalized[nk]
                    updated = True
                    print(f'Updated dataSchema "{schema_key}" in "{file_path}".')
                else:
                    print(f'Warning: dataSchema "{schema_key}" not found in models.')

            if updated:
                write_json(file_path, screen_spec)
                print(f'Successfully updated "{file_path}".')

    except Exception as e:
        raise RuntimeError(f"Error updating screen specs: {e}") from e


@require_paths("routing_app_path", "screens_dir")
def generate_screen_specs_from_routing(routing_app_path: str, screens_dir: str, features_dir: str) -> None:
    try:
        routing_app = read_json(routing_app_path)

        for screen_key, screen in routing_app.items():
            if screen_key == "/paywall":
                continue  # preserve original behavior

            screen_file_path = os.path.join(
                screens_dir,
                f"{format_path(screen_key, '/', '_')}.json"
            )

            # Fix call: pass features_dir first, then screen_key, then useIn
            features = get_feature_specs(features_dir, screen_key, screen.get("useIn", []))

            # Aggregates for this screen
            description_parts: List[str] = []
            ux_by_feature: Dict[str, Any] = {}
            req_by_screen: Dict[str, Any] = {}
            data_schemas: Dict[str, Any] = {}

            for feat in features:
                # Cache frequently used fields
                feat_name = feat.get("featureName")
                feat_ux_list = feat.get("userExperience", []) or []
                feat_requests = feat.get("requests", []) or []
                feat_data_schemas = feat.get("dataSchemas", {}) or {}
                screen_desc = feat.get("screenDescription")

                if screen_desc:
                    description_parts.append(str(screen_desc))

                # Collect UX items for this screen and the requestIds they reference
                where_requests: set = set()
                feature_ux_for_screen: List[Dict[str, Any]] = []

                for ux in feat_ux_list:
                    if ux.get("where") == screen_key:
                        # copy without 'where'
                        rest = {k: v for k, v in ux.items() if k != "where"}
                        req_id = (
                            rest.get("action", {})
                                .get("request", {})
                                .get("requestId")
                            or ""
                        )
                        if req_id:
                            where_requests.add(req_id)
                        feature_ux_for_screen.append(rest)

                if feature_ux_for_screen and feat_name:
                    ux_by_feature[feat_name] = feature_ux_for_screen

                if not where_requests:
                    continue  # no requests referenced on this screen for this feature

                # Index requests by id once (avoids O(n*m) lookups)
                req_by_id = {r.get("requestId"): r for r in feat_requests if r.get("requestId")}

                # Keep only requests actually referenced by UX for this screen
                for req_id in where_requests:
                    req = req_by_id.get(req_id)
                    if not req:
                        continue
                    req_by_screen[req_id] = req

                    schema_name = req.get("dataSchema")
                    if schema_name and schema_name in feat_data_schemas:
                        # Only include schemas that are actually used
                        data_schemas[schema_name] = feat_data_schemas[schema_name]

            screen_with_ux = {
                "screenSpecs": {
                    "path": screen_key,
                    "description": " ".join(description_parts).strip(),
                    "dataSchemas": data_schemas,
                    "userExperience": ux_by_feature,
                    "requests": req_by_screen,
                }
            }

            write_json(screen_file_path, screen_with_ux)
            print(f'Processed screen "{screen_key}" with user experiences.')

    except Exception as e:
        raise RuntimeError(f"Error generating screen specs from routing: {e}") from e

@require_paths("features_dir")
def get_feature_specs(features_dir: str, screen_key: str, use_in: List[str]) -> List[Dict[str, Any]]:
    """
    Build feature context specifications for a screen.

    Collects feature specifications that a screen uses, including user experience
    data, screen descriptions, requests, and data schemas.

    Args:
        features_dir: Directory containing feature JSON files
        screen_key: Key identifying the screen
        use_in: List of feature names that the screen uses

    Returns:
        List of feature specification dictionaries

    Raises:
        ValueError: If a feature is missing userExperience data
    """
    feature_specs: List[Dict[str, Any]] = []
    for feature_name in use_in:
        feature_path = os.path.join(features_dir, f"{feature_name}.json")
        feature = read_json(feature_path)
        ux = feature.get("feature", {}).get("userExperience", [])
        screen_desc = feature.get("feature", {}).get("screens", {}).get(screen_key)
        if len(ux) == 0:
            raise ValueError(f"Missing userExperience data for feature: {feature_name}")
        feature_specs.append({
            "featureName": feature_name,
            "userExperience": ux,
            "screenDescription": screen_desc,
            "requests": feature.get("feature", {}).get("requests", []),
            "dataSchemas": feature.get("feature", {}).get("dataSchemas", {}),
        })
    return feature_specs

@require_paths("pages_dir", "modals_dir")
def move_files_based_on_type(pages_dir: str, modals_dir: str) -> None:
    """
    Organize generated files by moving modals to appropriate directory.

    Moves JSON files from pages directory to modals directory if their
    screenSpecs type is 'modal'. Warns about unrecognized types.

    Args:
        pages_dir: Source directory containing page files
        modals_dir: Target directory for modal files

    Raises:
        RuntimeError: If file moving operation fails
    """
    try:
        files = list_files(pages_dir)
        for file in files:
            file_path = os.path.join(pages_dir, file)
            data = read_json(file_path)
            screen_type = (data.get("screenSpecs", {}).get("type") or "").lower()

            if screen_type == "modal":
                dest = os.path.join(modals_dir, file)
                move_file(file_path, dest)
                print(f'Moved "{file}" to output.modals.')
            elif screen_type != "page":
                print(f'Warning: File "{file}" has unrecognized type "{data.get("screenSpecs", {}).get("type")}".')

    except Exception as e:
        raise RuntimeError(f"Error moving files based on type: {e}") from e


def extract_key_from_path(path_str: str) -> str:
    """Extract resource key from path string."""
    if not path_str:
        return ""
    # Remove leading slash and extract first segment
    path_clean = path_str.lstrip("/")
    segments = path_clean.split("/")
    return segments[0] if segments else ""

def is_duplicate_request(request_list: List[Dict[str, Any]], request_data: Dict[str, Any]) -> bool:
    """Check if request data is already in the request list."""
    for existing_request in request_list:
        if (existing_request.get("type") == request_data.get("type") and
            existing_request.get("path") == request_data.get("path")):
            return True
    return False

def transform_screen_specs(spec: Dict[str, Any], request_list: Dict[str, Any]) -> None:
    """Transform screen specifications to extract requests and merge them into request list."""
    # If the spec or requests are missing, return early
    if not spec or "requests" not in spec:
        return

    # Extract requests from the spec
    requests = spec["requests"]

    # Iterate over each request ID
    for _, request in requests.items():
        # Extract the path string from the request, defaulting to an empty string if not present
        path_str = request.get("path", "")
        # Extract the resource key from the path string
        resource = extract_key_from_path(path_str)

        # If the resource is missing, log a warning and skip to the next request
        if not resource:
            print(f"Warning: Skipping request with empty resource for path: {path_str}")
            continue

        # If the resource is not yet in the request list, initialize it with an empty array
        if resource not in request_list:
            request_list[resource] = []

        # Construct the request data object
        request_data = {
            "type": request.get("type"),
            "path": request.get("path"),
            "useAi": request.get("useAi"),
            "useWorkflow": request.get("useWorkflow", {}),
            "params": request.get("params", {}),
            "body": request.get("body", {}),
            "dataResult": request.get("dataResult", {}),
            "notifyLink": request.get("notifyLink"),
        }

        # Add the request data to the request list if it's not a duplicate
        if not is_duplicate_request(request_list[resource], request_data):
            request_list[resource].append(request_data)

@require_paths("pages_dir", "modals_dir", "requests_file_path")
def extract_requests(
    pages_dir: str,
    modals_dir: str,
    requests_file_path: str
) -> Dict[str, Any]:
    """
    Extract and merge request specifications from all screen files.

    Reads all screen and modal specification files, extracts their requests
    and writes a merged requests.json file.

    Args:
        pages_dir: Directory containing page specification files
        modals_dir: Directory containing modal specification files
        requests_file_path: Output path for merged requests JSON

    Returns:
        Fully merged requests dictionary keyed by resource

    Raises:
        Exception: If request extraction fails
    """
    try:
        pages_files = [f for f in list_files(pages_dir) if f.endswith(".json")]
        modal_files = [f for f in list_files(modals_dir) if f.endswith(".json")]
        files = [(pages_dir, f) for f in pages_files] + [(modals_dir, f) for f in modal_files]

        request_list: Dict[str, Any] = {}

        for folder, filename in files:
            file_path = os.path.join(folder, filename)
            spec = read_json(file_path)
            # Transform screenSpecs dict into request list and merge into accumulator
            transform_screen_specs(spec.get("screenSpecs", {}), request_list)

        write_json(requests_file_path, request_list)
        return request_list

    except Exception as e:
        print("Failed to extract requests:", e)
        raise

# ============================================================================
# FILE SYSTEM TOOLSET
# MCP toolset for file system operations
# ============================================================================

def file_system_toolset() -> List[MCPToolset]:
    """
    Create file system toolset for MCP operations.

    Sets up the Model Context Protocol toolset for file system operations
    including reading, writing, and directory management within the
    team app studio directory structure.

    Returns:
        List containing configured MCPToolset for file operations
    """
    current_dir = os.path.dirname(os.path.abspath(__file__))
    team_app_studio_dir = os.path.abspath(os.path.join(current_dir, "..", "..", "..", ".."))

    return [MCPToolset(
        connection_params=StdioServerParameters(
            command='npx',
            args=[
                "-y",
                "@modelcontextprotocol/server-filesystem",
                team_app_studio_dir,
            ],
        ),
        tool_filter=["read_text_file", "write_file", "create_directory", "list_directory"]
    )]


# ============================================================================
# EXPORTS
# ============================================================================

# Export main tool functions for use by agents
__all__ = [
    "get_screens_specs_generator_tools",
    "get_patches_generator_tools",
    "get_model_updater_tools",
    "generate_screen_specs_from_routing",
    "move_files_based_on_type",
    "extract_requests",
    "update_screen_specs_from_models",
    "get_feature_specs",
    "transform_screen_specs",
]
