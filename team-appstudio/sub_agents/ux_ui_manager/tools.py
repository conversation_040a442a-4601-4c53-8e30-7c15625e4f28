"""
Feature Specification Designer Tools
===================================
"""

import os
from google.adk.tools.agent_tool import AgentTool
from typing import Dict, List
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters
from google.adk.tools.function_tool import FunctionTool
from google.adk.tools.mcp_tool.mcp_session_manager import StdioServerParameters, StreamableHTTPConnectionParams

def mcp_task_manager_toolset() -> MCPToolset:
    """Local context toolset for accessing company documents."""
    return MCPToolset(
        connection_params=StreamableHTTPConnectionParams(url="http://127.0.0.1:8000/mcp")
    )

def get_paths() -> Dict[str, str]:
    """
    Get standardized absolute paths for the application structure.

    Returns absolute paths for all directories and files used across agents.
    Base directory is calculated relative to this file's location.

    Returns:
        Dictionary mapping logical path names to absolute file paths
    """
    base_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))
    return {
        "root": base_dir,
        "specifications_path": os.path.join(base_dir, "specs", "specifications.json"),
        "routing_app_path": os.path.join(base_dir, "specs", "routingApp.json"),
        "pages_dir": os.path.join(base_dir, "specs", "pages"),
        "modals_dir": os.path.join(base_dir, "specs", "modals"),
        "screens_dir": os.path.join(base_dir, "specs", "screens"),
        "project_models_file_path": os.path.join(base_dir, "specs", "models.json"),
        "features_dir": os.path.join(base_dir, "specs", "features"),
        "requests_file_path": os.path.join(base_dir, "specs", "requests.json"),
        "schema_prisma_file_path": os.path.join(base_dir, "backend", "api-starter", "prisma", "schema.prisma"),
        "consolidated_data_schemas_file_path": os.path.join(base_dir, "specs", "consolidated_data_schemas.json"),
        "collected_data_schemas_file_path": os.path.join(base_dir, "specs", "collected_data_schemas.json"),
        "features_config_path": os.path.join(
            base_dir, "sub_agents", "ux_ui_manager", "sub_agents", "screens", "templates", "features_config.py"
        ),
        "routing_app_path": os.path.join(base_dir, "specs", "routingApp.json"),
        "feature_output_folder": os.path.join(base_dir, "specs", "features"),
        "modification_report_path": os.path.join(base_dir, "specs", "modification_report.json"),
        "navigation_file_path": os.path.join(base_dir, "specs", "navigation.json"),
        "front_end_navigation_file_path": os.path.join(base_dir, "frontend", "front-starter", "src", "configs", "NavConfig.ts"),
        "diff_file_path": os.path.join(base_dir, "specs", "diffs.diff"),
        "ai_generated_notification_config_path": os.path.join(base_dir, "specs", "ai_generated_notification_config.md"),
        "generated_notification_config_path": os.path.join(base_dir, "backend", "api-starter", "src", "shared", "config", "notification.config.ts"),
        "notification_methods_path": os.path.join(base_dir, "backend", "api-starter", "src", "shared", "notification", "notifications.ts"),
        "home_routes_path": os.path.join(base_dir, "specs", "homeRoutes.json"),
        "home_config_path": os.path.join(base_dir, "frontend", "front-starter", "src", "configs", "HomeConfig.ts"),
        "locales_dir": os.path.join(base_dir, "frontend", "front-starter", "src", "locales"),
        "ai_models_path": os.path.join(base_dir, "backend", "api-starter", "src", "shared", "ai", "ai.model.ts")
    }

# ============================================================================
# FILE SYSTEM TOOLSET
# MCP toolset for file system operations
# ============================================================================

def file_system_toolset() -> List[MCPToolset]:
    """
    Create file system toolset for MCP operations.

    Sets up the Model Context Protocol toolset for file system operations
    including reading, writing, and directory management within the
    team app studio directory structure.

    Returns:
        List containing configured MCPToolset for file operations
    """
    current_dir = os.path.dirname(os.path.abspath(__file__))
    team_app_studio_dir = os.path.abspath(os.path.join(current_dir, "..", "..", "..", ".."))

    return [MCPToolset(
        connection_params=StdioServerParameters(
            command='npx',
            args=[
                "-y",
                "@modelcontextprotocol/server-filesystem",
                team_app_studio_dir,
            ],
        ),
        tool_filter=["read_text_file", "write_file", "create_directory", "list_directory"]
    )]

def get_ux_ui_manager_tools() -> list:
    """Get tools for the UX/UI designer - INCLUDES all sub-agents as tools in proper sequence."""

    # Import all sub-agents
    from .sub_agents.features.agent import feature_generator, routing_manager
    from .sub_agents.navigation_generator.agent import navigation_generator
    from .sub_agents.form_generator.agent import form_generator
    from .sub_agents.home_routing_generator.agent import home_routing_generator
    from .sub_agents.notification_generator.agent import notification_generator
    from .sub_agents.ai_model_selector.agent import ai_model_selector
    from .sub_agents.screens.agent import screens_specs_generator, model_updater, patches_generator, models_consolidator
    from .sub_agents.features.tools import generate_features_config
    from .sub_agents.screens.tools import generate_screen_specs_from_routing, move_files_based_on_type, extract_requests, update_screen_specs_from_models

    agents =[
        AgentTool(navigation_generator),
        AgentTool(form_generator),
        AgentTool(home_routing_generator),
        AgentTool(notification_generator),
        AgentTool(ai_model_selector),
        AgentTool(feature_generator),
        AgentTool(routing_manager),
        AgentTool(model_updater),
        AgentTool(patches_generator),
        AgentTool(models_consolidator),
        AgentTool(screens_specs_generator),
    ]
    model_context_tools = [
        *file_system_toolset(),
    ]
    tools = [
        FunctionTool(func=get_paths),
        FunctionTool(func=move_files_based_on_type),
        FunctionTool(func=extract_requests),
        FunctionTool(func=update_screen_specs_from_models),
        FunctionTool(func=generate_features_config),
        FunctionTool(func=generate_screen_specs_from_routing),
    ]
    return [
        *agents,
        *tools,
        *model_context_tools,
    ]

__all__ = [
    "get_ux_ui_manager_tools"
]

print(get_paths())