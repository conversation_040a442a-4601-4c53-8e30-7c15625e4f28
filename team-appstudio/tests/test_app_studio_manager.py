"""
Integration Tests for App Studio Manager
========================================

Tests the complete workflow orchestration of the app studio manager.
"""

import unittest
import asyncio
from unittest.mock import patch, MagicMock, AsyncMock
from team_app_studio.agent import app_studio_manager

class TestAppStudioManager(unittest.TestCase):
    """Test cases for the App Studio Manager orchestration."""

    def setUp(self):
        """Set up test environment."""
        self.test_prompt = "Build me a simple blog application with user authentication and commenting"

    def test_agent_creation(self):
        """Test that the app studio manager agent is created correctly."""
        self.assertEqual(app_studio_manager.name, "app_studio_manager")
        self.assertEqual(app_studio_manager.model, "gemini-2.0-flash")
        self.assertIn("Full-stack application generation coordinator", app_studio_manager.description)

        # Verify tools are loaded
        tools = app_studio_manager.tools
        self.assertIsNotNone(tools)
        self.assertGreater(len(tools), 0)

    @patch('team_app_studio.sub_agents.product_manager.tools.run_project_specs_generation')
    @patch('team_app_studio.sub_agents.ux_ui_manager.tools.run_feature_specs')
    @patch('team_app_studio.sub_agents.ux_ui_manager.tools.run_screen_specs')
    @patch('team_app_studio.sub_agents.ux_ui_manager.tools.run_navigation_specs')
    @patch('team_app_studio.sub_agents.ux_ui_manager.tools.run_form_specs')
    @patch('team_app_studio.sub_agents.backend_developer.tools.generate_server_code')
    @patch('team_app_studio.sub_agents.frontend_developer.tools.generate_client_code')
    def test_workflow_orchestration_mocked(self,
                                         mock_client_code,
                                         mock_server_code,
                                         mock_form_specs,
                                         mock_nav_specs,
                                         mock_screen_specs,
                                         mock_feature_specs,
                                         mock_project_specs):
        """Test the complete workflow with mocked sub-agent calls."""

        # Mock all sub-agent tool responses
        mock_project_specs.return_value = "Successfully generated project specifications at 'specs/specifications.json'."
        mock_feature_specs.return_value = "Successfully generated feature specifications."
        mock_screen_specs.return_value = "Successfully generated screen specifications."
        mock_nav_specs.return_value = "Successfully generated navigation specifications."
        mock_form_specs.return_value = "Successfully generated form specifications."
        mock_server_code.return_value = "Successfully generated all server-side code."
        mock_client_code.return_value = "Successfully generated all React client-side code."

        # Test that the agent has the correct instruction
        instruction = app_studio_manager.instruction
        self.assertIn("App Studio Manager", instruction)
        self.assertIn("Step 1: Define the Product", instruction)
        self.assertIn("Step 2: Design the UX and UI", instruction)
        self.assertIn("Step 3: Develop the Backend", instruction)
        self.assertIn("Step 4: Develop the Frontend", instruction)
        self.assertIn("Step 5: Final Report", instruction)

    def test_agent_tools_availability(self):
        """Test that all required agent tools are available."""
        tools = app_studio_manager.tools

        # We should have 5 agent tools (one for each sub-agent)
        self.assertEqual(len(tools), 5)

        # Verify tool types
        from google.adk.tools.agent_tool import AgentTool
        for tool in tools:
            self.assertIsInstance(tool, AgentTool)

    def test_prompt_structure(self):
        """Test that the orchestration prompt has the correct structure."""
        instruction = app_studio_manager.instruction

        # Check for critical workflow elements
        self.assertIn("CRITICAL: You MUST call the agents in this exact order", instruction)
        self.assertIn("product_manager", instruction)
        self.assertIn("ux_ui_manager", instruction)
        self.assertIn("backend_developer", instruction)
        self.assertIn("frontend_developer", instruction)

        # Check for sequential steps
        self.assertIn("Step 1", instruction)
        self.assertIn("Step 2", instruction)
        self.assertIn("Step 3", instruction)
        self.assertIn("Step 4", instruction)
        self.assertIn("Step 5", instruction)

class TestAppStudioManagerAsync(unittest.IsolatedAsyncioTestCase):
    """Async test cases for the App Studio Manager."""

    async def test_main_function_structure(self):
        """Test the main function structure without actually running it."""
        # Import the main function
        from team_app_studio.agent import main

        # Verify it's a coroutine function
        self.assertTrue(asyncio.iscoroutinefunction(main))

if __name__ == '__main__':
    unittest.main()
