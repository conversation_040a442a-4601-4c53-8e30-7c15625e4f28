"""
Unit Tests for UX/UI Designer Agent
===================================

Tests the UX/UI designer agent tools and subprocess calls.
"""

import unittest
from unittest.mock import patch, MagicMock
from team_app_studio.sub_agents.ux_ui_manager.tools import (
    run_feature_specs,
    run_screen_specs,
    run_navigation_specs,
    run_form_specs
)

class TestUXUIDesigner(unittest.TestCase):
    """Test cases for the UX/UI Designer agent."""

    @patch('subprocess.run')
    def test_run_feature_specs_success(self, mock_subprocess):
        """Test successful feature specs generation."""
        mock_result = MagicMock()
        mock_result.stdout = "Feature specs generated"
        mock_result.stderr = ""
        mock_subprocess.return_value = mock_result

        result = run_feature_specs()

        self.assertIn("Successfully generated feature specifications", result)
        mock_subprocess.assert_called_once()

    @patch('subprocess.run')
    def test_run_screen_specs_success(self, mock_subprocess):
        """Test successful screen specs generation."""
        mock_result = MagicMock()
        mock_result.stdout = "Screen specs generated"
        mock_result.stderr = ""
        mock_subprocess.return_value = mock_result

        result = run_screen_specs()

        self.assertIn("Successfully generated screen specifications", result)
        mock_subprocess.assert_called_once()

    @patch('subprocess.run')
    def test_run_navigation_specs_success(self, mock_subprocess):
        """Test successful navigation specs generation."""
        mock_result = MagicMock()
        mock_result.stdout = "Navigation specs generated"
        mock_result.stderr = ""
        mock_subprocess.return_value = mock_result

        result = run_navigation_specs()

        self.assertIn("Successfully generated navigation specifications", result)
        mock_subprocess.assert_called_once()

    @patch('subprocess.run')
    def test_run_form_specs_success(self, mock_subprocess):
        """Test successful form specs generation."""
        mock_result = MagicMock()
        mock_result.stdout = "Form specs generated"
        mock_result.stderr = ""
        mock_subprocess.return_value = mock_result

        result = run_form_specs()

        self.assertIn("Successfully generated form specifications", result)
        mock_subprocess.assert_called_once()

    @patch('subprocess.run')
    def test_run_feature_specs_error(self, mock_subprocess):
        """Test handling of subprocess errors in feature specs."""
        from subprocess import CalledProcessError
        mock_subprocess.side_effect = CalledProcessError(1, "node", stderr="Script error")

        result = run_feature_specs()

        self.assertIn("Error executing featureSpecs.js", result)

if __name__ == '__main__':
    unittest.main()
