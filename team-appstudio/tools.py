"""
App Studio Manager Tools
========================
"""

from typing import List, Union
from google.adk.tools.agent_tool import AgentTool
from google.adk.tools.base_tool import BaseTool
from google.adk.tools.function_tool import FunctionTool

def get_app_studio_manager_tools() -> List[Union[BaseTool, AgentTool, FunctionTool]]:
    """
    Get tools for app studio manager agent.

    Returns:
        List of tools including agent tools for all sub-agents
    """
    from .sub_agents import (
        product_manager,
        ux_ui_manager,
        screen_designer,
        frontend_developer,
        backend_developer,
        document_manager
    )

    # Create agent tools
    product_manager_tool = AgentTool(product_manager)
    ux_ui_manager_tool = AgentTool(ux_ui_manager)
    screen_designer_tool = AgentTool(screen_designer)
    frontend_developer_tool = AgentTool(frontend_developer)
    backend_developer_tool = AgentTool(backend_developer)
    document_manager_tool = AgentTool(document_manager)

    return [
        product_manager_tool,
        ux_ui_manager_tool,
        screen_designer_tool,
        frontend_developer_tool,
        backend_developer_tool,
        document_manager_tool
    ]

# Export tools
__all__ = [
    "get_app_studio_manager_tools"
]
